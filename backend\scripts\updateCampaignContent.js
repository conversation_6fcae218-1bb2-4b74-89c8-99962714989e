import mongoose from 'mongoose';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { Campaign } from '../models/index.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Update existing campaign's generatedContent with test data
 * This script will:
 * 1. Connect to the database
 * 2. Find the existing campaign by ID
 * 3. Delete existing generatedContent
 * 4. Add new content from test-content-data.json
 * 5. Update contentCalendar accordingly
 */
const updateCampaignContent = async () => {
  try {
    console.log('🚀 Starting campaign content update...');
    
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    });
    console.log('✅ Connected to MongoDB');

    // Load test content data
    const testDataPath = path.join(__dirname, '../../test-content-data.json');
    if (!fs.existsSync(testDataPath)) {
      throw new Error('❌ test-content-data.json not found. Please ensure the file exists in the project root.');
    }

    const testData = JSON.parse(fs.readFileSync(testDataPath, 'utf8'));
    console.log(`📊 Loaded test data with ${testData.data.length} content items`);

    // Find the existing campaign by ID
    const campaignId = '6889051f7efa21d6c7dfabe7'; // The campaign ID from your screenshot
    const existingCampaign = await Campaign.findById(campaignId);
    
    if (!existingCampaign) {
      throw new Error(`❌ Campaign with ID ${campaignId} not found.`);
    }
    
    console.log(`✅ Found existing campaign: ${existingCampaign.name}`);
    console.log(`📋 Current generatedContent items: ${existingCampaign.generatedContent?.length || 0}`);

    // Clear existing content
    console.log('🗑️  Clearing existing generatedContent...');
    
    // Update campaign with new content from test data
    const newGeneratedContent = testData.data.map(item => ({
      date: new Date(item.date || Date.now()),
      channel: item.channel,
      contentType: item.contentType,
      topic: item.topic,
      hashtags: item.hashtags || [],
      
      // Content fields based on type
      title: item.title,
      subheading: item.subheading,
      content: item.content,
      
      // Article fields
      sections: item.sections,
      summary: item.summary,
      
      // Poll fields
      question: item.question,
      options: item.options,
      expirationDate: item.expirationDate ? new Date(item.expirationDate) : undefined,
      
      // Email fields
      subject: item.subject,
      body: item.body,
      preheader: item.preheader,
      emailSections: item.sections, // Map sections to emailSections for email content
      footer: item.footer,
      
      // Video fields
      desc: item.desc,
      videoScript: item.videoScript,
      
      // Thread fields
      thread: item.thread,
      
      // Press Release fields
      headline: item.headline,
      subheadline: item.subheadline,
      dateline: item.dateline,
      lead_paragraph: item.lead_paragraph,
      body_sections: item.body_sections,
      boilerplate: item.boilerplate,
      contact_info: item.contact_info,
      
      // Carousel fields
      slides: item.slides,
      call_to_action: item.call_to_action,
      
      status: 'generated',
      generatedAt: new Date()
    }));

    // Update contentCalendar to match new content
    const newContentCalendar = testData.data.map(item => ({
      date: new Date(item.date || Date.now()),
      channel: item.channel,
      contentType: item.contentType,
      topic: item.topic,
      hashtags: item.hashtags || [],
      status: 'created'
    }));

    // Update the campaign
    const updatedCampaign = await Campaign.findByIdAndUpdate(
      campaignId,
      {
        $set: {
          generatedContent: newGeneratedContent,
          contentCalendar: newContentCalendar,
          numberOfPosts: newGeneratedContent.length,
          updatedAt: new Date()
        }
      },
      { new: true }
    );

    console.log('✅ Campaign updated successfully!');
    console.log(`📊 New generatedContent items: ${updatedCampaign.generatedContent.length}`);
    console.log(`📅 New contentCalendar items: ${updatedCampaign.contentCalendar.length}`);

    // Display summary of content types
    console.log('\n📝 Content Types Added:');
    const contentTypes = {};
    updatedCampaign.generatedContent.forEach(item => {
      contentTypes[item.contentType] = (contentTypes[item.contentType] || 0) + 1;
    });
    
    Object.entries(contentTypes).forEach(([type, count]) => {
      console.log(`• ${type}: ${count} items`);
    });

    console.log('\n🎯 Next Steps:');
    console.log('1. Login to the <NAME_EMAIL> (password: 123)');
    console.log('2. Navigate to the Campaigns page');
    console.log(`3. Find the "${updatedCampaign.name}" campaign`);
    console.log('4. Click on any content item to test the PostDetail component');
    console.log('5. Verify all 9 content types render correctly');
    console.log('6. Test edit functionality for each content type');

    console.log('\n✅ Campaign content update completed successfully!');

  } catch (error) {
    console.error('❌ Campaign update failed:', error.message);
    throw error;
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run update if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  updateCampaignContent()
    .then(() => {
      console.log('🎉 Update script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Update script failed:', error);
      process.exit(1);
    });
}

export default updateCampaignContent;
