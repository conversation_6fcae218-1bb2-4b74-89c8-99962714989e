import React, { useEffect } from 'react'
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react'
import { cn } from '@/utils/cn'
import { useToastStore, Toast as ToastType } from '@/stores/toastStore'

interface ToastProps {
  toast: ToastType
}

const Toast: React.FC<ToastProps> = ({ toast }) => {
  const { removeToast } = useToastStore()

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-success-500" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-error-500" />
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-warning-500" />
      case 'info':
        return <Info className="w-5 h-5 text-brand-500" />
      default:
        return <Info className="w-5 h-5 text-brand-500" />
    }
  }

  const getBackgroundColor = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-success-500/10 border-success-500/20'
      case 'error':
        return 'bg-error-500/10 border-error-500/20'
      case 'warning':
        return 'bg-warning-500/10 border-warning-500/20'
      case 'info':
        return 'bg-brand-500/10 border-brand-500/20'
      default:
        return 'bg-brand-500/10 border-brand-500/20'
    }
  }

  return (
    <div
      className={cn(
        'flex items-start gap-3 p-4 rounded-lg border backdrop-blur-sm shadow-lg max-w-md w-full',
        'animate-in slide-in-from-right-full duration-300',
        getBackgroundColor()
      )}
    >
      {/* Icon */}
      <div className="flex-shrink-0 mt-0.5">
        {getIcon()}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="font-semibold text-text-primary text-sm">
          {toast.title}
        </div>
        {toast.message && (
          <div className="text-text-secondary text-sm mt-1">
            {toast.message}
          </div>
        )}
        
        {/* Action Button */}
        {toast.action && (
          <button
            onClick={toast.action.onClick}
            className="mt-2 text-sm font-medium text-brand-500 hover:text-brand-400 transition-colors duration-200"
          >
            {toast.action.label}
          </button>
        )}
      </div>

      {/* Close Button */}
      <button
        onClick={() => removeToast(toast.id)}
        className="flex-shrink-0 text-text-tertiary hover:text-text-primary transition-colors duration-200"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  )
}

export default Toast
