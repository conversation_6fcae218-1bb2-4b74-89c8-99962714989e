{"name": "lumyn-backend", "version": "1.0.0", "description": "Backend API for Lumyn Marketing AI Platform", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "init-db": "node scripts/initDatabase.js", "reset-db": "node scripts/resetDatabase.js", "seed-neuralogix": "node scripts/seedNeuraLogixCampaign.js", "deploy-check": "node deploy-check.js"}, "dependencies": {"axios": "^1.5.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "form-data": "^4.0.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.14", "jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["marketing", "ai", "api", "express", "mongodb"], "author": "Lumyn Team", "license": "MIT"}