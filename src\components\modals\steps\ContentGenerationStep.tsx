import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui'
import { ArrowLeft, CheckCircle, AlertCircle, Sparkles, FileText, Video, MessageSquare, Mail, Hash, BarChart3, Newspaper } from 'lucide-react'
import { contentTypeMeta } from '@/icons/ChannelIcons'
import { cn } from '@/utils/cn'
import { useBrandStore } from '@/stores/brandStore'
import aiService, { type CampaignContentGenerationRequest, type GeneratedContent } from '@/services/aiService'

interface ContentGenerationStepProps {
  campaignData: {
    name: string;
    startDate: string;
    endDate: string;
    purpose: string;
    customPurpose: string;
    destinationUrl: string;
    instructions: string;
    selectedProducts: string[];
    selectedICPs: string[];
    selectedChannels: string[];
    numberOfPosts: string;
    researchInsights: any;
    campaignStrategy: any;
    contentCalendar: any;
  };
  onCampaignCreated: (campaignId: string) => void;
  onBack: () => void;
}

const ContentGenerationStep: React.FC<ContentGenerationStepProps> = ({ 
  campaignData, 
  onCampaignCreated, 
  onBack 
}) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([])
  const [campaignId, setCampaignId] = useState<string | null>(null)

  const { brand, activeBrand } = useBrandStore()

  // Use active brand if available, otherwise fall back to brand (same as BrandProfile.tsx)
  const currentBrand = activeBrand || brand;

  const generateContent = async () => {
    if (!currentBrand) {
      setError('Brand information is required')
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Build the content generation request
      const request: CampaignContentGenerationRequest = {
        brandId: currentBrand._id,
        campaignData: {
          name: campaignData.name,
          startDate: campaignData.startDate,
          endDate: campaignData.endDate,
          purpose: campaignData.purpose,
          customPurpose: campaignData.customPurpose,
          destinationUrl: campaignData.destinationUrl,
          instructions: campaignData.instructions,
          selectedProducts: campaignData.selectedProducts,
          selectedICPs: campaignData.selectedICPs,
          selectedChannels: campaignData.selectedChannels,
          numberOfPosts: campaignData.numberOfPosts,
          campaignFiles: campaignData.campaignFiles || [],
          selectedBrandFiles: campaignData.selectedBrandFiles || []
        },
        researchInsights: campaignData.researchInsights,
        campaignStrategy: campaignData.campaignStrategy,
        contentCalendar: campaignData.contentCalendar.contentCalendar || []
      }

      console.log('Generating campaign content...', request)

      const response = await aiService.generateCampaignContent(request)
      
      setGeneratedContent(response.generatedContent)
      setCampaignId(response.campaignId)
      setSuccess(true)
      setLoading(false)

      console.log('✅ Campaign content generated successfully:', response)

    } catch (error: any) {
      console.error('Error generating campaign content:', error)
      setError(error.message || 'Failed to generate campaign content')
      setLoading(false)
    }
  }

  const getContentTypeIcon = (contentType: string) => {
    // Use contentTypeMeta for consistent icon mapping
    const meta = contentTypeMeta[contentType] || contentTypeMeta[contentType.toLowerCase()]
    if (meta) return meta.Icon

    // Fallback for legacy types
    const type = contentType.toLowerCase()
    if (type.includes('article') || type.includes('blog')) return FileText
    if (type.includes('video')) return Video
    if (type.includes('thread') || type.includes('post')) return MessageSquare
    if (type.includes('email')) return Mail
    if (type.includes('poll')) return BarChart3
    if (type.includes('press')) return Newspaper
    return Hash
  }

  const getContentTypeColor = (contentType: string) => {
    // Use contentTypeMeta for consistent color mapping
    const meta = contentTypeMeta[contentType] || contentTypeMeta[contentType.toLowerCase()]
    if (meta) {
      // Convert bg class to appropriate opacity version
      const bgClass = meta.bg.replace('bg-', 'bg-').replace('-600', '-500/10').replace('-500', '-500/10')
      const textClass = meta.bg.replace('bg-', 'text-').replace('-600', '-400').replace('-500', '-400')
      return `${textClass} ${bgClass}`
    }

    // Fallback for channels
    const colors = {
      LinkedIn: "text-blue-400 bg-blue-500/10",
      Twitter: "text-gray-300 bg-gray-500/10",
      Instagram: "text-pink-400 bg-pink-500/10",
      Facebook: "text-blue-400 bg-blue-500/10",
      YouTube: "text-red-400 bg-red-500/10",
      Blog: "text-green-400 bg-green-500/10"
    }
    return colors[contentType as keyof typeof colors] || "text-gray-400 bg-gray-500/10"
  }

  const renderContentPreview = (content: GeneratedContent) => {
    const IconComponent = getContentTypeIcon(content.contentType)
    
    return (
      <div key={`${content.date}-${content.channel}-${content.contentType}`} className="border border-dark-quaternary rounded-lg p-4 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn("p-2 rounded-lg", getContentTypeColor(content.contentType))}>
              <IconComponent className="w-4 h-4" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-text-primary">{content.topic}</h4>
              <p className="text-xs text-text-tertiary">{content.channel} • {content.contentType}</p>
            </div>
          </div>
          <span className="text-xs text-text-quaternary">
            {new Date(content.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
          </span>
        </div>

        {/* Content preview based on type */}
        <div className="space-y-2">
          {/* Post Content */}
          {content.contentType === 'Post' && (
            <>
              {content.title && (
                <h5 className="text-sm font-medium text-text-primary">{content.title}</h5>
              )}
              {content.content && (
                <p className="text-sm text-text-secondary line-clamp-3">{content.content}</p>
              )}
            </>
          )}

          {/* Article Content */}
          {content.contentType === 'Article' && (
            <>
              {content.title && (
                <h5 className="text-sm font-medium text-text-primary">{content.title}</h5>
              )}
              {content.subheading && (
                <p className="text-xs text-text-tertiary italic">{content.subheading}</p>
              )}
              {content.sections && content.sections.length > 0 && (
                <p className="text-sm text-text-secondary line-clamp-2">
                  {content.sections[0]?.Introduction || content.sections[0]?.Description}
                </p>
              )}
              {content.summary && (
                <p className="text-xs text-text-quaternary">Summary: {content.summary}</p>
              )}
            </>
          )}

          {/* Poll Content */}
          {content.contentType === 'Poll' && content.question && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-text-primary">{content.question}</p>
              {content.options && (
                <div className="space-y-1">
                  {content.options.map((option, idx) => (
                    <div key={idx} className="text-xs text-text-tertiary">• {option.option}</div>
                  ))}
                </div>
              )}
              {content.expirationDate && (
                <p className="text-xs text-text-quaternary">
                  Expires: {new Date(content.expirationDate).toLocaleDateString()}
                </p>
              )}
            </div>
          )}

          {/* Email Content */}
          {content.contentType === 'Email' && (
            <div className="space-y-1">
              {content.subject && (
                <p className="text-sm font-medium text-text-primary">Subject: {content.subject}</p>
              )}
              {content.preheader && (
                <p className="text-xs text-text-tertiary">Preheader: {content.preheader}</p>
              )}
              {content.body && (
                <p className="text-sm text-text-secondary line-clamp-2">{content.body}</p>
              )}
              {content.sections && content.sections.length > 0 && (
                <p className="text-xs text-text-quaternary">
                  {content.sections.length} section{content.sections.length !== 1 ? 's' : ''}
                </p>
              )}
            </div>
          )}

          {/* Video Content */}
          {content.contentType === 'Video' && (
            <div className="space-y-1">
              {content.title && (
                <h5 className="text-sm font-medium text-text-primary">{content.title}</h5>
              )}
              {content.desc && (
                <p className="text-sm text-text-secondary line-clamp-2">{content.desc}</p>
              )}
              {content.videoScript && (
                <p className="text-xs text-text-tertiary">
                  Script: {content.videoScript.intro?.title || 'Video script available'}
                </p>
              )}
            </div>
          )}

          {/* Thread Content */}
          {content.contentType === 'Thread' && content.thread && (
            <div className="space-y-1">
              <p className="text-xs text-text-tertiary">Thread ({content.thread.length} posts)</p>
              <p className="text-sm text-text-secondary line-clamp-2">{content.thread[0]?.content}</p>
              {content.thread.length > 1 && (
                <p className="text-xs text-text-quaternary">+{content.thread.length - 1} more posts</p>
              )}
            </div>
          )}

          {/* Press Release Content */}
          {content.contentType === 'PressRelease' && (
            <div className="space-y-1">
              {content.headline && (
                <h5 className="text-sm font-medium text-text-primary">{content.headline}</h5>
              )}
              {content.subheadline && (
                <p className="text-xs text-text-tertiary italic">{content.subheadline}</p>
              )}
              {content.lead_paragraph && (
                <p className="text-sm text-text-secondary line-clamp-2">{content.lead_paragraph}</p>
              )}
              {content.body_sections && content.body_sections.length > 0 && (
                <p className="text-xs text-text-quaternary">
                  {content.body_sections.length} section{content.body_sections.length !== 1 ? 's' : ''}
                </p>
              )}
            </div>
          )}

          {/* Carousel Content */}
          {content.contentType === 'Carousel' && (
            <div className="space-y-1">
              {content.title && (
                <h5 className="text-sm font-medium text-text-primary">{content.title}</h5>
              )}
              {content.slides && content.slides.length > 0 && (
                <>
                  <p className="text-sm text-text-secondary line-clamp-2">
                    {content.slides[0]?.content || content.slides[0]?.title}
                  </p>
                  <p className="text-xs text-text-quaternary">
                    {content.slides.length} slide{content.slides.length !== 1 ? 's' : ''}
                  </p>
                </>
              )}
              {content.call_to_action && (
                <p className="text-xs text-text-quaternary">CTA: {content.call_to_action}</p>
              )}
            </div>
          )}

          {/* Legacy fallback for old content types */}
          {!['Post', 'Article', 'Poll', 'Email', 'Video', 'Thread', 'PressRelease', 'Carousel'].includes(content.contentType) && (
            <>
              {content.title && (
                <h5 className="text-sm font-medium text-text-primary">{content.title}</h5>
              )}
              {content.content && (
                <p className="text-sm text-text-secondary line-clamp-3">{content.content}</p>
              )}
            </>
          )}
        </div>

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {content.hashtags.slice(0, 3).map((tag, idx) => (
              <span key={idx} className="text-xs px-2 py-1 bg-brand-500/10 text-brand-400 rounded">
                {tag.startsWith('#') ? tag : `#${tag}`}
              </span>
            ))}
            {content.hashtags.length > 3 && (
              <span className="text-xs text-text-quaternary">+{content.hashtags.length - 3} more</span>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-text-secondary hover:text-text-primary"
            disabled={loading}
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <h1 className="text-xl font-semibold text-text-primary">Generate Content</h1>
            <p className="text-sm text-text-tertiary">Create your marketing content with AI</p>
          </div>
        </div>
      </div>

      <div className="card p-8">
        {!success && !loading && !error && (
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-brand-500/10 rounded-full flex items-center justify-center mx-auto">
              <Sparkles className="w-8 h-8 text-brand-500" />
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-text-primary">Ready to Generate Content</h3>
              <p className="text-text-secondary max-w-md mx-auto">
                Your campaign strategy and content calendar are ready. Click below to generate your marketing content using AI.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 max-w-md mx-auto text-sm">
              <div className="text-center">
                <div className="text-lg font-semibold text-brand-400">
                  {campaignData.contentCalendar?.contentCalendar?.length || 0}
                </div>
                <div className="text-text-tertiary">Content Items</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-brand-400">
                  {campaignData.selectedChannels?.length || 0}
                </div>
                <div className="text-text-tertiary">Channels</div>
              </div>
            </div>

            <Button
              onClick={generateContent}
              className="bg-brand-500 hover:bg-brand-600 text-white px-8 py-3"
              size="lg"
            >
              <Sparkles className="w-5 h-5 mr-2" />
              Generate Content
            </Button>
          </div>
        )}

        {loading && (
          <div className="text-center space-y-6 py-16">
            <div className="w-16 h-16 border-4 border-brand-500 border-t-transparent rounded-full animate-spin mx-auto" />
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-text-primary">Generating Your Content</h3>
              <p className="text-text-secondary">
                Our AI is creating personalized marketing content for your campaign...
              </p>
              <p className="text-xs text-text-quaternary">This may take a few minutes</p>
            </div>
          </div>
        )}

        {error && (
          <div className="text-center space-y-6 py-16">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto" />
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-text-primary">Content Generation Failed</h3>
              <p className="text-text-secondary max-w-md mx-auto">{error}</p>
            </div>
            <div className="flex gap-3 justify-center">
              <Button variant="secondary" onClick={() => setError(null)}>
                Try Again
              </Button>
              <Button variant="ghost" onClick={onBack}>
                Go Back
              </Button>
            </div>
          </div>
        )}

        {success && (
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-text-primary">Content Generated Successfully!</h3>
                <p className="text-text-secondary">
                  Your campaign has been created with {generatedContent.length} pieces of content.
                </p>
              </div>
            </div>

            {/* Generated Content Preview */}
            <div className="space-y-4">
              <h4 className="text-md font-semibold text-text-primary">Generated Content Preview</h4>
              <div className="grid gap-4 max-h-96 overflow-y-auto">
                {generatedContent.map(renderContentPreview)}
              </div>
            </div>

            <div className="flex justify-center">
              <Button
                onClick={() => campaignId && onCampaignCreated(campaignId)}
                className="bg-brand-500 hover:bg-brand-600 text-white px-8 py-3"
                size="lg"
              >
                View Campaign
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ContentGenerationStep