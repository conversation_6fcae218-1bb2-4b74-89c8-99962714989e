import React, { useState } from 'react'
import { getAvatarColor, getInitials } from '@/utils/avatarUtils'

export { default as Button } from './Button'
export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card'
export { default as Input } from './Input'
export { default as Status } from './Status'
export type { CampaignStatus, ContentStatus } from './Status'

// Simple Avatar component
export const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = useState(false)

  if (!src || imageError) {
    return (
      <div className={`flex items-center justify-center text-white font-semibold rounded-full ${getAvatarColor(name)} ${className || ''}`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`rounded-full object-cover ${className || ''}`}
      onError={() => setImageError(true)}
    />
  )
}