import React from 'react'
import { X, Plus, FileText, User, Mail, Phone } from 'lucide-react'
import Button from '@/components/ui/Button'

interface BodySection {
  heading: string
  content: string
}

interface ContactInfo {
  name?: string
  email?: string
  phone?: string
}

interface PressReleaseContent {
  headline: string
  subheadline?: string
  dateline: string
  lead_paragraph: string
  body_sections: BodySection[]
  boilerplate: string
  contact_info: ContactInfo
  hashtags?: string[]
}

interface PressReleaseEditProps {
  content: PressReleaseContent
  editContent: PressReleaseContent
  setEditContent: (content: PressReleaseContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const PressReleaseEdit: React.FC<PressReleaseEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof PressReleaseContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateBodySection = (index: number, field: keyof BodySection, value: string) => {
    const updatedSections = [...(editContent.body_sections || [])]
    updatedSections[index] = { ...updatedSections[index], [field]: value }
    updateField('body_sections', updatedSections)
  }

  const addBodySection = () => {
    const newSection: BodySection = { heading: '', content: '' }
    updateField('body_sections', [...(editContent.body_sections || []), newSection])
  }

  const removeBodySection = (index: number) => {
    const updatedSections = (editContent.body_sections || []).filter((_, i) => i !== index)
    updateField('body_sections', updatedSections)
  }

  const updateContactInfo = (field: keyof ContactInfo, value: string) => {
    const updatedContact = { ...editContent.contact_info, [field]: value }
    updateField('contact_info', updatedContact)
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  return (
    <div className="space-y-6">
      {/* Headline */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">
          <FileText className="w-4 h-4 inline mr-2" />
          Headline
        </label>
        <input
          type="text"
          value={editContent.headline || ''}
          onChange={(e) => updateField('headline', e.target.value)}
          className="w-full search-input"
          placeholder="Enter press release headline..."
        />
      </div>

      {/* Subheadline */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Subheadline (Optional)</label>
        <input
          type="text"
          value={editContent.subheadline || ''}
          onChange={(e) => updateField('subheadline', e.target.value)}
          className="w-full search-input"
          placeholder="Enter subheadline..."
        />
      </div>

      {/* Dateline */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Dateline</label>
        <input
          type="text"
          value={editContent.dateline || ''}
          onChange={(e) => updateField('dateline', e.target.value)}
          className="w-full search-input"
          placeholder="City, State - Month Day, Year"
        />
      </div>

      {/* Lead Paragraph */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Lead Paragraph</label>
        <textarea
          value={editContent.lead_paragraph || ''}
          onChange={(e) => updateField('lead_paragraph', e.target.value)}
          rows={4}
          className="w-full search-input resize-none"
          placeholder="Write the opening paragraph that summarizes the key news..."
        />
      </div>

      {/* Body Sections */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Body Sections</label>
          <Button
            onClick={addBodySection}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Section
          </Button>
        </div>
        
        <div className="space-y-4">
          {(editContent.body_sections || []).map((section, index) => (
            <div key={index} className="p-4 bg-dark-secondary rounded-lg border border-dark-quaternary">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-text-secondary">Section {index + 1}</span>
                <button
                  onClick={() => removeBodySection(index)}
                  className="text-red-400 hover:text-red-300 p-1"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-3">
                <input
                  type="text"
                  value={section.heading}
                  onChange={(e) => updateBodySection(index, 'heading', e.target.value)}
                  className="w-full search-input"
                  placeholder="Section heading..."
                />
                <textarea
                  value={section.content}
                  onChange={(e) => updateBodySection(index, 'content', e.target.value)}
                  rows={4}
                  className="w-full search-input resize-none"
                  placeholder="Section content..."
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Boilerplate */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Company Boilerplate</label>
        <textarea
          value={editContent.boilerplate || ''}
          onChange={(e) => updateField('boilerplate', e.target.value)}
          rows={4}
          className="w-full search-input resize-none"
          placeholder="Standard company description and background information..."
        />
      </div>

      {/* Contact Information */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Media Contact Information</label>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-text-tertiary" />
            <input
              type="text"
              value={editContent.contact_info?.name || ''}
              onChange={(e) => updateContactInfo('name', e.target.value)}
              className="flex-1 search-input"
              placeholder="Contact name"
            />
          </div>
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4 text-text-tertiary" />
            <input
              type="email"
              value={editContent.contact_info?.email || ''}
              onChange={(e) => updateContactInfo('email', e.target.value)}
              className="flex-1 search-input"
              placeholder="<EMAIL>"
            />
          </div>
          <div className="flex items-center gap-2">
            <Phone className="w-4 h-4 text-text-tertiary" />
            <input
              type="tel"
              value={editContent.contact_info?.phone || ''}
              onChange={(e) => updateContactInfo('phone', e.target.value)}
              className="flex-1 search-input"
              placeholder="+****************"
            />
          </div>
        </div>
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 search-input"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default PressReleaseEdit
