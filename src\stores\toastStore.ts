import { create } from 'zustand'

export interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number // in milliseconds, 0 means persistent
  action?: {
    label: string
    onClick: () => void
  }
  createdAt: number
}

interface ToastState {
  toasts: Toast[]
  
  // Actions
  addToast: (toast: Omit<Toast, 'id' | 'createdAt'>) => string
  removeToast: (id: string) => void
  clearAllToasts: () => void
  updateToast: (id: string, updates: Partial<Toast>) => void
}

export const useToastStore = create<ToastState>((set, get) => ({
  toasts: [],

  addToast: (toastData) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const toast: Toast = {
      ...toastData,
      id,
      createdAt: Date.now(),
      duration: toastData.duration ?? 5000 // Default 5 seconds
    }

    set((state) => ({
      toasts: [...state.toasts, toast]
    }))

    // Auto-remove toast after duration (if not persistent)
    if (toast.duration && toast.duration > 0) {
      setTimeout(() => {
        get().removeToast(id)
      }, toast.duration)
    }

    return id
  },

  removeToast: (id) => {
    set((state) => ({
      toasts: state.toasts.filter((toast) => toast.id !== id)
    }))
  },

  clearAllToasts: () => {
    set({ toasts: [] })
  },

  updateToast: (id, updates) => {
    set((state) => ({
      toasts: state.toasts.map((toast) =>
        toast.id === id ? { ...toast, ...updates } : toast
      )
    }))
  }
}))
