import React from 'react'
import { X, Plus } from 'lucide-react'
import Button from '@/components/ui/Button'

interface ArticleSection {
  id?: string
  title?: string
  content?: string
  Introduction?: string
  Description?: string
}

interface ArticleContent {
  title: string
  subheading?: string
  sections?: ArticleSection[]
  summary?: string
  hashtags?: string[]
}

interface ArticleEditProps {
  content: ArticleContent
  editContent: ArticleContent
  setEditContent: (content: ArticleContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const ArticleEdit: React.FC<ArticleEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof ArticleContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateSection = (index: number, field: string, value: string) => {
    const updatedSections = [...(editContent.sections || [])]
    updatedSections[index] = { ...updatedSections[index], [field]: value }
    updateField('sections', updatedSections)
  }

  const addSection = () => {
    const newSection: ArticleSection = {
      id: `section-${Date.now()}`,
      title: '',
      content: ''
    }
    updateField('sections', [...(editContent.sections || []), newSection])
  }

  const removeSection = (index: number) => {
    const updatedSections = (editContent.sections || []).filter((_, i) => i !== index)
    updateField('sections', updatedSections)
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  return (
    <div className="space-y-6">
      {/* Title */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Article Title</label>
        <input
          type="text"
          value={editContent.title || ''}
          onChange={(e) => updateField('title', e.target.value)}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
          placeholder="Enter article title..."
        />
      </div>

      {/* Subheading */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Subheading (Optional)</label>
        <input
          type="text"
          value={editContent.subheading || ''}
          onChange={(e) => updateField('subheading', e.target.value)}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
          placeholder="Enter article subheading..."
        />
      </div>

      {/* Sections */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Article Sections</label>
          <Button
            onClick={addSection}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Section
          </Button>
        </div>
        
        <div className="space-y-4">
          {(editContent.sections || []).map((section, index) => (
            <div key={section.id || index} className="p-4 bg-dark-secondary rounded-lg border border-dark-quaternary">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-text-secondary">Section {index + 1}</span>
                <button
                  onClick={() => removeSection(index)}
                  className="text-red-400 hover:text-red-300 p-1"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-3">
                <input
                  type="text"
                  value={section.title || section.Introduction || ''}
                  onChange={(e) => updateSection(index, section.title !== undefined ? 'title' : 'Introduction', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-primary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                  placeholder="Section title..."
                />
                <textarea
                  value={section.content || section.Description || ''}
                  onChange={(e) => updateSection(index, section.content !== undefined ? 'content' : 'Description', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 bg-dark-primary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500 resize-none"
                  placeholder="Section content..."
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Summary (Optional)</label>
        <textarea
          value={editContent.summary || ''}
          onChange={(e) => updateField('summary', e.target.value)}
          rows={3}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500 resize-none"
          placeholder="Enter article summary..."
        />
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 px-3 py-2 bg-dark-secondary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ArticleEdit
