import { useState } from 'react'

export { default as <PERSON><PERSON> } from './Button'
export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card'
export { default as Input } from './Input'
export { default as Status } from './Status'
export type { CampaignStatus, ContentStatus } from './Status'

// Simple Avatar component
export const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = useState(false)

  const getInitials = (name: string) => {
    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2)
  }

  if (!src || imageError) {
    return (
      <div className={`flex items-center justify-center text-white font-semibold rounded-full bg-brand-500 ${className || ''}`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`rounded-full object-cover ${className || ''}`}
      onError={() => setImageError(true)}
    />
  )
}