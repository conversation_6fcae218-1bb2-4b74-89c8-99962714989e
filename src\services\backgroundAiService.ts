import { api } from '@/stores/authStore'
import { useBackgroundProcessStore } from '@/stores/backgroundProcessStore'
import { useToastStore } from '@/stores/toastStore'

export interface AiRegenerationData {
  postId: string
  campaignId?: string
  contentIndex?: number
  isQuickPost?: boolean
  quickPostId?: string
  instruction: string
  brandData: any
  originalContent: any
}

class BackgroundAiService {
  private static instance: BackgroundAiService
  private activeRequests = new Map<string, AbortController>()

  static getInstance(): BackgroundAiService {
    if (!BackgroundAiService.instance) {
      BackgroundAiService.instance = new BackgroundAiService()
    }
    return BackgroundAiService.instance
  }

  async startAiRegeneration(data: AiRegenerationData): Promise<string> {
    const { startProcess, updateProcess, completeProcess, failProcess } = useBackgroundProcessStore.getState()
    const { addToast } = useToastStore.getState()

    // Start the background process
    const processId = startProcess({
      type: 'ai-regeneration',
      data: {
        postId: data.postId,
        campaignId: data.campaignId,
        contentIndex: data.contentIndex,
        isQuickPost: data.isQuickPost,
        quickPostId: data.quickPostId,
        instruction: data.instruction,
        brandData: data.brandData,
        originalContent: data.originalContent
      }
    })

    // Show initial toast
    addToast({
      type: 'info',
      title: 'Content Regeneration Started',
      message: 'AI is regenerating your content in the background...',
      duration: 3000
    })

    // Create abort controller for this request
    const abortController = new AbortController()
    this.activeRequests.set(processId, abortController)

    // Start the actual regeneration process
    this.performRegeneration(processId, data, abortController.signal)

    return processId
  }

  private async performRegeneration(
    processId: string, 
    data: AiRegenerationData, 
    signal: AbortSignal
  ): Promise<void> {
    const { updateProcess, completeProcess, failProcess } = useBackgroundProcessStore.getState()
    const { addToast } = useToastStore.getState()

    try {
      // Update process status
      updateProcess(processId, { status: 'in-progress' })

      console.log('🔄 Starting background AI regeneration process...')
      console.log('📋 Received regeneration data:', {
        postId: data.postId,
        isQuickPost: data.isQuickPost,
        instruction: data.instruction,
        originalContent: data.originalContent
      })

      // Prepare the API request using the same format as the original implementation
      const brandId = typeof data.brandData === 'string' ? data.brandData : data.brandData?._id || data.brandData?.id

      if (!brandId) {
        throw new Error('Brand ID not found')
      }

      // Determine content type and build content ID
      const contentType = data.isQuickPost ? 'quickpost' : 'campaign'
      let contentId = ''

      if (data.isQuickPost) {
        // For quick posts: quickPostId-contentIndex
        const contentIndex = parseInt(data.quickPostId!.split('-').pop() || '0')
        contentId = `${data.quickPostId!.split('-')[0]}-${contentIndex}`
      } else {
        // For campaigns: campaignId-contentIndex
        contentId = `${data.campaignId}-${data.contentIndex}`
      }

      // Prepare old_gen_content with correct field mapping
      const oldGenContent = {
        topic: data.originalContent.topic || 'Content Topic',
        channel: data.originalContent.channel || 'LinkedIn',
        contentType: data.originalContent.contentType || 'post',
        date: data.originalContent.date || new Date().toISOString(),
        hashtags: data.originalContent.hashtags || [],
        ...data.originalContent // Include all other content fields
      }

      // Prepare regeneration request using the same format as the working implementation
      const requestData = {
        brandId: brandId,
        brandInformation: data.brandData?.brandInformation || {
          name: data.brandData?.name || '',
          about: data.brandData?.about || '',
          story: data.brandData?.story || '',
          location: data.brandData?.location || '',
          website: data.brandData?.website || '',
          size: data.brandData?.size || 'medium',
          type: data.brandData?.type || 'enterprise',
          industry: data.brandData?.industry || '',
          socialMedia: data.brandData?.socialMedia || [],
          contactInfo: data.brandData?.contactInfo || { phone: [], email: [] },
          keyTeamMembers: data.brandData?.keyTeamMembers || []
        },
        brandIdentity: data.brandData?.brandIdentity || {
          brandVoice: data.brandData?.brandVoice || ['Professional'],
          coreMessagingPillars: data.brandData?.coreMessagingPillars || '',
          primaryKeywords: data.brandData?.primaryKeywords || '',
          negativeKeywords: data.brandData?.negativeKeywords || ''
        },
        old_gen_content: oldGenContent,
        additional_instruction: data.instruction,
        selectedBrandFiles: data.brandData?.selectedBrandFiles || [],
        campaignFiles: data.brandData?.campaignFiles || []
      }

      const endpoint = '/ai/regenerate-content'

      console.log('📤 Sending background regeneration request:', requestData)

      // Make the API call
      const response = await api.post(endpoint, requestData, {
        signal,
        timeout: 120000 // 2 minutes timeout
      })

      // Check if request was aborted
      if (signal.aborted) {
        return
      }

      console.log('✅ Background AI regeneration completed:', response.data)
      const result = response.data

      // Complete the process
      completeProcess(processId, result)

      // Show success toast with navigation action
      addToast({
        type: 'success',
        title: 'Content Regeneration Complete!',
        message: 'Your content has been successfully regenerated and saved.',
        duration: 8000,
        action: {
          label: 'View Updated Content',
          onClick: () => this.navigateToUpdatedContent(data)
        }
      })

      // Trigger content refresh if user is still on the same page
      this.triggerContentRefresh(data)

    } catch (error: any) {
      // Check if request was aborted (user cancelled)
      if (signal.aborted) {
        updateProcess(processId, { status: 'completed' })
        return
      }

      console.error('❌ Background AI regeneration failed:', error)
      
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred'
      
      // Fail the process
      failProcess(processId, errorMessage)

      // Show error toast
      addToast({
        type: 'error',
        title: 'Content Regeneration Failed',
        message: errorMessage,
        duration: 8000,
        action: {
          label: 'Try Again',
          onClick: () => this.startAiRegeneration(data)
        }
      })
    } finally {
      // Clean up
      this.activeRequests.delete(processId)
    }
  }

  private navigateToUpdatedContent(data: AiRegenerationData): void {
    // Navigate to the updated content
    if (data.isQuickPost && data.quickPostId) {
      window.location.href = `/quick-posts/${data.quickPostId}`
    } else if (data.campaignId && data.postId) {
      window.location.href = `/campaigns/${data.campaignId}/posts/${data.postId}`
    }
  }

  private triggerContentRefresh(data: AiRegenerationData): void {
    // Dispatch custom event to trigger content refresh if user is on the same page
    const event = new CustomEvent('ai-content-regenerated', {
      detail: {
        postId: data.postId,
        campaignId: data.campaignId,
        isQuickPost: data.isQuickPost,
        quickPostId: data.quickPostId
      }
    })
    window.dispatchEvent(event)
  }

  cancelRegeneration(processId: string): void {
    const abortController = this.activeRequests.get(processId)
    if (abortController) {
      abortController.abort()
      this.activeRequests.delete(processId)
    }

    const { updateProcess } = useBackgroundProcessStore.getState()
    updateProcess(processId, { status: 'completed' })
  }

  getActiveRegenerations(): string[] {
    return Array.from(this.activeRequests.keys())
  }
}

export const backgroundAiService = BackgroundAiService.getInstance()
