import React from 'react'
import { BarChart3 } from 'lucide-react'

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = React.useState(false)

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ]
    const index = name.charCodeAt(0) % colors.length
    return colors[index]
  }

  if (imageError || !src) {
    return (
      <div className={`${className} ${getAvatarColor(name)} rounded-full flex items-center justify-center text-white font-semibold`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`${className} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  )
}

interface PollOption {
  option: string
  votes: number
}

interface PollContent {
  question: string
  options: PollOption[]
  expirationDate?: string
  hashtags?: string[]
}

interface PollPreviewProps {
  content: PollContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const PollPreview: React.FC<PollPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  const totalVotes = content.options?.reduce((sum, option) => sum + option.votes, 0) || 0

  return (
    <div className="card">
      {/* Author Header */}
      <div className="flex items-center gap-3 mb-4">
        <Avatar
          src={createdBy.avatar}
          name={createdBy.name}
          className="w-12 h-12"
        />
        <div className="flex-1">
          <div className="font-semibold text-text-primary">{createdBy.name}</div>
          <div className="text-sm text-text-tertiary">
            {formatDate(scheduledDate || new Date().toISOString())}
          </div>
        </div>
      </div>

      {/* Poll Content */}
      <div className="space-y-4">
        {/* Poll Question */}
        <div className="flex items-start gap-3">
          <BarChart3 className="w-5 h-5 text-brand-500 mt-1 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-text-primary mb-4">
              {content.question}
            </h3>

            {/* Poll Options */}
            <div className="space-y-3">
              {content.options?.map((option, index) => {
                const percentage = totalVotes > 0 ? Math.round((option.votes / totalVotes) * 100) : 0
                
                return (
                  <div key={index} className="relative">
                    <div className="flex items-center justify-between p-3 bg-dark-secondary rounded-lg border border-dark-quaternary hover:border-brand-500 cursor-pointer transition-colors">
                      <span className="text-text-primary font-medium">{option.option}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-text-tertiary">{option.votes} votes</span>
                        <span className="text-sm font-semibold text-brand-500">{percentage}%</span>
                      </div>
                    </div>
                    
                    {/* Progress Bar */}
                    <div className="absolute bottom-0 left-0 h-1 bg-brand-500 rounded-b-lg transition-all duration-300"
                         style={{ width: `${percentage}%` }} />
                  </div>
                )
              })}
            </div>

            {/* Poll Stats */}
            <div className="flex items-center justify-between mt-4 pt-3 border-t border-dark-quaternary">
              <span className="text-sm text-text-tertiary">
                {totalVotes} total votes
              </span>
              {content.expirationDate && (
                <span className="text-sm text-text-tertiary">
                  Expires {formatDate(content.expirationDate)}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {content.hashtags.map((tag, index) => (
              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                {tag.startsWith('#') ? tag : `#${tag}`}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default PollPreview
