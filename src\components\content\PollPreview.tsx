import React from 'react'
import { BarChart3 } from 'lucide-react'
import { getAvatarColor, getInitials } from '@/utils/avatarUtils'

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = React.useState(false)

  if (imageError || !src) {
    return (
      <div className={`${className} ${getAvatarColor(name)} rounded-full flex items-center justify-center text-white font-semibold`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`${className} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  )
}

interface PollContent {
  question: string
  options: string[] // Simple array of option strings
  expirationDate?: string
  hashtags?: string[]
}

interface PollPreviewProps {
  content: PollContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
  copyButton?: React.ReactNode
}

const PollPreview: React.FC<PollPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  return (
    <div className="card">
      {/* Author Header */}
      <div className="flex items-center gap-3 mb-4">
        <Avatar
          src={createdBy.avatar}
          name={createdBy.name}
          className="w-12 h-12"
        />
        <div className="flex-1">
          <div className="font-semibold text-text-primary">{createdBy.name}</div>
          <div className="text-sm text-text-tertiary">
            {formatDate(scheduledDate || new Date().toISOString())}
          </div>
        </div>
      </div>

      {/* Poll Content */}
      <div className="space-y-4">
        {/* Poll Question */}
        <div className="flex items-start gap-3">
          <BarChart3 className="w-5 h-5 text-brand-500 mt-1 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-text-primary mb-4">
              {content.question}
            </h3>

            {/* Poll Options */}
            <div className="space-y-3">
              {content.options?.map((option, index) => (
                <div key={index} className="relative">
                  <div className="flex items-center justify-between p-3 bg-dark-secondary rounded-lg border border-dark-quaternary hover:border-brand-500 cursor-pointer transition-colors">
                    <span className="text-text-primary font-medium">{option}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-text-tertiary">Vote</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Poll Stats */}
            <div className="flex items-center justify-between mt-4 pt-3 border-t border-dark-quaternary">
              <span className="text-sm text-text-tertiary">
                {content.options?.length || 0} options
              </span>
              {content.expirationDate && (
                <span className="text-sm text-text-tertiary">
                  Expires {formatDate(content.expirationDate)}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {content.hashtags.map((tag, index) => (
              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                {tag.startsWith('#') ? tag : `#${tag}`}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default PollPreview
