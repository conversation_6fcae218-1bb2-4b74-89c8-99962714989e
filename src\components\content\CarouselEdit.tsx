import React from 'react'
import { X, Plus, Image as ImageIcon, ExternalLink } from 'lucide-react'
import Button from '@/components/ui/Button'

interface CarouselSlide {
  title: string
  content: string
  image_description?: string
}

interface CarouselContent {
  title: string
  slides: CarouselSlide[]
  call_to_action?: string
  hashtags?: string[]
}

interface CarouselEditProps {
  content: CarouselContent
  editContent: CarouselContent
  setEditContent: (content: CarouselContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const CarouselEdit: React.FC<CarouselEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof CarouselContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateSlide = (index: number, field: keyof CarouselSlide, value: string) => {
    const updatedSlides = [...(editContent.slides || [])]
    updatedSlides[index] = { ...updatedSlides[index], [field]: value }
    updateField('slides', updatedSlides)
  }

  const addSlide = () => {
    const newSlide: CarouselSlide = {
      title: '',
      content: '',
      image_description: ''
    }
    updateField('slides', [...(editContent.slides || []), newSlide])
  }

  const removeSlide = (index: number) => {
    const updatedSlides = (editContent.slides || []).filter((_, i) => i !== index)
    updateField('slides', updatedSlides)
  }

  const moveSlide = (index: number, direction: 'up' | 'down') => {
    const updatedSlides = [...(editContent.slides || [])]
    const newIndex = direction === 'up' ? index - 1 : index + 1
    
    if (newIndex >= 0 && newIndex < updatedSlides.length) {
      [updatedSlides[index], updatedSlides[newIndex]] = [updatedSlides[newIndex], updatedSlides[index]]
      updateField('slides', updatedSlides)
    }
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  return (
    <div className="space-y-6">
      {/* Carousel Title */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">
          <ImageIcon className="w-4 h-4 inline mr-2" />
          Carousel Title
        </label>
        <input
          type="text"
          value={editContent.title || ''}
          onChange={(e) => updateField('title', e.target.value)}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
          placeholder="Enter carousel title..."
        />
      </div>

      {/* Slides */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Carousel Slides</label>
          <Button
            onClick={addSlide}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            disabled={(editContent.slides || []).length >= 10}
          >
            <Plus className="w-4 h-4" />
            Add Slide
          </Button>
        </div>
        
        <div className="space-y-4">
          {(editContent.slides || []).map((slide, index) => (
            <div key={index} className="p-4 bg-dark-secondary rounded-lg border border-dark-quaternary">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-text-secondary">
                    Slide {index + 1}
                  </span>
                  <div className="flex gap-1">
                    {index > 0 && (
                      <button
                        onClick={() => moveSlide(index, 'up')}
                        className="text-text-tertiary hover:text-text-secondary p-1 text-xs"
                        title="Move up"
                      >
                        ↑
                      </button>
                    )}
                    {index < (editContent.slides || []).length - 1 && (
                      <button
                        onClick={() => moveSlide(index, 'down')}
                        className="text-text-tertiary hover:text-text-secondary p-1 text-xs"
                        title="Move down"
                      >
                        ↓
                      </button>
                    )}
                  </div>
                </div>
                {(editContent.slides || []).length > 1 && (
                  <button
                    onClick={() => removeSlide(index)}
                    className="text-red-400 hover:text-red-300 p-1"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
              
              <div className="space-y-3">
                <input
                  type="text"
                  value={slide.title}
                  onChange={(e) => updateSlide(index, 'title', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-primary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                  placeholder="Slide title..."
                />
                <textarea
                  value={slide.content}
                  onChange={(e) => updateSlide(index, 'content', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 bg-dark-primary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500 resize-none"
                  placeholder="Slide content..."
                />
                <input
                  type="text"
                  value={slide.image_description || ''}
                  onChange={(e) => updateSlide(index, 'image_description', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-primary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                  placeholder="Image description (optional)..."
                />
              </div>
            </div>
          ))}
        </div>
        
        {(editContent.slides || []).length === 0 && (
          <div className="text-center py-8 text-text-tertiary">
            <ImageIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No slides yet. Add your first slide to get started.</p>
          </div>
        )}
        
        {(editContent.slides || []).length >= 10 && (
          <p className="text-xs text-text-tertiary mt-2">
            Maximum of 10 slides allowed
          </p>
        )}
      </div>

      {/* Call to Action */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">
          <ExternalLink className="w-4 h-4 inline mr-2" />
          Call to Action (Optional)
        </label>
        <input
          type="text"
          value={editContent.call_to_action || ''}
          onChange={(e) => updateField('call_to_action', e.target.value)}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
          placeholder="Learn More, Shop Now, Sign Up, etc."
        />
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 px-3 py-2 bg-dark-secondary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Carousel Tips */}
      <div className="p-4 bg-dark-secondary rounded-lg border border-dark-quaternary">
        <h4 className="text-sm font-medium text-text-secondary mb-2">Carousel Tips</h4>
        <ul className="text-xs text-text-tertiary space-y-1">
          <li>• Use 2-10 slides for optimal engagement</li>
          <li>• Keep slide content concise and visually appealing</li>
          <li>• Include a clear call-to-action to drive conversions</li>
          <li>• Use consistent visual style across all slides</li>
        </ul>
      </div>
    </div>
  )
}

export default CarouselEdit
