import React from 'react'
import { Avatar } from '@/components/ui'
import { formatDate } from '@/utils/formatDate'
import { MessageCircle, Repeat2, Heart } from 'lucide-react'
import MarkdownRenderer from '@/components/MarkdownRenderer'

interface ThreadPost {
  content: string
}

interface ThreadContent {
  thread: ThreadPost[]
  hashtags?: string[]
}

interface ThreadPreviewProps {
  content: ThreadContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const ThreadPreview: React.FC<ThreadPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="space-y-4">
      {content.thread && content.thread.map((threadPost, index) => (
        <div key={index} className="card">
          <div className="p-6">
            <div className="flex items-start gap-3">
              <Avatar
                src={createdBy.avatar}
                name={createdBy.name}
                className="w-12 h-12 flex-shrink-0"
              />
              
              <div className="flex-1 min-w-0">
                {/* Author Info */}
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-semibold text-text-primary">{createdBy.name}</span>
                  <span className="text-text-tertiary text-sm">
                    {formatDate(scheduledDate || new Date().toISOString())}
                  </span>
                </div>

                {/* Thread Content */}
                <div className="prose prose-invert max-w-none mb-4">
                  <MarkdownRenderer content={threadPost.content} />
                </div>

                {/* Thread Indicator */}
                {index < content.thread.length - 1 && (
                  <div className="flex items-center gap-2 text-text-tertiary text-sm mb-4">
                    <div className="w-px h-4 bg-text-tertiary"></div>
                    <span>Thread continues...</span>
                  </div>
                )}

                {/* Social Actions */}
                <div className="flex items-center gap-6">
                  <button className="flex items-center gap-2 text-text-tertiary hover:text-blue-400 transition-colors">
                    <MessageCircle className="w-4 h-4" />
                    <span className="text-sm">Reply</span>
                  </button>
                  <button className="flex items-center gap-2 text-text-tertiary hover:text-green-400 transition-colors">
                    <Repeat2 className="w-4 h-4" />
                    <span className="text-sm">Repost</span>
                  </button>
                  <button className="flex items-center gap-2 text-text-tertiary hover:text-red-400 transition-colors">
                    <Heart className="w-4 h-4" />
                    <span className="text-sm">Like</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Thread Connection Line */}
          {index < content.thread.length - 1 && (
            <div className="flex justify-center">
              <div className="w-px h-4 bg-dark-quaternary"></div>
            </div>
          )}
        </div>
      ))}

      {/* Hashtags (shown only on last post) */}
      {content.hashtags && content.hashtags.length > 0 && (
        <div className="card">
          <div className="p-4">
            <div className="flex flex-wrap gap-2">
              {content.hashtags.map((tag, index) => (
                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                  {tag.startsWith('#') ? tag : `#${tag}`}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ThreadPreview
