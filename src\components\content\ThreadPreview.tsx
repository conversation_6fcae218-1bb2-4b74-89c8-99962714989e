import React from 'react'
import { MessageCircle, Repeat2, Heart } from 'lucide-react'
import Markdown<PERSON>ender<PERSON> from '@/components/MarkdownRenderer'

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = React.useState(false)

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ]
    const index = name.charCodeAt(0) % colors.length
    return colors[index]
  }

  if (imageError || !src) {
    return (
      <div className={`${className} ${getAvatarColor(name)} rounded-full flex items-center justify-center text-white font-semibold`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`${className} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  )
}

interface ThreadPost {
  content: string
}

interface ThreadContent {
  thread: ThreadPost[]
  hashtags?: string[]
}

interface ThreadPreviewProps {
  content: ThreadContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const ThreadPreview: React.FC<ThreadPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="space-y-4">
      {content.thread && content.thread.map((threadPost, index) => (
        <div key={index} className="card">
          <div className="p-6">
            <div className="flex items-start gap-3">
              <Avatar
                src={createdBy.avatar}
                name={createdBy.name}
                className="w-10 h-10 flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-semibold text-text-primary">{createdBy.name}</span>
                  <span className="text-text-tertiary">@{createdBy.name.toLowerCase().replace(' ', '')}</span>
                  <span className="text-text-tertiary">·</span>
                  <span className="text-text-tertiary text-sm">now</span>
                </div>
                </div>

                <div className="space-y-3">
                  <MarkdownRenderer
                    content={threadPost.content}
                    className="text-text-secondary leading-relaxed"
                  />
                </div>

                {index < content.thread.length - 1 && (
                  <div className="mt-4 flex items-center gap-2 text-text-tertiary text-sm">
                    <div className="w-0.5 h-4 bg-text-quaternary"></div>
                    <span>Thread continues...</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Thread Connection Line */}
          {index < content.thread.length - 1 && (
            <div className="flex justify-center">
              <div className="w-px h-4 bg-dark-quaternary"></div>
            </div>
          )}
        </div>
      ))}

      {/* Hashtags (shown only on last post) */}
      {content.hashtags && content.hashtags.length > 0 && (
        <div className="card">
          <div className="p-4">
            <div className="flex flex-wrap gap-2">
              {content.hashtags.map((tag, index) => (
                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                  {tag.startsWith('#') ? tag : `#${tag}`}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ThreadPreview
