import React from 'react'
import { X, Plus, MessageSquare } from 'lucide-react'
import But<PERSON> from '@/components/ui/Button'

interface ThreadPost {
  content: string
}

interface ThreadContent {
  thread: ThreadPost[]
  hashtags?: string[]
}

interface ThreadEditProps {
  content: ThreadContent
  editContent: ThreadContent
  setEditContent: (content: ThreadContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const ThreadEdit: React.FC<ThreadEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof ThreadContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateThreadPost = (index: number, content: string) => {
    const updatedThread = [...(editContent.thread || [])]
    updatedThread[index] = { content }
    updateField('thread', updatedThread)
  }

  const addThreadPost = () => {
    const newPost: ThreadPost = { content: '' }
    updateField('thread', [...(editContent.thread || []), newPost])
  }

  const removeThreadPost = (index: number) => {
    const updatedThread = (editContent.thread || []).filter((_, i) => i !== index)
    updateField('thread', updatedThread)
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  return (
    <div className="space-y-6">
      {/* Thread Posts */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">
            <MessageSquare className="w-4 h-4 inline mr-2" />
            Thread Posts
          </label>
          <Button
            onClick={addThreadPost}
            variant="secondary"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Post
          </Button>
        </div>
        
        <div className="space-y-4">
          {(editContent.thread || []).map((post, index) => (
            <div key={index} className="relative">
              <div className="p-4 bg-dark-secondary rounded-lg border border-dark-quaternary">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-text-secondary">
                    Post {index + 1}
                  </span>
                  {(editContent.thread || []).length > 1 && (
                    <button
                      onClick={() => removeThreadPost(index)}
                      className="text-red-400 hover:text-red-300 p-1"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  )}
                </div>
                
                <textarea
                  value={post.content}
                  onChange={(e) => updateThreadPost(index, e.target.value)}
                  rows={4}
                  className="w-full search-input resize-none"
                  placeholder={`What's happening in post ${index + 1}?`}
                />
                
                {/* Character count */}
                <div className="flex justify-end mt-2">
                  <span className={`text-xs ${
                    post.content.length > 280 ? 'text-red-400' : 'text-text-tertiary'
                  }`}>
                    {post.content.length}/280
                  </span>
                </div>
              </div>
              
              {/* Thread connection line */}
              {index < (editContent.thread || []).length - 1 && (
                <div className="flex justify-center py-2">
                  <div className="w-px h-4 bg-dark-quaternary"></div>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {(editContent.thread || []).length === 0 && (
          <div className="text-center py-8 text-text-tertiary">
            <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No thread posts yet. Add your first post to get started.</p>
          </div>
        )}
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 search-input"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Thread Tips */}
      <div className="p-4 bg-dark-secondary rounded-lg border border-dark-quaternary">
        <h4 className="text-sm font-medium text-text-secondary mb-2">Thread Tips</h4>
        <ul className="text-xs text-text-tertiary space-y-1">
          <li>• Keep each post under 280 characters for optimal engagement</li>
          <li>• Use the first post to hook your audience</li>
          <li>• End with a call-to-action or question</li>
          <li>• Add hashtags to increase discoverability</li>
        </ul>
      </div>
    </div>
  )
}

export default ThreadEdit
