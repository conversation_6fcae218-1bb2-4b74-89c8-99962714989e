
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import Layout from '@/components/layout/Layout'
import AuthGuard from '@/components/auth/AuthGuard'
import Login from '@/pages/Login'
import Dashboard from '@/pages/Dashboard'
import Campaigns from '@/pages/Campaigns'
import CampaignDetails from '@/pages/CampaignDetails'
import PostDetail from '@/pages/PostDetail'
import Calendar from '@/pages/Calendar'
import BrandProfile from '@/pages/BrandProfile'
import ToastContainer from '@/components/ui/ToastContainer'
import '@/styles/globals.css'

function App() {
  return (
    <div className="App">
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<Login />} />

          {/* Protected routes with layout and auth guard */}
          <Route path="/*" element={
            <AuthGuard requireBrand={true}>
              <Layout>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/campaigns" element={<Campaigns />} />
                  <Route path="/campaigns/:campaignId" element={<CampaignDetails />} />
                  <Route path="/campaigns/:id/posts/:postId" element={<PostDetail />} />
                  <Route path="/quick-posts/:quickPostId" element={<PostDetail />} />
                  <Route path="/calendar" element={<Calendar />} />
                  <Route path="/brand-profile" element={<BrandProfile />} />
                  {/* Redirect unknown routes to dashboard */}
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </Layout>
            </AuthGuard>
          } />
        </Routes>
      </Router>

      {/* Global Toast Container */}
      <ToastContainer />
    </div>
  )
}

export default App 