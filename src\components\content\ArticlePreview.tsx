import React from 'react'
import <PERSON><PERSON><PERSON>ender<PERSON> from '@/components/MarkdownRenderer'
import { formatDate } from '@/utils/formatDate'

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = React.useState(false)

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ]
    const index = name.charCodeAt(0) % colors.length
    return colors[index]
  }

  if (imageError || !src) {
    return (
      <div className={`${className} ${getAvatarColor(name)} rounded-full flex items-center justify-center text-white font-semibold`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`${className} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  )
}

interface ArticleSection {
  id?: string
  title: string
  content: string
  // Legacy support for old format
  Introduction?: string
  Description?: string
}

interface ArticleContent {
  title: string
  subheading?: string
  sections: ArticleSection[]
  summary?: string
  hashtags?: string[]
}

interface ArticlePreviewProps {
  content: ArticleContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const ArticlePreview: React.FC<ArticlePreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="card">
      <div className="max-w-4xl mx-auto">
        {/* Author Info */}
        <div className="flex items-center gap-3 mb-6 pb-6 border-b border-dark-quaternary">
          <Avatar
            src={createdBy.avatar}
            name={createdBy.name}
            className="w-12 h-12"
          />
          <div>
            <div className="font-semibold text-text-primary">{createdBy.name}</div>
            <div className="text-sm text-text-tertiary">
              {formatDate(scheduledDate || new Date().toISOString())}
            </div>
          </div>
        </div>

        {/* Article Header */}
        <div className="mb-8">
          {content.title && (
            <h1 className="text-3xl font-bold text-text-primary mb-4">
              {content.title}
            </h1>
          )}
          {content.subheading && (
            <p className="text-lg text-text-secondary mb-6 italic">
              {content.subheading}
            </p>
          )}
        </div>

        {/* Article Sections */}
        <div className="space-y-8">
          {content.sections && content.sections.map((section, index) => (
            <div key={section.id || index} className="space-y-4">
              {/* Support both new and legacy formats */}
              {section.title || section.Introduction ? (
                <div>
                  <h2 className="text-xl font-semibold text-text-primary mb-4">
                    {section.title || 'Introduction'}
                  </h2>
                  <MarkdownRenderer
                    content={section.content || section.Introduction || ''}
                    className="prose prose-invert max-w-none"
                  />
                </div>
              ) : null}

              {/* Legacy Description field */}
              {section.Description && (
                <div>
                  <h2 className="text-xl font-semibold text-text-primary mb-4">Description</h2>
                  <MarkdownRenderer
                    content={section.Description}
                    className="prose prose-invert max-w-none"
                  />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Summary */}
        {content.summary && (
          <div className="mt-8 pt-6 border-t border-dark-quaternary">
            <h3 className="text-lg font-semibold text-text-primary mb-3">Summary</h3>
            <div className="bg-dark-secondary rounded-lg p-4">
              <p className="text-text-secondary italic">{content.summary}</p>
            </div>
          </div>
        )}

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="mt-6 flex flex-wrap gap-2">
            {content.hashtags.map((tag, index) => (
              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ArticlePreview
