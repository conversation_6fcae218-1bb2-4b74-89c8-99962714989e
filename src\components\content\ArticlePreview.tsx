import React from 'react'
import { Avatar } from '@/components/ui'
import { formatDate } from '@/utils/formatDate'
import Markdown<PERSON>enderer from '@/components/MarkdownRenderer'

interface ArticleContent {
  title: string
  subheading?: string
  sections?: Array<{
    Introduction?: string
    Description?: string
    title?: string
    content?: string
    id?: string
  }>
  summary?: string
  hashtags?: string[]
}

interface ArticlePreviewProps {
  content: ArticleContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const ArticlePreview: React.FC<ArticlePreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="card">
      <div className="max-w-4xl mx-auto">
        {/* Author Info */}
        <div className="flex items-center gap-3 mb-6 pb-6 border-b border-dark-quaternary">
          <Avatar
            src={createdBy.avatar}
            name={createdBy.name}
            className="w-12 h-12"
          />
          <div>
            <div className="font-semibold text-text-primary">{createdBy.name}</div>
            <div className="text-sm text-text-tertiary">
              {formatDate(scheduledDate || new Date().toISOString())}
            </div>
          </div>
        </div>

        {/* Article Header */}
        <div className="mb-8">
          {content.title && (
            <h1 className="text-3xl font-bold text-text-primary mb-4">
              {content.title}
            </h1>
          )}
          {content.subheading && (
            <p className="text-lg text-text-secondary mb-6 italic">
              {content.subheading}
            </p>
          )}
        </div>

        {/* Article Sections */}
        {content.sections && content.sections.length > 0 ? (
          <div className="space-y-8">
            {content.sections.map((section, index) => {
              // Handle different section data structures
              const sectionTitle = section.title || section.Introduction || `Section ${index + 1}`;
              const sectionContent = section.content || section.Description || '';
              
              return (
                <div key={section.id || index} className="prose prose-invert max-w-none">
                  {sectionTitle && (
                    <h2 className="text-xl font-semibold text-text-primary mb-4">
                      {sectionTitle}
                    </h2>
                  )}
                  {sectionContent && (
                    <MarkdownRenderer content={sectionContent} />
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          /* Fallback content when sections are missing */
          <div className="prose prose-invert max-w-none">
            {content.summary && (
              <MarkdownRenderer content={content.summary} />
            )}
            {!content.summary && (
              <div className="text-center py-8 text-text-tertiary">
                <p>Article content will be displayed here once sections are added.</p>
              </div>
            )}
          </div>
        )}

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="mt-8 pt-6 border-t border-dark-quaternary">
            <div className="flex flex-wrap gap-2">
              {content.hashtags.map((tag, index) => (
                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                  {tag.startsWith('#') ? tag : `#${tag}`}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ArticlePreview
