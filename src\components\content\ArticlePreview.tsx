import React from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/MarkdownRenderer'
import { formatDate } from '@/utils/formatDate'
import { getAvatarColor, getInitials } from '@/utils/avatarUtils'

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = React.useState(false)

  if (imageError || !src) {
    return (
      <div className={`${className} ${getAvatarColor(name)} rounded-full flex items-center justify-center text-white font-semibold`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`${className} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  )
}

interface ArticleSection {
  id?: string
  title: string
  content: string
  // Legacy support for old format
  Introduction?: string
  Description?: string
}

interface ArticleContent {
  title: string
  subheading?: string
  sections: ArticleSection[]
  summary?: string
  hashtags?: string[]
}

interface ArticlePreviewProps {
  content: ArticleContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
  copyButton?: React.ReactNode
}

const ArticlePreview: React.FC<ArticlePreviewProps> = ({
  content,
  createdBy,
  scheduledDate,
  copyButton
}) => {
  return (
    <div className="card">
      <div className="max-w-4xl mx-auto">
        {/* Author Info */}
        <div className="flex items-center justify-between mb-6 pb-6 border-b border-dark-quaternary">
          <div className="flex items-center gap-3">
            <Avatar
              src={createdBy.avatar}
              name={createdBy.name}
              className="w-12 h-12"
            />
            <div>
              <div className="font-semibold text-text-primary">{createdBy.name}</div>
              <div className="text-sm text-text-tertiary">
                {formatDate(scheduledDate || new Date().toISOString())}
              </div>
            </div>
          </div>
          {copyButton && copyButton}
        </div>

        {/* Article Header */}
        <div className="mb-8">
          {content.title && (
            <h1 className="text-2xl font-bold text-text-primary mb-4 leading-tight">
              {content.title}
            </h1>
          )}
          {content.subheading && (
            <p className="text-lg text-text-secondary mb-4 italic leading-relaxed">
              {content.subheading}
            </p>
          )}
        </div>

        {/* Article Sections */}
        <div className="space-y-6">
          {content.sections && content.sections.map((section, index) => (
            <div key={section.id || index} className="space-y-4">
              {/* Support both new and legacy formats */}
              {section.title || section.Introduction ? (
                <div>
                  <h2 className="text-lg font-semibold text-text-primary mb-3 leading-tight">
                    {section.title || 'Introduction'}
                  </h2>
                  <div className="prose prose-invert max-w-none">
                    <MarkdownRenderer
                      content={section.content || section.Introduction || ''}
                    />
                  </div>
                </div>
              ) : null}

              {/* Legacy Description field */}
              {section.Description && (
                <div>
                  <h2 className="text-lg font-semibold text-text-primary mb-3 leading-tight">Description</h2>
                  <div className="prose prose-invert max-w-none">
                    <MarkdownRenderer
                      content={section.Description}
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Summary */}
        {content.summary && (
          <div className="mt-6 pt-6 border-t border-dark-quaternary">
            <h3 className="text-lg font-semibold text-text-primary mb-3">Summary</h3>
            <div className="bg-dark-secondary/50 rounded-lg p-4 border border-dark-quaternary/50">
              <p className="text-text-secondary italic leading-relaxed">{content.summary}</p>
            </div>
          </div>
        )}

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="mt-8 pt-6 border-t border-dark-quaternary/50 flex flex-wrap gap-2">
            {content.hashtags.map((tag, index) => (
              <span key={index} className="text-brand-400 hover:text-brand-300 cursor-pointer text-sm font-medium transition-colors">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ArticlePreview
