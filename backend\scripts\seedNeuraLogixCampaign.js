import mongoose from 'mongoose';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { User, Brand, Campaign } from '../models/index.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Seed NeuraLogix campaign data into the database
 * This script will:
 * 1. Connect to the database
 * 2. Find or create the NeuraLogix brand
 * 3. Create the campaign with all content types
 * 4. Display results
 */
const seedNeuraLogixCampaign = async () => {
  try {
    console.log('🚀 Starting NeuraLogix campaign seeding...');
    
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    });
    console.log('✅ Connected to MongoDB');

    // Load campaign data
    const campaignDataPath = path.join(__dirname, '../../neuralogix-campaign-data.json');
    if (!fs.existsSync(campaignDataPath)) {
      throw new Error('❌ neuralogix-campaign-data.json not found. Please ensure the file exists in the project root.');
    }

    const campaignData = JSON.parse(fs.readFileSync(campaignDataPath, 'utf8'));
    console.log(`📊 Loaded campaign data with ${campaignData.data.length} content items`);

    // Step 1: Find admin user (required for brand and campaign creation)
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      throw new Error('❌ Admin user not found. Please run "npm run init-db" first to create the admin user.');
    }
    console.log(`✅ Found admin user: ${adminUser.email} (${adminUser._id})`);

    // Step 2: Find or create NeuraLogix brand
    let neuralogixBrand = await Brand.findOne({ 
      name: 'NeuraLogix',
      userId: adminUser._id 
    });

    if (!neuralogixBrand) {
      console.log('🏢 Creating NeuraLogix brand...');
      
      neuralogixBrand = new Brand({
        userId: adminUser._id,
        name: 'NeuraLogix',
        about: 'NeuraLogix leverages Artificial Intelligence to transform enterprise operations across United States, India, United Kingdom, and globally through a worldwide network.',
        story: 'NeuraLogix is a strategic joint venture formed by Acompworld, a leader in AI-powered enterprise solutions.',
        location: 'USA: 1081 Parsippany Blvd., Parsippany, New Jersey 07054, USA\nIndia: Mumbai Operations Center\nUK: London Technology Hub',
        targetGeo: 'United States, India, United Kingdom, and globally through a worldwide network',
        website: 'https://neuralogix.ai',
        size: 'medium',
        type: 'startup',
        industry: 'technology',
        socialMedia: [
          { platform: 'linkedin', url: 'https://linkedin.com/company/neuralogix' },
          { platform: 'twitter', url: 'https://twitter.com/neuralogix' }
        ],
        contactInfo: {
          email: '<EMAIL>',
          phone: '+****************',
          address: '1081 Parsippany Blvd., Parsippany, New Jersey 07054, USA'
        },
        keyTeamMembers: [
          {
            name: 'Dr. Sarah Chen',
            role: 'Chief Technology Officer',
            bio: 'AI research expert with 15+ years in enterprise solutions'
          },
          {
            name: 'Michael Rodriguez',
            role: 'VP of Global Operations',
            bio: 'Oversees operations across US, India, and UK markets'
          },
          {
            name: 'Priya Sharma',
            role: 'Head of AI Development',
            bio: 'Leads AI platform development and innovation initiatives'
          }
        ],
        brandIdentity: {
          mission: 'To transform enterprise operations through innovative AI solutions',
          vision: 'Leading the global AI transformation revolution',
          values: ['Innovation', 'Excellence', 'Global Collaboration', 'Customer Success'],
          tone: 'Professional, innovative, and results-driven',
          personality: ['Expert', 'Trustworthy', 'Forward-thinking', 'Global']
        },
        products: [
          {
            name: 'NeuraLogix AI Platform',
            description: 'Comprehensive AI solution for enterprise transformation',
            features: ['Real-time Analytics', 'Predictive Modeling', 'Automated Decision Making', 'Seamless Integration']
          },
          {
            name: 'Enterprise AI Consulting',
            description: 'Strategic consulting for AI implementation and optimization',
            features: ['Strategy Development', 'Implementation Planning', 'Change Management', 'Performance Optimization']
          }
        ],
        icps: [
          {
            name: 'Enterprise Technology Leaders',
            description: 'CTOs, IT Directors, and technology decision-makers in large enterprises',
            demographics: 'C-level executives, 40-55 years old, technology-focused',
            painPoints: ['Legacy system limitations', 'Digital transformation challenges', 'Operational inefficiencies'],
            goals: ['Modernize operations', 'Improve efficiency', 'Reduce costs', 'Stay competitive']
          },
          {
            name: 'Manufacturing Operations Managers',
            description: 'Operations leaders in manufacturing and industrial sectors',
            demographics: 'Operations managers, 35-50 years old, process-focused',
            painPoints: ['Manual processes', 'Quality control issues', 'Predictive maintenance needs'],
            goals: ['Automate operations', 'Improve quality', 'Reduce downtime', 'Optimize resources']
          }
        ],
        competitors: [
          {
            name: 'IBM Watson',
            strengths: ['Brand recognition', 'Enterprise relationships'],
            weaknesses: ['Complex implementation', 'High costs']
          }
        ],
        isAIGenerated: false,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await neuralogixBrand.save();
      console.log(`✅ Created NeuraLogix brand: ${neuralogixBrand.name} (${neuralogixBrand._id})`);
    } else {
      console.log(`✅ Found existing NeuraLogix brand: ${neuralogixBrand.name} (${neuralogixBrand._id})`);
    }

    // Step 3: Check if campaign already exists
    const existingCampaign = await Campaign.findOne({
      name: campaignData.campaign.name,
      brandId: neuralogixBrand._id
    });

    if (existingCampaign) {
      console.log(`⚠️  Campaign "${campaignData.campaign.name}" already exists. Skipping creation.`);
      console.log(`📋 Existing campaign ID: ${existingCampaign._id}`);
      return;
    }

    // Step 4: Create the campaign with all content
    console.log('📝 Creating NeuraLogix campaign...');
    
    const newCampaign = new Campaign({
      userId: adminUser._id,
      brandId: neuralogixBrand._id,
      name: campaignData.campaign.name,
      purpose: 'Showcase NeuraLogix AI solutions for enterprise transformation',
      callToAction: 'Contact NeuraLogix for AI consultation',
      instructions: 'Comprehensive campaign demonstrating all content types with NeuraLogix branding',
      startDate: new Date(campaignData.campaign.createdAt),
      endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
      targetChannels: ['LinkedIn', 'Medium', 'Website-Blog', 'Facebook', 'Instagram', 'Email', 'YouTube', 'Twitter'],
      numberOfPosts: campaignData.data.length,
      status: 'active',
      isAIGenerated: true,
      
      // Content calendar from the data
      contentCalendar: campaignData.data.map(item => ({
        date: new Date(item.date),
        channel: item.channel,
        contentType: item.contentType,
        topic: item.topic,
        hashtags: item.hashtags || [],
        status: 'created'
      })),

      // Generated content with all 9 content types
      generatedContent: campaignData.data.map(item => ({
        date: new Date(item.date),
        channel: item.channel,
        contentType: item.contentType,
        topic: item.topic,
        hashtags: item.hashtags || [],
        
        // Content fields based on type
        title: item.title,
        subheading: item.subheading,
        content: item.content,
        
        // Article fields
        sections: item.sections,
        summary: item.summary,
        
        // Poll fields
        question: item.question,
        options: item.options,
        expirationDate: item.expirationDate ? new Date(item.expirationDate) : undefined,
        
        // Email fields
        subject: item.subject,
        body: item.body,
        preheader: item.preheader,
        emailSections: item.sections,
        footer: item.footer,
        
        // Video fields
        desc: item.desc,
        videoScript: item.videoScript,
        
        // Thread fields
        thread: item.thread,
        
        // Press Release fields
        headline: item.headline,
        subheadline: item.subheadline,
        dateline: item.dateline,
        lead_paragraph: item.lead_paragraph,
        body_sections: item.body_sections,
        boilerplate: item.boilerplate,
        contact_info: item.contact_info,
        
        // Carousel fields
        slides: item.slides,
        call_to_action: item.call_to_action,
        
        status: 'generated',
        generatedAt: new Date(item.date)
      })),

      createdAt: new Date(campaignData.campaign.createdAt),
      updatedAt: new Date(campaignData.campaign.updatedAt)
    });

    await newCampaign.save();
    console.log(`✅ Created campaign: ${newCampaign.name} (${newCampaign._id})`);

    // Step 5: Display summary
    console.log('\n📊 Campaign Summary:');
    console.log(`Campaign ID: ${newCampaign._id}`);
    console.log(`Brand: ${neuralogixBrand.name} (${neuralogixBrand._id})`);
    console.log(`Content Items: ${newCampaign.generatedContent.length}`);
    console.log(`Status: ${newCampaign.status}`);
    
    console.log('\n📝 Content Types Created:');
    const contentTypes = {};
    newCampaign.generatedContent.forEach(item => {
      contentTypes[item.contentType] = (contentTypes[item.contentType] || 0) + 1;
    });
    
    Object.entries(contentTypes).forEach(([type, count]) => {
      console.log(`• ${type}: ${count} items`);
    });

    console.log('\n🎯 Next Steps:');
    console.log('1. Login to the <NAME_EMAIL> (password: 123)');
    console.log('2. Navigate to the Campaigns page');
    console.log(`3. Find the "${campaignData.campaign.name}" campaign`);
    console.log('4. Click on any content item to test the PostDetail component');
    console.log('5. Verify all 9 content types render correctly');
    console.log('6. Test edit functionality for each content type');

    console.log('\n✅ NeuraLogix campaign seeding completed successfully!');

  } catch (error) {
    console.error('❌ Campaign seeding failed:', error.message);
    throw error;
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run seeding if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedNeuraLogixCampaign()
    .then(() => {
      console.log('🎉 Seeding script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding script failed:', error);
      process.exit(1);
    });
}

export default seedNeuraLogixCampaign;
