import React from 'react'
import { Heart, MessageCircle, Share2 } from 'lucide-react'
import Markdown<PERSON>enderer from '@/components/MarkdownRenderer'

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = React.useState(false)

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ]
    const index = name.charCodeAt(0) % colors.length
    return colors[index]
  }

  if (imageError || !src) {
    return (
      <div className={`${className} ${getAvatarColor(name)} rounded-full flex items-center justify-center text-white font-semibold`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`${className} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  )
}

interface PostContent {
  title: string
  content: string
  hashtags?: string[]
}

interface PostPreviewProps {
  content: PostContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const PostPreview: React.FC<PostPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="card">
      {/* Author Header */}
      <div className="flex items-center gap-3 mb-4">
        <Avatar
          src={createdBy.avatar}
          name={createdBy.name}
          className="w-12 h-12"
        />
        <div className="flex-1">
          <div className="font-semibold text-text-primary">{createdBy.name}</div>
          <div className="text-sm text-text-tertiary">
            {formatDate(scheduledDate || new Date().toISOString())}
          </div>
        </div>
      </div>

      {/* Post Content */}
      <div className="space-y-4">
        {/* Title */}
        {content.title && (
          <h2 className="text-xl font-semibold text-text-primary">
            {content.title}
          </h2>
        )}

        {/* Content */}
        {content.content && (
          <div className="prose prose-invert max-w-none">
            <MarkdownRenderer content={content.content} />
          </div>
        )}

        {/* Images */}
        {content.images && content.images.length > 0 && (
          <div className="grid grid-cols-2 gap-2 mt-4">
            {content.images.map((image, index) => (
              <div key={index} className="aspect-square bg-dark-secondary rounded-lg flex items-center justify-center">
                <span className="text-text-tertiary text-sm">Image {index + 1}</span>
              </div>
            ))}
          </div>
        )}

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {content.hashtags.map((tag, index) => (
              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                {tag.startsWith('#') ? tag : `#${tag}`}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Social Actions */}
      <div className="flex items-center gap-6 mt-6 pt-4 border-t border-dark-quaternary">
        <button className="flex items-center gap-2 text-text-tertiary hover:text-red-400 transition-colors">
          <Heart className="w-5 h-5" />
          <span className="text-sm">Like</span>
        </button>
        <button className="flex items-center gap-2 text-text-tertiary hover:text-blue-400 transition-colors">
          <MessageCircle className="w-5 h-5" />
          <span className="text-sm">Comment</span>
        </button>
        <button className="flex items-center gap-2 text-text-tertiary hover:text-green-400 transition-colors">
          <Share2 className="w-5 h-5" />
          <span className="text-sm">Share</span>
        </button>
      </div>
    </div>
  )
}

export default PostPreview
