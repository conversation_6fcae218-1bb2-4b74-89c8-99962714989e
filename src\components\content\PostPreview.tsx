import React from 'react'
import { Heart, MessageCircle, Share2 } from 'lucide-react'
import Markdown<PERSON>ender<PERSON> from '@/components/MarkdownRenderer'
import { getAvatarColor, getInitials } from '@/utils/avatarUtils'

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = React.useState(false)

  if (imageError || !src) {
    return (
      <div className={`${className} ${getAvatarColor(name)} rounded-full flex items-center justify-center text-white font-semibold`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`${className} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  )
}

interface PostContent {
  title: string
  content: string
  hashtags?: string[]
}

interface PostPreviewProps {
  content: PostContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const PostPreview: React.FC<PostPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  return (
    <div className="card">
      {/* Author Header */}
      <div className="flex items-center gap-3 mb-4">
        <Avatar
          src={createdBy.avatar}
          name={createdBy.name}
          className="w-12 h-12"
        />
        <div className="flex-1">
          <div className="font-semibold text-text-primary">{createdBy.name}</div>
          <div className="text-sm text-text-tertiary">
            {formatDate(scheduledDate || new Date().toISOString())}
          </div>
        </div>
      </div>

      {/* Post Content */}
      <div className="space-y-4">
        {/* Title */}
        {content.title && (
          <h2 className="text-xl font-semibold text-text-primary">
            {content.title}
          </h2>
        )}

        {/* Content */}
        {content.content && (
          <div className="prose prose-invert max-w-none">
            <MarkdownRenderer content={content.content} />
          </div>
        )}

        {/* Images */}
        {content.images && content.images.length > 0 && (
          <div className="grid grid-cols-2 gap-2 mt-4">
            {content.images.map((image, index) => (
              <div key={index} className="aspect-square bg-dark-secondary rounded-lg flex items-center justify-center">
                <span className="text-text-tertiary text-sm">Image {index + 1}</span>
              </div>
            ))}
          </div>
        )}

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4 pt-3 border-t border-dark-quaternary/50">
            {content.hashtags.map((tag, index) => (
              <span key={index} className="text-brand-400 hover:text-brand-300 cursor-pointer text-sm font-medium transition-colors">
                {tag.startsWith('#') ? tag : `#${tag}`}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Social Actions */}
      <div className="flex items-center gap-8 mt-6 pt-4 border-t border-dark-quaternary">
        <button className="flex items-center gap-2 text-text-tertiary hover:text-red-400 transition-colors group">
          <Heart className="w-5 h-5 group-hover:scale-110 transition-transform" />
          <span className="text-sm font-medium">Like</span>
        </button>
        <button className="flex items-center gap-2 text-text-tertiary hover:text-blue-400 transition-colors group">
          <MessageCircle className="w-5 h-5 group-hover:scale-110 transition-transform" />
          <span className="text-sm font-medium">Comment</span>
        </button>
        <button className="flex items-center gap-2 text-text-tertiary hover:text-green-400 transition-colors group">
          <Share2 className="w-5 h-5 group-hover:scale-110 transition-transform" />
          <span className="text-sm font-medium">Share</span>
        </button>
      </div>
    </div>
  )
}

export default PostPreview
