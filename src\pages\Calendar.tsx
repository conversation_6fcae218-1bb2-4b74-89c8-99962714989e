import React, { useState, useMemo, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useCampaignStore } from '@/stores/campaignStore'
import { cn } from '@/utils/cn'
import { formatDate } from '@/utils/formatDate'
import Button from '@/components/ui/Button'
import { Status } from '@/components/ui'
import type { ContentStatus } from '@/components/ui'
import QuickPostWizard from '@/components/modals/QuickPostWizard'
import { api } from '@/stores/authStore'
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Search,
  Globe,
  MoreVertical,
  ChevronUp,
  Megaphone,
  Zap,
  Sparkles,
  Eye,
  Mail
} from 'lucide-react'

// Channel Icons
const LinkedinIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
)

const TwitterIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
)

const InstagramIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
)

const YouTubeIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
)

const MediumIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"/>
  </svg>
)





// Add FacebookIcon definition
const FacebookIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M24 12.073c0-6.627-5.373-12-12-12S0 5.446 0 12.073c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953h-1.852c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
  </svg>
)

const channelConfig = {
  LinkedIn: { Icon: LinkedinIcon, color: 'text-blue-700', bg: 'bg-blue-50' },
  Twitter: { Icon: TwitterIcon, color: 'text-gray-900', bg: 'bg-gray-50' },
  Instagram: { Icon: InstagramIcon, color: 'text-pink-600', bg: 'bg-pink-50' },
  Facebook: { Icon: FacebookIcon, color: 'text-blue-600', bg: 'bg-blue-50' },
  Blog: { Icon: Globe, color: 'text-green-600', bg: 'bg-green-50' },
  YouTube: { Icon: YouTubeIcon, color: 'text-red-600', bg: 'bg-red-50' },
  Email: { Icon: Mail, color: 'text-purple-600', bg: 'bg-purple-50' },
  Medium: { Icon: MediumIcon, color: 'text-gray-800', bg: 'bg-gray-50' }
}

const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'published', label: 'Published' },
  { value: 'scheduled', label: 'Scheduled' },
  { value: 'changes_needed', label: 'Changes Needed' },
  { value: 'pending_review', label: 'Pending Review' }
]

const channelOptions = [
  { value: 'all', label: 'All Channels' },
  { value: 'LinkedIn', label: 'LinkedIn' },
  { value: 'Twitter', label: 'Twitter' },
  { value: 'Instagram', label: 'Instagram' },
  { value: 'Blog', label: 'Blog' }
]

const sourceOptions = [
  { value: 'all', label: 'All Sources' },
  { value: 'campaign', label: 'Campaigns' },
  { value: 'quick_post', label: 'Quick Posts' }
]

type ViewMode = 'week' | 'month' | 'all'
type SortField = 'title' | 'status' | 'channel' | 'date' | 'source'
type SortDirection = 'asc' | 'desc'

// Helper function to extract title from different content types
const getContentTitle = (content: any): string => {
  if (!content) return 'Untitled Content'

  // Handle new AI API content types
  switch (content.contentType) {
    case 'Article':
      return content.title || content.topic || 'Article'
    case 'Post':
      return content.title || content.topic || 'Post'
    case 'Poll':
      return content.question || content.topic || 'Poll'
    case 'Email':
      return content.subject || content.topic || 'Email'
    case 'Video':
      return content.title || content.topic || 'Video'
    case 'Thread':
      return content.thread?.[0]?.content?.substring(0, 50) + '...' || content.topic || 'Thread'
    case 'PressRelease':
      return content.headline || content.topic || 'Press Release'
    case 'Carousel':
      return content.title || content.topic || 'Carousel'
    default:
      // Legacy fallback for old content types
      return content.content || content.caption || content.tweet || content.postText ||
             content.subject || content.blogTitle || content.title || content.topic || 'Content'
  }
}

const Calendar: React.FC = () => {
  const navigate = useNavigate()
  const { campaigns, fetchCampaigns } = useCampaignStore()

  // Get URL search params to restore filter state
  const [searchParams, setSearchParams] = useSearchParams()

  const [viewMode, setViewMode] = useState<ViewMode>(
    (searchParams.get('view') as ViewMode) || 'month'
  )
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '')
  const [quickPosts, setQuickPosts] = useState<any[]>([])
  const [isLoadingQuickPosts, setIsLoadingQuickPosts] = useState(false)

  // Fetch campaigns when component mounts
  useEffect(() => {
    fetchCampaigns()
  }, [fetchCampaigns])

  // Fetch quick posts
  const fetchQuickPosts = async () => {
    try {
      setIsLoadingQuickPosts(true)
      const response = await api.get('/quick-posts')
      setQuickPosts(response.data.data || [])
    } catch (error) {
      console.error('Failed to fetch quick posts:', error)
      setQuickPosts([])
    } finally {
      setIsLoadingQuickPosts(false)
    }
  }

  // Fetch quick posts when component mounts
  useEffect(() => {
    fetchQuickPosts()
  }, [])

  // Transform live campaign data and quick posts to calendar content format
  const contentData = useMemo(() => {
    const allContent: any[] = []

    // Add campaign content
    campaigns.forEach((campaign) => {
      if (campaign.generatedContent) {
        campaign.generatedContent.forEach((content, index) => {
          allContent.push({
            id: `${campaign._id}-${index}`,
            date: content.date,
            channel: content.channel,
            type: content.contentType,
            title: getContentTitle(content),
            status: (content.status === 'generated' ? 'scheduled' : content.status) as ContentStatus,
            source: campaign.name,
            sourceType: "campaign",
            campaignId: campaign._id
          })
        })
      }
    })

    // Add quick post content
    quickPosts.forEach((quickPost) => {
      if (quickPost.generatedContent) {
        quickPost.generatedContent.forEach((content, index) => {
          allContent.push({
            id: `quickpost-${quickPost._id}-${index}`,
            date: quickPost.postDate,
            channel: quickPost.channel,
            type: content.contentType || 'post',
            title: getContentTitle(content),
            status: (content.status === 'generated' ? 'scheduled' : content.status) as ContentStatus,
            source: `Quick Post - ${quickPost.channel}`,
            sourceType: "quick_post",
            quickPostId: quickPost._id,
            instructions: quickPost.contentInstructions
          })
        })
      }
    })

    return allContent
  }, [campaigns, quickPosts])
  const [statusFilter, setStatusFilter] = useState(searchParams.get('status') || 'all')
  const [channelFilter, setChannelFilter] = useState(searchParams.get('channel') || 'all')
  const [sourceFilter, setSourceFilter] = useState(searchParams.get('source') || 'all')
  const [dayPopup, setDayPopup] = useState<{
    date: Date
    content: any[]
    position: { x: number; y: number }
  } | null>(null)
  const [statusOpen, setStatusOpen] = useState(false)
  const [channelOpen, setChannelOpen] = useState(false)
  const [sourceOpen, setSourceOpen] = useState(false)
  const [sortField, setSortField] = useState<SortField>('date')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)

  // Quick Post state
  const [isCreatingQuickPost, setIsCreatingQuickPost] = useState(false)

  // Filter content based on search and filters
  const filteredContent = useMemo(() => {
    let filtered = contentData.filter(item => {
      const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.source.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === 'all' || item.status === statusFilter
      const matchesChannel = channelFilter === 'all' || item.channel === channelFilter
      const matchesSource = sourceFilter === 'all' || item.sourceType === sourceFilter
      
      return matchesSearch && matchesStatus && matchesChannel && matchesSource
    })

    // Sort the filtered results
    filtered.sort((a, b) => {
      let aVal: any = a[sortField]
      let bVal: any = b[sortField]

      if (sortField === 'date') {
        aVal = new Date(aVal).getTime()
        bVal = new Date(bVal).getTime()
      }

      if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
      if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [contentData, searchTerm, statusFilter, channelFilter, sourceFilter, sortField, sortDirection])

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Quick Post handlers
  const handleCreateQuickPost = () => {
    setIsCreatingQuickPost(true)
  }

  const handleQuickPostBack = () => {
    setIsCreatingQuickPost(false)
  }

  const handleQuickPostGenerate = (postData: any, quickPostId?: string) => {
    console.log('Quick post generated:', postData)
    console.log('Quick post ID:', quickPostId)
    setIsCreatingQuickPost(false)
    // Refresh quick posts to show the new one
    fetchQuickPosts()

    // Navigate to the generated post view page
    if (quickPostId) {
      navigate(`/quick-posts/${quickPostId}`)
    }
  }

  const generateMonthlyCalendar = () => {
    const startOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1)
    const startOfCalendar = new Date(startOfMonth)
    startOfCalendar.setDate(startOfCalendar.getDate() - startOfMonth.getDay())
    
    const days = []
    const currentDate = new Date(startOfCalendar)
    
    for (let i = 0; i < 42; i++) {
      const dayContent = filteredContent.filter(item => {
        const itemDate = new Date(item.date)
        return itemDate.toDateString() === currentDate.toDateString()
      })
      
      days.push({
        date: new Date(currentDate),
        dayNumber: currentDate.getDate(),
        isCurrentMonth: currentDate.getMonth() === selectedDate.getMonth(),
        isToday: currentDate.toDateString() === new Date('2025-07-08').toDateString(),
        content: dayContent,
        hasContent: dayContent.length > 0
      })
      
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    return days
  }

  // Generate weekly calendar data
  const generateWeeklyCalendar = () => {
    const startOfWeek = new Date(selectedDate)
    startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay())
    
    const days = []
    const currentDate = new Date(startOfWeek)
    
    for (let i = 0; i < 7; i++) {
      const dayContent = filteredContent.filter(item => {
        const itemDate = new Date(item.date)
        return itemDate.toDateString() === currentDate.toDateString()
      })
      
      days.push({
        date: new Date(currentDate),
        dayNumber: currentDate.getDate(),
        dayName: currentDate.toLocaleDateString('en-US', { weekday: 'long' }),
        monthName: currentDate.toLocaleDateString('en-US', { month: 'short' }),
        isToday: currentDate.toDateString() === new Date('2025-07-08').toDateString(),
        content: dayContent,
        hasContent: dayContent.length > 0
      })
      
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    return days
  }

  const monthlyDays = generateMonthlyCalendar()
  const weeklyDays = generateWeeklyCalendar()

  const goToPreviousMonth = () => {
    setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() - 1, 1))
  }

  const goToNextMonth = () => {
    setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 1))
  }

  const goToPreviousWeek = () => {
    const newDate = new Date(selectedDate)
    newDate.setDate(selectedDate.getDate() - 7)
    setSelectedDate(newDate)
  }

  const goToNextWeek = () => {
    const newDate = new Date(selectedDate)
    newDate.setDate(selectedDate.getDate() + 7)
    setSelectedDate(newDate)
  }

  const goToToday = () => {
    setSelectedDate(new Date())
  }

  const handleContentClick = (contentId: string) => {
    const content = contentData.find(item => item.id === contentId)

    // Build URL with current filter state
    const params = new URLSearchParams()
    params.set('from', 'calendar')
    if (searchTerm) params.set('search', searchTerm)
    if (statusFilter !== 'all') params.set('status', statusFilter)
    if (channelFilter !== 'all') params.set('channel', channelFilter)
    if (sourceFilter !== 'all') params.set('source', sourceFilter)
    if (viewMode !== 'month') params.set('view', viewMode)

    const queryString = params.toString()

    if (content?.sourceType === 'campaign' && content.campaignId) {
      // Navigate to post detail page with calendar context and filters
      navigate(`/campaigns/${content.campaignId}/posts/${contentId}?${queryString}`)
    } else if (content?.sourceType === 'quick_post' && content.quickPostId) {
      // Navigate to quick post details with filters
      navigate(`/quick-posts/${content.quickPostId}?${queryString}`)
    } else {
      // Fallback navigation
      navigate(`/posts/${contentId}?${queryString}`)
    }
  }

  // Channel meta function - exactly like campaigns use
  const getChannelMeta = (channel: string) => {
    // Map lowercase channel names to proper channelConfig keys
    const channelMapping: Record<string, string> = {
      'linkedin': 'LinkedIn',
      'twitter': 'Twitter',
      'instagram': 'Instagram',
      'facebook': 'Facebook',
      'youtube': 'YouTube',
      'blog': 'Blog',
      'email': 'Email',
      'medium': 'Medium'
    }

    const mappedChannel = channelMapping[channel.toLowerCase()] || channel
    const channelConfig_item = channelConfig[mappedChannel as keyof typeof channelConfig]
    const Icon = channelConfig_item?.Icon || Globe

    // Gradients for different channels
    const gradients: Record<string, string> = {
      linkedin: 'linear-gradient(180deg,#0A66C2 0%,#004182 100%)',
      twitter: 'linear-gradient(180deg,#1DA1F2 0%,#0D8BDE 100%)',
      instagram: 'linear-gradient(180deg,#F58529 0%,#DD2A7B 100%)',
      facebook: 'linear-gradient(180deg,#1877F2 0%,#0e4ead 100%)',
      youtube: 'linear-gradient(180deg,#FF0000 0%,#BB0000 100%)',
      medium: 'linear-gradient(180deg,#444444 0%,#000000 100%)',
      blog: 'linear-gradient(180deg,#34d399 0%,#059669 100%)',
      email: 'linear-gradient(180deg,#8B5CF6 0%,#7C3AED 100%)',
    }

    const gradient = gradients[channel.toLowerCase()] || 'linear-gradient(180deg,#6366f1 0%,#4f46e5 100%)'

    return { Icon, gradient }
  }

  // Channel color functions (matching CampaignDetails week view)
  const getChannelColor = (channel: string) => {
    const colors = {
      LinkedIn: "text-blue-400",
      Twitter: "text-gray-300",
      Instagram: "text-pink-400",
      Facebook: "text-blue-400",
      YouTube: "text-red-400",
      Blog: "text-green-400",
      Email: "text-purple-400",
      Medium: "text-gray-400"
    }
    return colors[channel as keyof typeof colors] || "text-gray-400"
  }

  const getChannelBgColor = (channel: string) => {
    const colors = {
      LinkedIn: "bg-blue-500/10",
      Twitter: "bg-gray-500/10",
      Instagram: "bg-pink-500/10",
      Facebook: "bg-blue-500/10",
      YouTube: "bg-red-500/10",
      Blog: "bg-green-500/10",
      Email: "bg-purple-500/10",
      Medium: "bg-gray-500/10"
    }
    return colors[channel as keyof typeof colors] || "bg-gray-500/10"
  }

  const renderMonthlyCalendarView = () => (
    <div className="space-y-6">
      {/* Month Navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={goToPreviousMonth}
            className="w-8 h-8 p-0 rounded-full"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <h3 className="text-lg font-semibold text-text-primary">
            {selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </h3>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={goToNextMonth}
            className="w-8 h-8 p-0 rounded-full"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        <Button
          variant="secondary"
          size="sm"
          onClick={goToToday}
        >
          Today
        </Button>
      </div>

      {/* Calendar Grid - Dark Theme */}
      <div className="card overflow-hidden">
        {/* Weekday Headers */}
        <div className="grid grid-cols-7 border-b border-dark-quaternary">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="text-center text-xs font-medium text-text-tertiary uppercase tracking-wider py-3 border-r border-dark-quaternary last:border-r-0">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days - Dark Theme */}
        <div className="grid grid-cols-7">
          {monthlyDays.map((day, index) => (
            <div
              key={index}
              className={cn(
                'min-h-[120px] border-r border-b border-dark-quaternary last:border-r-0 relative',
                'transition-colors cursor-pointer hover:bg-dark-tertiary/50',
                day.isCurrentMonth ? 'bg-dark-secondary' : 'bg-dark-tertiary',
                // Remove bottom border for last row
                index >= 35 && 'border-b-0'
              )}
              onClick={(e) => {
                if (day.content.length > 0) {
                  const rect = e.currentTarget.getBoundingClientRect()
                  setDayPopup({
                    date: day.date,
                    content: day.content,
                    position: { x: rect.right + 10, y: rect.top }
                  })
                }
              }}
            >
              {/* Today highlight overlay - Enhanced visibility */}
              {day.isToday && (
                <div
                  className="absolute inset-0 pointer-events-none"
                  style={{
                    margin: '1px',
                    background: 'linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(99, 102, 241, 0.12) 100%)',
                    border: '2px solid rgba(99, 102, 241, 0.6)',
                    borderRadius: '8px',
                    boxShadow: `
                      0 0 0 1px rgba(99, 102, 241, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.05)
                    `
                  }}
                />
              )}

              {/* Content container */}
              <div className="p-3 flex flex-col h-full relative z-10">
                {/* Day Number - Elegant styling */}
                <div className={cn(
                  'text-sm font-medium mb-3',
                  day.isCurrentMonth ? 'text-text-secondary' : 'text-text-quaternary',
                  day.isToday && 'text-brand-500 font-semibold'
                )}>
                  {day.dayNumber}
                </div>
                {/* Content Entries - Elegant Cards with Campaign Tags */}
                <div className="space-y-1 flex-1 overflow-hidden">
                  {day.content.slice(0, 2).map((item) => {
                    const meta = getChannelMeta(item.channel)

                    return (
                      <div
                        key={item.id}
                        className={cn(
                          'rounded-lg p-2 cursor-pointer text-xs transition-all duration-200',
                          'hover:shadow-dark-sm shadow-sm hover:shadow-md'
                        )}
                        style={{
                          // Top-to-bottom gradient with subtle contrast and fading border effect using box-shadow
                          background: 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)',
                          boxShadow: `
                            0 1px 3px rgba(0, 0, 0, 0.3),
                            0 0 0 1px rgba(255, 255, 255, 0.08),
                            inset 0 1px 0 rgba(255, 255, 255, 0.06),
                            inset 0 -1px 0 rgba(0, 0, 0, 0.2)
                          `
                        }}
                        title={`${item.title} (${item.sourceType === 'quick_post' ? 'Quick Post' : item.source})`}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleContentClick(item.id)
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'linear-gradient(to bottom, #383838 0%, #2f2f2f 100%)'
                          e.currentTarget.style.boxShadow = `
                            0 2px 6px rgba(0, 0, 0, 0.4),
                            0 0 0 1px rgba(99, 102, 241, 0.3),
                            inset 0 1px 0 rgba(255, 255, 255, 0.1),
                            inset 0 -1px 0 rgba(0, 0, 0, 0.3)
                          `
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)'
                          e.currentTarget.style.boxShadow = `
                            0 1px 3px rgba(0, 0, 0, 0.3),
                            0 0 0 1px rgba(255, 255, 255, 0.08),
                            inset 0 1px 0 rgba(255, 255, 255, 0.06),
                            inset 0 -1px 0 rgba(0, 0, 0, 0.2)
                          `
                        }}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          <div className="w-3 h-3 rounded flex items-center justify-center" style={{ background: meta.gradient }}>
                            <meta.Icon className="w-2 h-2 text-white" />
                          </div>
                          <span className="truncate font-medium flex-1 text-text-primary text-xs">
                            {item.title}
                          </span>
                          <div className="flex-shrink-0">
                            <div className={cn('w-1.5 h-1.5 rounded-full', item.status === 'published' ? 'bg-success-500' : item.status === 'scheduled' ? 'bg-info-500' : item.status === 'changes_needed' ? 'bg-error-500' : 'bg-warning-500')} />
                          </div>
                        </div>

                        {/* Campaign Tag */}
                        <div className="flex items-center gap-1">
                          <span className={cn(
                            'text-xs px-1.5 py-0.5 rounded text-text-tertiary',
                            item.sourceType === 'campaign'
                              ? 'bg-blue-500/20 text-blue-400'
                              : 'bg-purple-500/20 text-purple-400'
                          )}>
                            {item.sourceType === 'quick_post' ? 'Quick Post' : item.source}
                          </span>
                        </div>
                      </div>
                    )
                  })}

                  {/* Show "+X more" if there are more entries - Enhanced styling */}
                  {day.content.length > 2 && (
                    <div
                      className={cn(
                        'text-xs text-text-tertiary hover:text-brand-400 px-2 py-1.5 rounded-lg',
                        'cursor-pointer transition-all duration-200'
                      )}
                      style={{
                        background: 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)',
                        boxShadow: `
                          0 1px 2px rgba(0, 0, 0, 0.2),
                          0 0 0 1px rgba(255, 255, 255, 0.15),
                          inset 0 1px 0 rgba(255, 255, 255, 0.05)
                        `,
                        borderRadius: '8px',
                        position: 'relative'
                      }}
                      onClick={(e) => {
                        e.stopPropagation()
                        const rect = e.currentTarget.getBoundingClientRect()
                        setDayPopup({
                          date: day.date,
                          content: day.content,
                          position: { x: rect.right + 10, y: rect.top }
                        })
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(to bottom, #383838 0%, #2f2f2f 100%)'
                        e.currentTarget.style.boxShadow = `
                          0 1px 2px rgba(0, 0, 0, 0.2),
                          0 0 0 1px rgba(99, 102, 241, 0.4),
                          inset 0 1px 0 rgba(255, 255, 255, 0.08)
                        `
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)'
                        e.currentTarget.style.boxShadow = `
                          0 1px 2px rgba(0, 0, 0, 0.2),
                          0 0 0 1px rgba(255, 255, 255, 0.15),
                          inset 0 1px 0 rgba(255, 255, 255, 0.05)
                        `
                      }}
                    >
                      +{day.content.length - 2} more
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderWeeklyCalendarView = () => {
    return (
      <div className="space-y-6">
        {/* Week Navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={goToPreviousWeek}
              className="w-8 h-8 p-0 rounded-full"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            
            <div className="text-center">
              <h3 className="text-sm font-semibold text-text-primary">
                {weeklyDays[0]?.monthName} {weeklyDays[0]?.dayNumber} - {weeklyDays[6]?.monthName} {weeklyDays[6]?.dayNumber}, {selectedDate.getFullYear()}
              </h3>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={goToNextWeek}
              className="w-8 h-8 p-0 rounded-full"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          <Button
            variant="secondary"
            size="sm"
            onClick={goToToday}
          >
            Today
          </Button>
        </div>

        {/* Day-by-Day Content */}
        <div className="space-y-6">
            {weeklyDays.map((day) => {
              const hasContent = day.content.length > 0
              const isToday = day.isToday

              return (
                <div key={day.date.toDateString()}>
                  {/* Day Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className={cn(
                        "text-sm font-semibold",
                        hasContent
                          ? (isToday ? "text-green-400" : "text-text-primary")
                          : "text-text-quaternary"
                      )}>
                        {day.dayName}
                      </h4>
                      <p className={cn(
                        "text-xs",
                        hasContent
                          ? (isToday ? "text-green-500" : "text-text-tertiary")
                          : "text-text-quaternary"
                      )}>
                        {day.dayNumber} {day.monthName}
                      </p>
                    </div>
                  </div>

                  {/* Content Items */}
                  <div className="space-y-3">
                    {day.content.length === 0 ? (
                      <div className="text-xs text-text-quaternary italic py-8 text-center border-2 border-dashed border-dark-quaternary rounded-lg">
                        No content planned for this day
                      </div>
                    ) : (
                      day.content.map((item) => {
                        const meta = getChannelMeta(item.channel)

                        return (
                          <div
                            key={item.id}
                            className="border border-dark-quaternary rounded-lg p-4 hover:shadow-dark-sm transition-shadow cursor-pointer"
                            onClick={() => handleContentClick(item.id)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex items-start gap-3 flex-1">
                                {/* Channel Icon */}
                                <div className={cn(
                                  "w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0",
                                  getChannelBgColor(item.channel)
                                )}>
                                  <meta.Icon className={cn("w-5 h-5", getChannelColor(item.channel))} />
                                </div>

                                {/* Content Details */}
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-2">
                                    <span className="text-sm font-medium text-text-primary">{item.channel}</span>
                                    <span className="text-sm text-text-tertiary">•</span>
                                    <span className="text-sm text-text-secondary">{item.type}</span>
                                  </div>

                                  <h5 className="text-sm font-medium text-text-primary leading-snug mb-2">{item.title}</h5>

                                  {/* Source Hierarchy */}
                                  <div className="flex items-center gap-2 mb-3">
                                    <span className={cn(
                                      "inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-xs font-medium",
                                      item.sourceType === 'campaign'
                                        ? "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                                        : "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                                    )}>
                                      {item.sourceType === 'campaign' ?
                                        <Megaphone className="w-3 h-3" /> :
                                        <Zap className="w-3 h-3" />
                                      }
                                      {item.sourceType === 'quick_post' ? 'Quick Post' : item.source}
                                    </span>
                                  </div>

                                  {/* Status */}
                                  <Status status={item.status} type="content" />
                                </div>
                              </div>

                              {/* Actions */}
                              <div className="flex items-center gap-2 ml-4">
                                <button
                                  onClick={(e) => e.stopPropagation()}
                                  className="p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-dark-tertiary transition-colors"
                                >
                                  <MoreVertical className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        )
                      })
                    )}
                  </div>
                </div>
              )
            })}
        </div>
      </div>
    )
  }

  // List view with table - "All" view (matching Campaigns.tsx structure)
  const renderAllContentView = () => {
    // Pagination logic
    const totalItems = filteredContent.length
    const totalPages = Math.ceil(totalItems / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedContent = filteredContent.slice(startIndex, endIndex)

    return (
      <div className="space-y-4">
        <div className="card overflow-hidden p-2 md:p-3">
          {/* Professional Table Structure */}
          <table className="w-full table-fixed">
            <thead className="border-b border-dark-quaternary">
              <tr>
                <th className="px-6 py-4 text-left" style={{ width: '280px' }}>
                  <button
                    onClick={() => handleSort('title')}
                    className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors"
                  >
                    Content
                    {sortField === 'title' && (
                      sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </button>
                </th>
                <th className="px-6 py-4 text-left" style={{ width: '120px' }}>
                  <span className="text-xs font-medium text-text-secondary uppercase tracking-wider">Channel</span>
                </th>
                <th className="px-6 py-4 text-left" style={{ width: '180px' }}>
                  <button
                    onClick={() => handleSort('source')}
                    className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors"
                  >
                    Source
                    {sortField === 'source' && (
                      sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </button>
                </th>
                <th className="px-6 py-4 text-left" style={{ width: '140px' }}>
                  <button
                    onClick={() => handleSort('status')}
                    className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors"
                  >
                    Status
                    {sortField === 'status' && (
                      sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </button>
                </th>
                <th className="px-6 py-4 text-right" style={{ width: '120px' }}>
                  <button
                    onClick={() => handleSort('date')}
                    className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors ml-auto"
                  >
                    Date
                    {sortField === 'date' && (
                      sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </button>
                </th>
                <th className="px-6 py-4 text-center" style={{ width: '150px' }}>
                  <span className="text-xs font-medium text-text-secondary uppercase tracking-wider">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-quaternary">
              {paginatedContent.map((item) => {
                const meta = getChannelMeta(item.channel)

                return (
                  <tr key={item.id} onClick={() => handleContentClick(item.id)} className="hover:bg-dark-tertiary transition-colors cursor-pointer">
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        <div className="campaign-list-icon" style={{ background: meta.gradient }}>
                          <meta.Icon className="w-4 h-4 text-white" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-text-primary text-sm truncate">{item.title}</div>
                          <div className="text-xs font-normal text-text-tertiary mt-0.5 truncate">{item.type}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <span
                          className="w-5 h-5 rounded-full flex items-center justify-center"
                          style={{ background: meta.gradient }}
                        >
                          <meta.Icon className="w-3 h-3 text-white" />
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={cn(
                        "inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-xs font-medium border",
                        item.sourceType === 'campaign'
                          ? "bg-blue-500/20 text-blue-400 border-blue-500/30"
                          : "bg-purple-500/20 text-purple-400 border-purple-500/30"
                      )}>
                        {item.sourceType === 'campaign' ?
                          <Megaphone className="w-3 h-3 flex-shrink-0" /> :
                          <Zap className="w-3 h-3 flex-shrink-0" />
                        }
                        <span className="truncate">
                          {item.sourceType === 'quick_post' ? 'Quick Post' : item.source}
                        </span>
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <Status status={item.status} type="content" />
                    </td>
                    <td className="px-6 py-4 text-right">
                      <span className="text-xs font-normal text-text-tertiary">{formatDate(item.date)}</span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center justify-center gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleContentClick(item.id);
                          }}
                          className="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-text-tertiary border border-transparent hover:text-success-500 hover:border-success-500 hover:bg-success-500/10 rounded-md transition-colors"
                        >
                          <Eye className="w-3 h-3" />
                          View
                        </button>
                        <button
                          onClick={(e) => e.stopPropagation()}
                          className="p-1.5 rounded-md hover:bg-dark-quaternary transition-colors"
                        >
                          <MoreVertical className="w-4 h-4 text-text-tertiary" />
                        </button>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>

        {/* Pagination - Always show for testing */}
        {(() => {
          return (
            <div className="flex items-center justify-between px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg">
              <div className="flex items-center gap-2 text-sm text-text-tertiary">
                <span>Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} results</span>
                <span className="text-xs text-text-quaternary ml-2">
                  (Page {currentPage} of {Math.max(1, totalPages)})
                </span>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1.5 text-sm font-medium text-text-secondary bg-dark-tertiary border border-dark-quaternary rounded-md hover:bg-dark-quaternary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Previous
                </button>
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.max(1, totalPages) }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={cn(
                        "px-3 py-1.5 text-sm font-medium rounded-md transition-colors",
                        currentPage === page
                          ? "bg-brand-500 text-white"
                          : "text-text-secondary bg-dark-tertiary border border-dark-quaternary hover:bg-dark-quaternary"
                      )}
                    >
                      {page}
                    </button>
                  ))}
                </div>
                <button
                  onClick={() => setCurrentPage(Math.min(Math.max(1, totalPages), currentPage + 1))}
                  disabled={currentPage === totalPages || totalPages <= 1}
                  className="px-3 py-1.5 text-sm font-medium text-text-secondary bg-dark-tertiary border border-dark-quaternary rounded-md hover:bg-dark-quaternary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Next
                </button>
              </div>
            </div>
          )
        })()}
      </div>
    )
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'month':
        return renderMonthlyCalendarView()
      case 'week':
        return renderWeeklyCalendarView()
      case 'all':
        return renderAllContentView()
      default:
        return renderMonthlyCalendarView()
    }
  }

  // If creating quick post, show wizard
  if (isCreatingQuickPost) {
    return (
      <QuickPostWizard
        onBack={handleQuickPostBack}
        onNext={handleQuickPostGenerate}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header - Dark Theme */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">Schedule</h1>
          <p className="text-sm text-text-tertiary mt-1">
            View all your scheduled content from campaigns and quick posts
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="gradient"
            size="sm"
            icon={Sparkles}
            className="shadow-glow"
            onClick={handleCreateQuickPost}
          >
            Quick Post
          </Button>
        </div>
      </div>

      {/* Navigation Bar - Dark Theme */}
      <div className="card px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-quaternary w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search content or campaigns..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input pl-10 pr-4 py-2 w-64"
                />
              </div>

              {/* Source Filter */}
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setSourceOpen(!sourceOpen)}
                  className={cn(
                    'brand-selector-btn flex items-center justify-between px-3 py-2 text-sm min-w-[140px]',
                    (sourceOpen || sourceFilter !== 'all') && 'brand-selector-selected'
                  )}
                >
                  <span className={cn(
                    sourceFilter === 'all' && !sourceOpen ? 'text-text-secondary' : 'text-white'
                  )}>
                    {sourceOptions.find(opt => opt.value === sourceFilter)?.label}
                  </span>
                  <ChevronDown className={cn(
                    'w-4 h-4 transition-transform ml-2',
                    sourceOpen || sourceFilter !== 'all' ? 'text-white' : 'text-text-tertiary',
                    sourceOpen && 'rotate-180'
                  )} />
                </button>
                {sourceOpen && (
                  <div className="dropdown-panel absolute top-full left-0 right-0 mt-1 z-20">
                    <div className="p-2">
                      {sourceOptions.map(option => (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => {
                            setSourceFilter(option.value)
                            setSourceOpen(false)
                          }}
                          className={cn(
                            "dropdown-item",
                            sourceFilter === option.value && "dropdown-item-active"
                          )}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Status Filter */}
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setStatusOpen(!statusOpen)}
                  className={cn(
                    'brand-selector-btn flex items-center justify-between px-3 py-2 text-sm min-w-[140px]',
                    (statusOpen || statusFilter !== 'all') && 'brand-selector-selected'
                  )}
                >
                  <span className={cn(
                    statusFilter === 'all' && !statusOpen ? 'text-text-secondary' : 'text-white'
                  )}>
                    {statusOptions.find(opt => opt.value === statusFilter)?.label}
                  </span>
                  <ChevronDown className={cn(
                    'w-4 h-4 transition-transform ml-2',
                    statusOpen || statusFilter !== 'all' ? 'text-white' : 'text-text-tertiary',
                    statusOpen && 'rotate-180'
                  )} />
                </button>
                {statusOpen && (
                  <div className="dropdown-panel absolute top-full left-0 right-0 mt-1 z-20">
                    <div className="p-2">
                      {statusOptions.map(option => (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => {
                            setStatusFilter(option.value)
                            setStatusOpen(false)
                          }}
                          className={cn(
                            "dropdown-item",
                            statusFilter === option.value && "dropdown-item-active"
                          )}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Channel Filter */}
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setChannelOpen(!channelOpen)}
                  className={cn(
                    'brand-selector-btn flex items-center justify-between px-3 py-2 text-sm min-w-[140px]',
                    (channelOpen || channelFilter !== 'all') && 'brand-selector-selected'
                  )}
                >
                  <span className={cn(
                    channelFilter === 'all' && !channelOpen ? 'text-text-secondary' : 'text-white'
                  )}>
                    {channelOptions.find(opt => opt.value === channelFilter)?.label}
                  </span>
                  <ChevronDown className={cn(
                    'w-4 h-4 transition-transform ml-2',
                    channelOpen || channelFilter !== 'all' ? 'text-white' : 'text-text-tertiary',
                    channelOpen && 'rotate-180'
                  )} />
                </button>
                {channelOpen && (
                  <div className="dropdown-panel absolute top-full left-0 right-0 mt-1 z-20">
                    <div className="p-2">
                      {channelOptions.map(option => (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => {
                            setChannelFilter(option.value)
                            setChannelOpen(false)
                          }}
                          className={cn(
                            "dropdown-item",
                            channelFilter === option.value && "dropdown-item-active"
                          )}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* View Toggle */}
            <div className="view-toggle">
              <button
                onClick={() => setViewMode('week')}
                className={cn(
                  'px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-150',
                  viewMode === 'week' ? 'view-toggle-btn-active' : 'text-text-tertiary hover:text-text-primary'
                )}
              >
                Week
              </button>
              <button
                onClick={() => setViewMode('month')}
                className={cn(
                  'px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-150',
                  viewMode === 'month' ? 'view-toggle-btn-active' : 'text-text-tertiary hover:text-text-primary'
                )}
              >
                Month
              </button>
              <button
                onClick={() => setViewMode('all')}
                className={cn(
                  'px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-150',
                  viewMode === 'all' ? 'view-toggle-btn-active' : 'text-text-tertiary hover:text-text-primary'
                )}
              >
                All
              </button>
            </div>
          </div>
        </div>

      {/* Content */}
      {renderContent()}

      {/* Day Popup - Redesigned to match CampaignDetails */}
      {dayPopup && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
            onClick={() => setDayPopup(null)}
          />

          {/* Popup Content */}
          <div
            className="fixed z-50 w-80 max-h-96 overflow-hidden"
            style={{
              left: `${Math.min(dayPopup.position.x, window.innerWidth - 320)}px`,
              top: `${Math.min(dayPopup.position.y, window.innerHeight - 400)}px`,
              background: 'linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 50%, #1a1a1a 100%)',
              borderRadius: '16px',
              boxShadow: `
                0 8px 32px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2)
              `
            }}
          >
            {/* Header */}
            <div className="p-4 border-b border-dark-quaternary/30">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-text-primary">
                  {dayPopup.date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric'
                  })}
                </h3>
                <button
                  onClick={() => setDayPopup(null)}
                  className="w-6 h-6 rounded-full bg-dark-quaternary/50 hover:bg-dark-quaternary/70
                           text-text-tertiary hover:text-text-primary transition-all duration-200
                           flex items-center justify-center text-sm font-medium"
                >
                  ×
                </button>
              </div>
              <p className="text-xs text-text-tertiary mt-1">
                {dayPopup.content.length} post{dayPopup.content.length !== 1 ? 's' : ''} scheduled
              </p>
            </div>
            {/* Content List */}
            <div className="p-4 space-y-3 max-h-80 overflow-y-auto">
              {dayPopup.content.map((item) => {
                const meta = getChannelMeta(item.channel)

                return (
                  <div
                    key={item.id}
                    className={cn(
                      'rounded-lg p-3 cursor-pointer transition-all duration-200',
                      'hover:shadow-dark-sm'
                    )}
                    style={{
                      // Top-to-bottom gradient with subtle contrast and fading border effect using box-shadow
                      background: 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)',
                      boxShadow: `
                        0 1px 3px rgba(0, 0, 0, 0.3),
                        0 0 0 1px rgba(255, 255, 255, 0.08),
                        inset 0 1px 0 rgba(255, 255, 255, 0.06),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.2)
                      `
                    }}
                    onClick={() => {
                      handleContentClick(item.id)
                      setDayPopup(null)
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(to bottom, #383838 0%, #2f2f2f 100%)'
                      e.currentTarget.style.boxShadow = `
                        0 2px 6px rgba(0, 0, 0, 0.4),
                        0 0 0 1px rgba(99, 102, 241, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.3)
                      `
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)'
                      e.currentTarget.style.boxShadow = `
                        0 1px 3px rgba(0, 0, 0, 0.3),
                        0 0 0 1px rgba(255, 255, 255, 0.08),
                        inset 0 1px 0 rgba(255, 255, 255, 0.06),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.2)
                      `
                    }}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-3 h-3 rounded flex items-center justify-center" style={{ background: meta.gradient }}>
                        <meta.Icon className="w-2 h-2 text-white" />
                      </div>
                      <span className="truncate font-medium flex-1 text-text-primary text-xs">
                        {item.title}
                      </span>
                      <div className="flex-shrink-0">
                        <div className={cn('w-1.5 h-1.5 rounded-full', item.status === 'published' ? 'bg-success-500' : item.status === 'scheduled' ? 'bg-info-500' : item.status === 'changes_needed' ? 'bg-error-500' : 'bg-warning-500')} />
                      </div>
                    </div>

                    {/* Campaign Tag */}
                    <div className="flex items-center gap-1">
                      <span className={cn(
                        'text-xs px-1.5 py-0.5 rounded text-text-tertiary',
                        item.sourceType === 'campaign'
                          ? 'bg-blue-500/20 text-blue-400'
                          : 'bg-purple-500/20 text-purple-400'
                      )}>
                        {item.sourceType === 'quick_post' ? 'Quick Post' : item.source}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Calendar 