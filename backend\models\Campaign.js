import mongoose from 'mongoose';

// Campaign Schema
const campaignSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  brandId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Brand',
    required: [true, 'Brand ID is required']
  },
  
  // Basic Campaign Information
  name: {
    type: String,
    required: [true, 'Campaign name is required'],
    trim: true
  },
  purpose: {
    type: String,
    required: [true, 'Campaign purpose is required'],
    trim: true
  },
  callToAction: {
    type: String,
    required: false, // Make it optional since not all campaigns need a specific CTA
    trim: true,
    default: 'Visit our website'
  },
  instructions: {
    type: String,
    trim: true
  },
  
  // Campaign Timeline
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required']
  },
  
  // Campaign Settings
  targetChannels: [{
    type: String,
    trim: true
  }],
  numberOfPosts: {
    type: Number,
    default: 0
  },
  
  // Campaign Status
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'completed', 'cancelled'],
    default: 'draft'
  },
  progress: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  
  // AI Generated Data
  researchInsights: {
    industryTrends: [{
      trend: String,
      description: String
    }],
    competitiveAnalysis: [{
      title: String,
      description: String
    }],
    audienceBehavior: [{
      behavior: String,
      description: String
    }]
  },
  
  campaignStrategy: {
    campaignThemes: [{
      theme: String,
      description: String,
      tags: [String]
    }],
    channelSpecificStrategies: [{
      channel: String,
      approach: String,
      contentTypes: [String],
      contentFrequency: String,
      targetAudience: String
    }]
  },
  
  contentCalendar: [{
    date: Date,
    channel: String,
    contentType: String,
    topic: String,
    hashtags: [String],
    status: {
      type: String,
      enum: ['planned', 'created', 'approved', 'published'],
      default: 'planned'
    }
  }],
  
  // Generated Content from AI - Optimized schema without field conflicts
  generatedContent: [{
    // Common fields (included in ALL content types)
    date: {
      type: String,
      required: true
    },
    channel: {
      type: String,
      required: true
    },
    contentType: {
      type: String,
      required: true,
      enum: ['Article', 'Post', 'Poll', 'Email', 'Video', 'Thread', 'PressRelease', 'Carousel']
    },
    topic: {
      type: String,
      required: true
    },
    hashtags: {
      type: [String],
      required: true
    },

    // Content-specific fields (varies by content type)
    // Using Mixed type to store the exact structure returned by AI API

    // For Post (Facebook, Instagram, LinkedIn, Twitter):
    title: String,
    content: String,

    // For Article (Medium, Blog/Website):
    subheading: String,
    sections: mongoose.Schema.Types.Mixed, // Will store different structures based on contentType
    summary: String,

    // For Poll (Social platforms with poll support):
    question: String,
    options: [{
      option: {
        type: String,
        required: true
      },
      votes: {
        type: Number,
        required: true
      }
    }],
    expirationDate: String,

    // For Email (Email/Newsletter content):
    subject: String,
    body: String,
    preheader: String,
    footer: String,

    // For Video (YouTube, Instagram, Facebook video):
    desc: String,
    videoScript: {
      intro: {
        title: String,
        startTime: String,
        desc: String
      },
      main: [{
        title: String,
        startTime: String,
        desc: String
      }],
      outro: {
        title: String,
        startTime: String,
        desc: String
      }
    },

    // For Thread (Twitter/X and other threaded content):
    thread: [{
      content: {
        type: String,
        required: true
      }
    }],

    // For Press Release (Press release content):
    headline: String,
    subheadline: String,
    dateline: String,
    lead_paragraph: String,
    body_sections: [{
      heading: String,
      content: String
    }],
    boilerplate: String,
    contact_info: {
      name: String,
      email: String,
      phone: String
    },

    // For Carousel (Instagram, LinkedIn, Facebook carousel posts):
    slides: [{
      title: String,
      content: String,
      image_description: String
    }],
    call_to_action: String,

    // Content status
    status: {
      type: String,
      enum: ['generated', 'reviewed', 'approved', 'published'],
      default: 'generated'
    },
    generatedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // File References
  campaignFiles: [String],
  selectedBrandFiles: [String],
  
  // Metadata
  isAIGenerated: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
campaignSchema.index({ userId: 1 });
campaignSchema.index({ brandId: 1 });
campaignSchema.index({ status: 1 });
campaignSchema.index({ startDate: 1, endDate: 1 });

// Virtual for content count
campaignSchema.virtual('contentCount').get(function() {
  return this.contentCalendar ? this.contentCalendar.length : 0;
});

// Virtual for due date formatting
campaignSchema.virtual('dueDate').get(function() {
  if (!this.endDate) return '';
  const now = new Date();
  const end = new Date(this.endDate);
  const diffTime = end - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) return 'Overdue';
  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return 'Tomorrow';
  if (diffDays < 7) return `${diffDays} days`;
  
  return end.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
});

// Static method to find campaigns by user ID
campaignSchema.statics.findByUserId = function(userId) {
  return this.find({ userId }).populate('brandId', 'name industry').sort({ updatedAt: -1 });
};

// Static method to find campaigns by brand ID
campaignSchema.statics.findByBrandId = function(brandId) {
  return this.find({ brandId }).populate('brandId', 'name industry').sort({ updatedAt: -1 });
};

// Instance method to check if campaign is active
campaignSchema.methods.isActive = function() {
  const now = new Date();
  return this.status === 'active' && 
         new Date(this.startDate) <= now && 
         new Date(this.endDate) >= now;
};

// Instance method to calculate progress
campaignSchema.methods.calculateProgress = function() {
  if (!this.contentCalendar || this.contentCalendar.length === 0) return 0;
  
  const completedContent = this.contentCalendar.filter(
    content => content.status === 'published' || content.status === 'approved'
  ).length;
  
  return Math.round((completedContent / this.contentCalendar.length) * 100);
};

const Campaign = mongoose.model('Campaign', campaignSchema);

export default Campaign;