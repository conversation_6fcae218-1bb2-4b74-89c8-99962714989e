import React, { useState } from 'react'
import { ChevronLeft, ChevronRight, Image as ImageIcon, ExternalLink } from 'lucide-react'
import MarkdownRenderer from '@/components/MarkdownRenderer'
import { formatDate } from '@/utils/formatDate'

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = React.useState(false)

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ]
    const index = name.charCodeAt(0) % colors.length
    return colors[index]
  }

  if (imageError || !src) {
    return (
      <div className={`${className} ${getAvatarColor(name)} rounded-full flex items-center justify-center text-white font-semibold`}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={`${className} rounded-full object-cover`}
      onError={() => setImageError(true)}
    />
  )
}

interface CarouselSlide {
  title: string
  content: string
  image_description?: string
}

interface CarouselContent {
  title: string
  slides: CarouselSlide[]
  call_to_action?: string
  hashtags?: string[]
}

interface CarouselPreviewProps {
  content: CarouselContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const CarouselPreview: React.FC<CarouselPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  const [currentSlide, setCurrentSlide] = useState(0)

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % content.slides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + content.slides.length) % content.slides.length)
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  return (
    <div className="card">
      {/* Author Header */}
      <div className="flex items-center gap-3 mb-4">
        <Avatar
          src={createdBy.avatar}
          name={createdBy.name}
          className="w-12 h-12"
        />
        <div className="flex-1">
          <div className="font-semibold text-text-primary">{createdBy.name}</div>
          <div className="text-sm text-text-tertiary">
            {formatDate(scheduledDate || new Date().toISOString())}
          </div>
        </div>
      </div>

      {/* Carousel Title */}
      {content.title && (
        <h2 className="text-xl font-semibold text-text-primary mb-4">
          {content.title}
        </h2>
      )}

      {/* Carousel Container */}
      <div className="relative bg-dark-secondary rounded-lg overflow-hidden mb-4">
        {/* Slide Content */}
        {content.slides && content.slides.length > 0 && (
          <div className="aspect-square relative">
            {/* Image Placeholder Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-slate-700 to-slate-900 flex items-center justify-center">
              {content.slides[currentSlide].image_description ? (
                <div className="text-center p-6">
                  <ImageIcon className="w-12 h-12 text-slate-400 mx-auto mb-3" />
                  <p className="text-slate-300 text-sm max-w-xs">
                    {content.slides[currentSlide].image_description}
                  </p>
                </div>
              ) : (
                <ImageIcon className="w-16 h-16 text-slate-500" />
              )}
            </div>

            {/* Slide Text Content - Better positioned and styled */}
            <div className="absolute inset-0 flex flex-col">
              {/* Top section for title */}
              <div className="flex-1 flex items-center justify-center p-6">
                <div className="text-center bg-black/60 backdrop-blur-sm rounded-lg p-4 max-w-sm">
                  <h3 className="text-white font-bold text-xl mb-3 leading-tight">
                    {content.slides[currentSlide].title}
                  </h3>
                </div>
              </div>

              {/* Bottom section for content */}
              <div className="bg-gradient-to-t from-black/90 via-black/70 to-transparent p-6">
                <div className="text-white/95 text-sm leading-relaxed">
                  <MarkdownRenderer content={content.slides[currentSlide].content} />
                </div>
              </div>
            </div>

            {/* Navigation Arrows */}
            {content.slides.length > 1 && (
              <>
                <button
                  onClick={prevSlide}
                  className="absolute left-3 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/60 hover:bg-black/80 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-105"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute right-3 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/60 hover:bg-black/80 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-105"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </>
            )}

            {/* Slide Indicators */}
            {content.slides.length > 1 && (
              <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-2 bg-black/40 backdrop-blur-sm rounded-full px-3 py-2">
                {content.slides.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-2.5 h-2.5 rounded-full transition-all duration-200 ${
                      index === currentSlide ? 'bg-white scale-125' : 'bg-white/60 hover:bg-white/80'
                    }`}
                  />
                ))}
              </div>
            )}

            {/* Slide Counter */}
            <div className="absolute top-4 right-4 bg-black/60 backdrop-blur-sm text-white text-xs px-3 py-1.5 rounded-full font-medium">
              {currentSlide + 1} / {content.slides.length}
            </div>
          </div>
        )}
      </div>

      {/* Call to Action */}
      {content.call_to_action && (
        <div className="mb-4">
          <button className="w-full bg-brand-600 hover:bg-brand-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
            {content.call_to_action}
            <ExternalLink className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Hashtags */}
      {content.hashtags && content.hashtags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {content.hashtags.map((tag, index) => (
            <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
              {tag.startsWith('#') ? tag : `#${tag}`}
            </span>
          ))}
        </div>
      )}
    </div>
  )
}

export default CarouselPreview
