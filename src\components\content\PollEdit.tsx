import React from 'react'
import { X, Plus, Calendar } from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'

interface PollContent {
  question: string
  options: string[] // Simple array of option strings
  expirationDate?: string
  hashtags?: string[]
}

interface PollEditProps {
  content: PollContent
  editContent: PollContent
  setEditContent: (content: PollContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const PollEdit: React.FC<PollEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof PollContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateOption = (index: number, value: string) => {
    const updatedOptions = [...(editContent.options || [])]
    updatedOptions[index] = value
    updateField('options', updatedOptions)
  }

  const addOption = () => {
    updateField('options', [...(editContent.options || []), ''])
  }

  const removeOption = (index: number) => {
    const updatedOptions = (editContent.options || []).filter((_, i) => i !== index)
    updateField('options', updatedOptions)
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  return (
    <div className="space-y-6">
      {/* Poll Question */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Poll Question</label>
        <input
          type="text"
          value={editContent.question || ''}
          onChange={(e) => updateField('question', e.target.value)}
          className="w-full search-input"
          placeholder="What would you like to ask?"
        />
      </div>

      {/* Poll Options */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Poll Options</label>
          <Button
            onClick={addOption}
            variant="secondary"
            size="sm"
            className="flex items-center gap-2"
            disabled={(editContent.options || []).length >= 4}
          >
            <Plus className="w-4 h-4" />
            Add Option
          </Button>
        </div>
        
        <div className="space-y-3">
          {(editContent.options || []).map((option, index) => (
            <div key={index} className="flex items-center gap-3 p-3 bg-dark-secondary rounded-lg border border-dark-quaternary">
              <span className="text-sm text-text-tertiary font-medium min-w-[60px]">
                Option {index + 1}
              </span>
              <input
                type="text"
                value={option}
                onChange={(e) => updateOption(index, e.target.value)}
                className="flex-1 search-input"
                placeholder="Enter option text..."
              />
              {(editContent.options || []).length > 2 && (
                <button
                  onClick={() => removeOption(index)}
                  className="text-red-400 hover:text-red-300 p-1"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          ))}
        </div>
        
        {(editContent.options || []).length < 2 && (
          <p className="text-xs text-text-tertiary mt-2">
            A poll must have at least 2 options
          </p>
        )}
      </div>

      {/* Expiration Date */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">
          <Calendar className="w-4 h-4 inline mr-2" />
          Expiration Date (Optional)
        </label>
        <input
          type="datetime-local"
          value={editContent.expirationDate ? new Date(editContent.expirationDate).toISOString().slice(0, 16) : ''}
          onChange={(e) => updateField('expirationDate', e.target.value ? new Date(e.target.value).toISOString() : '')}
          className="w-full search-input"
        />
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="secondary"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 search-input"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default PollEdit
