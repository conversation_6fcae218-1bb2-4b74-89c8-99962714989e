import React, { useState, useRef, useEffect } from 'react'
import { useParams, useNavigate, useSearchParams } from 'react-router-dom'
import { useCampaignStore } from '@/stores/campaignStore'
import { api } from '@/stores/authStore'
import { useBackgroundProcessStore } from '@/stores/backgroundProcessStore'
import { backgroundAiService } from '@/services/backgroundAiService'
import { ArrowLeft, Calendar, MessageCircle, Check, AlertCircle, Sparkles, Copy, CheckCheck, ChevronRight, BarChart3, TrendingUp, Users, Eye, X, Plus, Minus, Save, RotateCcw, Lock } from 'lucide-react'
import { cn } from '@/utils/cn'
import { formatDate } from '@/utils/formatDate'
import { getAvatarColor, getInitials } from '@/utils/avatarUtils'
import { SocialContent, EmailContent, YouTubeContent, BlogContent, CarouselContent, TwitterContent } from '@/types/content'
import Button from '@/components/ui/Button'
import { Status } from '@/components/ui'
import type { ContentStatus } from '@/components/ui'
import MarkdownRenderer from '@/components/MarkdownRenderer'

// Import content components
import { EmailPreview, EmailEdit } from '@/components/content'
import { VideoPreview, VideoEdit } from '@/components/content'
import { ThreadPreview, ThreadEdit } from '@/components/content'
import { ArticlePreview, ArticleEdit } from '@/components/content'
import { PostPreview, PostEdit } from '@/components/content'
import { PollPreview, PollEdit } from '@/components/content'
import { CarouselPreview, CarouselEdit } from '@/components/content'
import { PressReleasePreview, PressReleaseEdit } from '@/components/content'

// LinkedIn Icon Component
const LinkedinIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
)

// YouTube Icon Component
const YouTubeIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
)

// Email Icon Component  
const EmailIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
  </svg>
)

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = useState(false)

  if (!src || imageError) {
    return (
      <div className={cn(
        'flex items-center justify-center text-white font-semibold rounded-full',
        getAvatarColor(name),
        className
      )}>
        {getInitials(name)}
      </div>
    )
  }

  return (
    <img
      src={src}
      alt={name}
      className={cn('rounded-full', className)}
      onError={() => setImageError(true)}
    />
  )
}

// Platform Icon Component
const PlatformIcon = ({ platform, className }: { platform: string; className?: string }) => {
  switch (platform.toLowerCase()) {
    case 'linkedin':
      return <LinkedinIcon className={className} />
    case 'youtube':
      return <YouTubeIcon className={className} />
    case 'email':
      return <EmailIcon className={className} />
    case 'twitter':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
        </svg>
      )
    case 'facebook':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      )
    case 'instagram':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
        </svg>
      )
    case 'blog':
    case 'medium':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"/>
        </svg>
      )
    default:
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      )
  }
}

const PostDetail: React.FC = () => {
  const { id: campaignId, postId, quickPostId } = useParams()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { currentCampaign, fetchCampaign, isLoading, error } = useCampaignStore()

  // Determine if this is a quick post or campaign post
  const isQuickPost = !!quickPostId
  const [quickPost, setQuickPost] = useState<any>(null)
  const [quickPostLoading, setQuickPostLoading] = useState(false)
  const [quickPostError, setQuickPostError] = useState<string | null>(null)

  // Check if we came from calendar or campaign and get preserved filters
  const fromCalendar = searchParams.get('from') === 'calendar'
  const fromCampaign = searchParams.get('from') === 'campaign'
  const preservedFilters = {
    search: searchParams.get('search') || '',
    status: searchParams.get('status') || 'all',
    channel: searchParams.get('channel') || 'all',
    source: searchParams.get('source') || 'all',
    view: searchParams.get('view') || 'month',
    tab: searchParams.get('tab') || 'content'
  }
  
  // Fetch quick post data
  const fetchQuickPost = async (id: string) => {
    try {
      setQuickPostLoading(true)
      setQuickPostError(null)
      const response = await api.get(`/quick-posts/${id}`)
      setQuickPost(response.data.data)
    } catch (error: any) {
      console.error('Failed to fetch quick post:', error)
      setQuickPostError(error.response?.data?.message || 'Failed to fetch quick post')
    } finally {
      setQuickPostLoading(false)
    }
  }

  // Fetch campaign or quick post data when component mounts
  useEffect(() => {
    if (isQuickPost && quickPostId) {
      fetchQuickPost(quickPostId)
    } else if (campaignId) {
      fetchCampaign(campaignId)
    }
  }, [campaignId, quickPostId, isQuickPost, fetchCampaign])
  


  // Get post data from quick post
  const getPostFromQuickPost = () => {
    console.log('🔍 getPostFromQuickPost called with:', { quickPostId, hasContent: !!quickPost?.generatedContent });

    if (!quickPost?.generatedContent || quickPost.generatedContent.length === 0) {
      console.log('❌ No quick post content available');
      return null;
    }

    // For quick posts, we typically have one piece of content
    const generatedContent = quickPost.generatedContent[0];
    console.log('📄 Found quick post content:', !!generatedContent);

    if (!generatedContent) {
      console.log('❌ No content in quick post');
      return null;
    }

    // Transform the quick post data to match the expected post structure
    const transformedPost = {
      id: quickPostId,
      contentType: generatedContent.contentType, // Use actual contentType (Video, Email, etc.)
      platform: quickPost.channel.toLowerCase(),
      status: generatedContent.status === 'generated' ? 'pending_review' : generatedContent.status,
      scheduledDate: quickPost.postDate,
      createdBy: {
        name: 'Quick Post',
        avatar: '',
        role: 'AI Generated'
      },
      content: transformContentData(generatedContent), // Use the same transformation logic as campaigns
      hashtags: generatedContent.hashtags || [],
      metrics: {
        likes: 0,
        comments: 0,
        shares: 0,
        views: 0
      },
      comments: [], // Initialize empty comments array
      isQuickPost: true,
      quickPostData: quickPost
    };

    console.log('✅ Transformed quick post:', transformedPost.contentType);
    return transformedPost;
  };

  // Find the specific content item from the campaign's generated content
  const getPostFromCampaign = () => {
    console.log('🔍 getPostFromCampaign called with:', { postId, campaignId, hasContent: !!currentCampaign?.generatedContent });

    if (!currentCampaign?.generatedContent || !postId) {
      console.log('❌ No campaign content or postId available');
      return null;
    }

    // Handle different postId formats
    let contentIndex;

    // Check if postId is in format "campaignId-index" (from calendar)
    if (postId.includes('-')) {
      const parts = postId.split('-');
      const lastPart = parts[parts.length - 1];
      if (!isNaN(parseInt(lastPart))) {
        contentIndex = parseInt(lastPart); // Already 0-based from calendar
        console.log('📅 Calendar format postId detected, contentIndex:', contentIndex);
      } else {
        console.log('❌ Invalid calendar format postId');
        return null;
      }
    } else if (!isNaN(parseInt(postId))) {
      // Numeric postId (from campaign page)
      contentIndex = parseInt(postId) - 1; // postId is 1-based, array is 0-based
      console.log('🔢 Numeric postId detected, contentIndex:', contentIndex);
    } else {
      // If postId isn't in expected format, return null
      console.log('❌ Unknown postId format');
      return null;
    }

    const generatedContent = currentCampaign.generatedContent[contentIndex];
    console.log('📄 Found generated content:', !!generatedContent, generatedContent?.topic);
    console.log('🔍 Full generatedContent object:', JSON.stringify(generatedContent, null, 2));

    if (!generatedContent) {
      console.log('❌ No content at index', contentIndex);
      return null;
    }
    
    // Transform the real data to match the expected post structure
    const transformedPost = {
      id: postId,
      contentType: generatedContent.contentType, // Use actual contentType (Video, Email, etc.)
      platform: generatedContent.channel.toLowerCase(), // Add platform field for sidebar display
      status: generatedContent.status === 'generated' ? 'pending_review' : generatedContent.status,
      scheduledDate: generatedContent.date,
      createdBy: {
        id: 'creator-1',
        name: 'Sarah Chen',
        role: 'creator',
        avatar: '/api/placeholder/32/32'
      },
      content: transformContentData(generatedContent),
      comments: [], // Start with empty comments for now
      activity: []
    };

    console.log('✅ Transformed post data:', transformedPost);
    return transformedPost;
  }
  
  // Transform the generated content data to match UI expectations
  const transformContentData = (generatedContent: any) => {
    const channel = generatedContent.channel?.toLowerCase() || '';
    const contentType = generatedContent.contentType?.toLowerCase() || '';

    // For Post content type (any channel)
    if (channel === 'linkedin' && contentType === 'post') {
      return {
        title: generatedContent.title || generatedContent.topic,
        content: generatedContent.content || generatedContent.topic,
        hashtags: generatedContent.hashtags || [],
        images: [] // No images in generated content yet
      }
    }

    // For Article content type (any channel)
    if ((channel === 'linkedin' && contentType === 'article') || channel === 'medium' || contentType === 'article') {
      // Transform backend sections structure to frontend expected structure
      const transformedSections = generatedContent.sections ?
        generatedContent.sections.map((section: any, index: number) => ({
          id: `section-${index + 1}`,
          title: section.Introduction || section.title || `Section ${index + 1}`,
          content: section.Description || section.content || ''
        })) : [
          {
            id: 'section-1',
            title: generatedContent.title || generatedContent.topic || 'Article Content',
            content: generatedContent.content || generatedContent.summary || generatedContent.topic || ''
          }
        ]

      return {
        title: generatedContent.title || generatedContent.topic,
        subheading: generatedContent.subheading || '',
        sections: transformedSections,
        summary: generatedContent.summary || '',
        featuredImage: generatedContent.featuredImage || null
      }
    }
    
    // For Thread content type (any channel)
    if ((channel === 'twitter' || contentType === 'thread') && generatedContent.thread) {
      return {
        thread: generatedContent.thread.map((tweet: any, index: number) => ({
          content: tweet.content
        })),
        hashtags: generatedContent.hashtags || [],
        topic: generatedContent.topic || ''
      }
    }
    
    // For Video content type (any channel)
    if (channel === 'youtube' || contentType === 'video') {
      // Transform videoScript structure to match UI expectations
      let scriptSections = [];

      if (generatedContent.videoScript) {
        const videoScript = generatedContent.videoScript;

        // Add intro section
        if (videoScript.intro) {
          scriptSections.push({
            id: 'intro',
            type: 'intro',
            title: videoScript.intro.title,
            content: videoScript.intro.desc,
            timestamp: videoScript.intro.startTime
          });
        }

        // Add main sections
        if (videoScript.main && Array.isArray(videoScript.main)) {
          videoScript.main.forEach((section, index) => {
            scriptSections.push({
              id: `main-${index}`,
              type: 'main',
              title: section.title,
              content: section.desc,
              timestamp: section.startTime
            });
          });
        }

        // Add outro section
        if (videoScript.outro) {
          scriptSections.push({
            id: 'outro',
            type: 'outro',
            title: videoScript.outro.title,
            content: videoScript.outro.desc,
            timestamp: videoScript.outro.startTime
          });
        }
      }

      return {
        title: generatedContent.title || generatedContent.topic,
        desc: generatedContent.desc || generatedContent.description || generatedContent.topic,
        hashtags: generatedContent.hashtags || [],
        videoScript: generatedContent.videoScript || null,
        thumbnailUrl: generatedContent.thumbnailUrl || null
      }
    }
    
    // For Email content
    if (channel === 'email' || contentType === 'email') {
      const transformedEmail = {
        subject: generatedContent.subject || generatedContent.topic,
        preheader: generatedContent.preheader || '',
        body: generatedContent.body || generatedContent.topic,
        footer: generatedContent.footer || '',
        sections: generatedContent.sections || [],
        hashtags: generatedContent.hashtags || [],
        topic: generatedContent.topic || ''
      };

      return transformedEmail;
    }

    // For Poll content
    if (contentType === 'poll') {
      return {
        question: generatedContent.question || generatedContent.topic,
        options: generatedContent.options || [],
        expirationDate: generatedContent.expirationDate || null,
        hashtags: generatedContent.hashtags || [],
        topic: generatedContent.topic || ''
      };
    }

    // For PressRelease content
    if (contentType === 'pressrelease') {
      return {
        headline: generatedContent.headline || generatedContent.title || generatedContent.topic,
        subheadline: generatedContent.subheadline || '',
        dateline: generatedContent.dateline || '',
        lead_paragraph: generatedContent.lead_paragraph || generatedContent.body || '',
        body_sections: generatedContent.body_sections || [],
        boilerplate: generatedContent.boilerplate || '',
        contact_info: generatedContent.contact_info || {},
        hashtags: generatedContent.hashtags || []
      }
    }

    // For Carousel content
    if (contentType === 'carousel') {
      return {
        title: generatedContent.title || generatedContent.topic,
        slides: generatedContent.slides || [],
        call_to_action: generatedContent.call_to_action || '',
        hashtags: generatedContent.hashtags || []
      }
    }

    // Default fallback for other content types
    return {
      title: generatedContent.title || generatedContent.topic,
      content: generatedContent.content || generatedContent.topic,
      hashtags: generatedContent.hashtags || []
    }
  }
  
  const [post, setPost] = useState<any>(null)
  const user = {
    id: '1',
    name: 'Current User',
    role: 'creator' as const,
    avatar: ''
  }
  
  // Update post when campaign or quick post data is loaded
  useEffect(() => {
    if (isQuickPost) {
      console.log('🔄 Quick Post useEffect triggered:', { quickPostId, hasQuickPost: !!quickPost });
      const quickPostData = getPostFromQuickPost();
      console.log('📄 Got quick post data:', !!quickPostData, quickPostData?.contentType);
      if (quickPostData) {
        setPost(quickPostData);
        console.log('✅ Quick post set successfully');
      }
    } else {
      console.log('🔄 Campaign Post useEffect triggered:', { campaignId, postId, hasCampaign: !!currentCampaign });
      const campaignPost = getPostFromCampaign();
      console.log('📄 Got campaign post:', !!campaignPost, campaignPost?.contentType);
      if (campaignPost) {
        setPost(campaignPost);
        console.log('✅ Campaign post set successfully');
      }
    }
  }, [currentCampaign, postId, quickPost, quickPostId, isQuickPost])

  // Update editContent when post changes
  useEffect(() => {
    if (post?.content) {
      setEditContent(post.content)
    }
  }, [post])

  // Fetch content versions when post loads
  useEffect(() => {
    if (post && postId) {
      fetchContentVersions()
    }
  }, [post, postId])

  // Listen for background AI regeneration completion
  useEffect(() => {
    const handleAiContentRegenerated = (event: CustomEvent) => {
      const { postId: regeneratedPostId, campaignId: regeneratedCampaignId, isQuickPost: regeneratedIsQuickPost } = event.detail

      // Check if this event is for the current post
      const isCurrentPost = (
        (isQuickPost && regeneratedIsQuickPost && quickPostId === regeneratedPostId) ||
        (!isQuickPost && !regeneratedIsQuickPost && campaignId === regeneratedCampaignId && postId === regeneratedPostId)
      )

      if (isCurrentPost) {
        console.log('🔄 Background AI regeneration completed for current post, refreshing content...')
        // Refresh the post data and versions
        setTimeout(() => {
          fetchContentVersions()
          // Force re-fetch of post data if needed
          window.location.reload()
        }, 1000)
      }
    }

    window.addEventListener('ai-content-regenerated', handleAiContentRegenerated as EventListener)

    return () => {
      window.removeEventListener('ai-content-regenerated', handleAiContentRegenerated as EventListener)
    }
  }, [postId, campaignId, isQuickPost, quickPostId])
  
  // View mode state
  const [viewMode, setViewMode] = useState<'preview' | 'edit'>('preview')
  
  // Edit state - clone the current content for editing
  const [editContent, setEditContent] = useState(post?.content || null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Version management state
  const [contentVersions, setContentVersions] = useState<any[]>([])
  const [currentVersion, setCurrentVersion] = useState<number>(1)
  const [isLoadingVersions, setIsLoadingVersions] = useState(false)
  const [isSwitchingVersion, setIsSwitchingVersion] = useState(false)

  // Background process state
  const { getProcessesByType } = useBackgroundProcessStore()
  
  // Other existing state
  const [activeImageIndex, setActiveImageIndex] = useState(0)
  const [isAiModalOpen, setIsAiModalOpen] = useState(false)
  const [aiInstruction, setAiInstruction] = useState('')
  const [newComment, setNewComment] = useState('')
  const [isAiLoading, setIsAiLoading] = useState(false)
  const [copySuccess, setCopySuccess] = useState(false)
  const [reviewTab, setReviewTab] = useState<'open' | 'resolved'>('open')
  
  // New state for enhanced comments
  const [resolvingComment, setResolvingComment] = useState<string | null>(null)
  const [resolutionResponse, setResolutionResponse] = useState('')
  const [commentFilter, setCommentFilter] = useState<'all' | 'required_changes' | 'open' | 'resolved'>('all')

  // State for enhanced workflows
  const [isSubmitReviewModalOpen, setIsSubmitReviewModalOpen] = useState(false)
  
  // New state and ref for simplified request changes
  const [isRequestChange, setIsRequestChange] = useState(false)
  const commentSectionRef = useRef<HTMLDivElement>(null)

  // Utility functions
  const formatActivityDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getContentTitle = () => {
    if (!post || !post.content) return 'Loading...'

    // Handle quick posts where content is a string
    if (post.isQuickPost && typeof post.content === 'string') {
      return post.content.substring(0, 50) + (post.content.length > 50 ? '...' : '')
    }

    if (post.contentType.toLowerCase() === 'thread') {
      const threadContent = post.content as any
      return threadContent.thread?.[0]?.content?.substring(0, 50) + '...' || 'Thread'
    }

    if (post.contentType.toLowerCase() === 'video') {
      const videoContent = post.content as any
      return videoContent.title?.substring(0, 50) + '...' || 'Video'
    }

    if (post.contentType.toLowerCase() === 'article') {
      const articleContent = post.content as any
      return articleContent.title?.substring(0, 50) + '...' || 'Article'
    }

    if (post.contentType.toLowerCase() === 'post') {
      const postContent = post.content as any
      return postContent.content?.substring(0, 50) + '...' || 'Post'
    }

    if (post.contentType.toLowerCase() === 'poll') {
      const pollContent = post.content as any
      return pollContent.question?.substring(0, 50) + '...' || 'Poll'
    }

    if (post.contentType.toLowerCase() === 'carousel') {
      const carouselContent = post.content as any
      return carouselContent.title?.substring(0, 50) + '...' || 'Carousel'
    }

    if (post.contentType.toLowerCase() === 'pressrelease') {
      const pressReleaseContent = post.content as any
      return pressReleaseContent.headline?.substring(0, 50) + '...' || 'Press Release'
    }

    if (post.contentType.toLowerCase() === 'email') {
      const emailContent = post.content as any
      return emailContent.subject?.substring(0, 50) + '...' || 'Email'
    }

    // For campaign posts, content is an object
    if (typeof post.content === 'object' && post.content !== null && 'title' in post.content) {
      return post.content.title
    }

    return 'Untitled Content'
  }

  // Check if current user is a reviewer for this brand
  const isUserReviewer = () => {
    if (!currentCampaign?.brandId || !user) return false

    // Get brand data from campaign - brandId might be populated or just an ID
    const brand = currentCampaign.brandId

    // If brandId is populated with full brand data
    if (typeof brand === 'object' && brand.reviewers) {
      return brand.reviewers.some((reviewer: any) =>
        reviewer.userId === user.id || reviewer.email === user.email
      )
    }

    // For now, return false if brand data is not populated
    // TODO: Fetch brand data separately if needed
    return false
  }

  const getCommentTypeConfig = (type: string) => {
    switch (type) {
      case 'required_change':
        return {
          badge: 'bg-error-500/10 text-error-500 border-error-500/20',
          icon: <div className="w-1.5 h-1.5 bg-error-500 rounded-full" />,
          label: 'Required Change'
        }
      case 'blocker':
        return {
          badge: 'bg-error-500/20 text-error-400 border-error-500/30',
          icon: <div className="w-1.5 h-1.5 bg-error-400 rounded-full" />,
          label: 'Blocker'
        }
      case 'question':
        return {
          badge: 'bg-info-500/10 text-info-500 border-info-500/20',
          icon: <div className="w-1.5 h-1.5 bg-info-500 rounded-full" />,
          label: 'Question'
        }
      case 'comment':
      default:
        return {
          badge: 'bg-dark-tertiary text-text-secondary border-dark-quaternary',
          icon: <div className="w-1.5 h-1.5 bg-text-tertiary rounded-full" />,
          label: 'Comment'
        }
    }
  }

  const getCommentStatusConfig = (status: string) => {
    switch (status) {
      case 'resolved':
        return {
          badge: 'bg-success-500/10 text-success-500 border-success-500/20',
          icon: <Check className="w-3 h-3" />,
          label: 'Resolved'
        }
      case 'acknowledged':
        return {
          badge: 'bg-success-500/10 text-success-500 border-success-500/20',
          icon: <div className="w-1.5 h-1.5 bg-success-500 rounded-full" />,
          label: 'Acknowledged'
        }
      case 'open':
      default:
        return {
          badge: 'bg-warning-500/10 text-warning-500 border-warning-500/20',
          icon: <Lock className="w-3 h-3" />,
          label: 'Open'
        }
    }
  }

  // Handler functions
  const handleSaveChanges = async () => {
    if (!editContent || !post) return

    setIsSaving(true)

    try {
      console.log('💾 Saving content changes...', { editContent, postId, campaignId, isQuickPost })

      if (isQuickPost) {
        // Update quick post content
        const quickPostData = {
          generatedContent: quickPost.generatedContent.map((content: any, index: number) => {
            // Find the content index that matches our postId
            const contentIndex = parseInt(quickPostId.split('-').pop() || '0')
            if (index === contentIndex) {
              return {
                ...content,
                ...editContent,
                updatedAt: new Date().toISOString()
              }
            }
            return content
          })
        }

        const response = await api.put(`/quick-posts/${quickPost._id}`, quickPostData)
        console.log('✅ Quick post updated successfully:', response.data)

        // Update local state
        setQuickPost(response.data.data)

      } else {
        // Update campaign content
        if (!campaignId || !currentCampaign) {
          console.error('❌ Campaign ID or current campaign not available')
          return
        }

        // Check if postId is in format "campaignId-index" (from calendar)
        let contentIndex = 0
        if (postId && postId.includes('-')) {
          const parts = postId.split('-')
          const lastPart = parts[parts.length - 1]
          if (!isNaN(parseInt(lastPart))) {
            contentIndex = parseInt(lastPart) // Already 0-based from calendar
          }
        } else if (postId && !isNaN(parseInt(postId))) {
          // Numeric postId (from campaign page)
          contentIndex = parseInt(postId) - 1 // postId is 1-based, array is 0-based
        }

        const updatedGeneratedContent = [...(currentCampaign.generatedContent || [])]
        updatedGeneratedContent[contentIndex] = {
          ...updatedGeneratedContent[contentIndex],
          ...editContent,
          updatedAt: new Date().toISOString()
        }

        const campaignData = {
          generatedContent: updatedGeneratedContent
        }

        const response = await api.put(`/campaigns/${campaignId}`, campaignData)
        console.log('✅ Campaign updated successfully:', response.data)

        // Refresh campaign data
        if (campaignId) {
          await fetchCampaign(campaignId)
        }
      }

      setHasUnsavedChanges(false)
      setViewMode('preview')

      console.log('✅ Content saved and switched to preview mode')

    } catch (error) {
      console.error('❌ Error saving content:', error)
      // You could add a toast notification here for better UX
    } finally {
      setIsSaving(false)
    }
  }

  const handleDiscardChanges = () => {
    if (post?.content) {
      setEditContent(post.content)
    }
    setHasUnsavedChanges(false)
    setViewMode('preview')
  }

  const handleContentChange = (newContent: any) => {
    setEditContent(newContent)
    setHasUnsavedChanges(true)
  }

  // Version management functions
  const fetchContentVersions = async () => {
    if (!postId || !post) return

    setIsLoadingVersions(true)

    try {
      let endpoint = ''
      let contentIndex = 0

      if (isQuickPost) {
        const index = parseInt(quickPostId.split('-').pop() || '0')
        endpoint = `/quick-posts/${quickPost._id}/content/${index}/versions`
      } else {
        if (postId && postId.includes('-')) {
          const parts = postId.split('-')
          const lastPart = parts[parts.length - 1]
          if (!isNaN(parseInt(lastPart))) {
            contentIndex = parseInt(lastPart)
          }
        } else if (postId && !isNaN(parseInt(postId))) {
          contentIndex = parseInt(postId) - 1
        }
        endpoint = `/campaigns/${campaignId}/content/${contentIndex}/versions`
      }

      const response = await api.get(endpoint)
      const versionData = response.data.data

      // Ensure unique versions and sort them
      const versions = versionData.versions || []
      const uniqueVersions = versions.filter((version, index, self) =>
        index === self.findIndex(v => v.version === version.version)
      ).sort((a, b) => b.version - a.version) // Sort descending (newest first)

      setContentVersions(uniqueVersions)
      setCurrentVersion(versionData.currentVersion || 1)

    } catch (error) {
      console.error('❌ Error fetching content versions:', error)
    } finally {
      setIsLoadingVersions(false)
    }
  }

  const handleVersionSwitch = async (targetVersion: number) => {
    if (!postId || !post || targetVersion === currentVersion) return

    setIsSwitchingVersion(true)

    try {
      let endpoint = ''
      let contentIndex = 0

      if (isQuickPost) {
        const index = parseInt(quickPostId.split('-').pop() || '0')
        endpoint = `/quick-posts/${quickPost._id}/content/${index}/switch-version`
      } else {
        if (postId && postId.includes('-')) {
          const parts = postId.split('-')
          const lastPart = parts[parts.length - 1]
          if (!isNaN(parseInt(lastPart))) {
            contentIndex = parseInt(lastPart)
          }
        } else if (postId && !isNaN(parseInt(postId))) {
          contentIndex = parseInt(postId) - 1
        }
        endpoint = `/campaigns/${campaignId}/content/${contentIndex}/switch-version`
      }

      const response = await api.post(endpoint, { targetVersion })
      console.log('✅ Version switched successfully:', response.data)

      // Update current version
      setCurrentVersion(targetVersion)

      // Refresh the page data to show the new version
      if (isQuickPost) {
        // Refresh quick post data
        setQuickPost(response.data.data.quickPost)
      } else {
        // Refresh campaign data
        await fetchCampaign(campaignId!)
      }

      // Reset edit state
      setHasUnsavedChanges(false)
      setViewMode('preview')

    } catch (error) {
      console.error('❌ Error switching version:', error)
    } finally {
      setIsSwitchingVersion(false)
    }
  }

  const handleAiRegenerate = async () => {
    if (!aiInstruction.trim() || !post) return

    try {
      console.log('🤖 Starting background AI regeneration...', { postId, campaignId, isQuickPost })

      // Get current brand data
      const brandData = isQuickPost ? quickPost.brandId : currentCampaign?.brandId
      const brandId = typeof brandData === 'string' ? brandData : brandData?._id || brandData?.id

      if (!brandId) {
        throw new Error('Brand ID not found')
      }

      // Calculate content index for campaigns
      let contentIndex = 0
      if (!isQuickPost) {
        if (postId && postId.includes('-')) {
          const parts = postId.split('-')
          const lastPart = parts[parts.length - 1]
          if (!isNaN(parseInt(lastPart))) {
            contentIndex = parseInt(lastPart)
          }
        } else if (postId && !isNaN(parseInt(postId))) {
          contentIndex = parseInt(postId) - 1 // postId is 1-based, array is 0-based
        }
      }

      // Prepare regeneration data
      const regenerationData = {
        postId: postId!,
        campaignId: isQuickPost ? undefined : campaignId,
        contentIndex: isQuickPost ? undefined : contentIndex,
        isQuickPost,
        quickPostId: isQuickPost ? quickPostId : undefined,
        instruction: aiInstruction.trim(),
        brandData: brandData,
        originalContent: post.content
      }

      // Start background regeneration
      const processId = await backgroundAiService.startAiRegeneration(regenerationData)
      console.log('🔄 Background AI regeneration started with process ID:', processId)

      // Close modal and reset immediately (process continues in background)
      setAiInstruction('')
      setIsAiModalOpen(false)
      setIsAiLoading(false)

      console.log('✅ AI regeneration initiated in background, user can continue working')

    } catch (error) {
      console.error('❌ Failed to start background AI regeneration:', error)
      setIsAiLoading(false)
    }
  }

  const handleAddComment = () => {
    if (!newComment.trim()) return

    const newCommentObj = {
      id: `comment-${Date.now()}`,
      user: user,
      content: newComment.trim(),
      timestamp: new Date().toISOString(),
      type: isRequestChange ? ('required_change' as const) : ('comment' as const),
      status: 'open' as const,
      targetSection: undefined,
      creatorResponse: undefined,
      resolvedAt: undefined,
      resolved: false,
    }

    setPost(prevPost => ({
      ...prevPost,
      comments: [...prevPost.comments, newCommentObj],
    }))

    setNewComment('')
    setIsRequestChange(false)
  }

  const handleResolveComment = (commentId: string, response?: string) => {
    // TODO: Implement resolve comment logic
    console.log('Resolving comment:', commentId, 'with response:', response)
    setResolvingComment(null)
    setResolutionResponse('')
  }

  const handleAcknowledgeComment = (commentId: string, response?: string) => {
    // TODO: Implement acknowledge comment logic
    console.log('Acknowledging comment:', commentId, 'with response:', response)
  }

  const getFilteredComments = () => {
    if (!post?.comments) return []
    
    switch (commentFilter) {
      case 'required_changes':
        return post.comments.filter(c => c.type === 'required_change' || c.type === 'blocker')
      case 'open':
        return post.comments.filter(c => c.status === 'open')
      case 'resolved':
        return post.comments.filter(c => c.status === 'resolved')
      default:
        return post.comments
    }
  }

  const getRequiredChangesCount = () => {
    if (!post?.comments) return 0
    
    return post.comments.filter(c => 
      (c.type === 'required_change' || c.type === 'blocker') && c.status === 'open'
    ).length
  }

  const handleApprove = () => {
    console.log('Post approved')
  }

  const handleRequestChanges = () => {
    commentSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' })
    setIsRequestChange(true)
  }

  const handleSubmitForReview = () => {
    const openRequiredChanges = getRequiredChangesCount()
    
    if (openRequiredChanges > 0) {
      setIsSubmitReviewModalOpen(true)
    } else {
      // No pending required changes, can submit directly
      console.log('Submitting for review - no pending required changes')
    }
  }

  const handleConfirmSubmitReview = () => {
    // TODO: Implement submit for review logic
    console.log('Confirmed submit for review')
    setIsSubmitReviewModalOpen(false)
  }

  const handleCopyContent = async () => {
    if (!post?.content) return

    try {
      // For social content, copy the main content
      if (post.contentType.toLowerCase() === 'linkedin' || post.contentType.toLowerCase() === 'instagram' || post.contentType.toLowerCase() === 'facebook') {
        const socialContent = post.content as SocialContent
        await navigator.clipboard.writeText(socialContent.content)
      }
      // For Thread, copy the first thread content
      else if (post.contentType.toLowerCase() === 'thread') {
        const threadContent = post.content as any
        await navigator.clipboard.writeText(threadContent.thread?.[0]?.content || '')
      }
      // For Carousel, copy the title
      else if (post.contentType.toLowerCase() === 'carousel') {
        const carouselContent = post.content as any
        await navigator.clipboard.writeText(carouselContent.title || '')
      }
      // For Video, copy the title
      else if (post.contentType.toLowerCase() === 'video') {
        const videoContent = post.content as any
        await navigator.clipboard.writeText(videoContent.title || '')
      }
      // For Article, copy the title
      else if (post.contentType.toLowerCase() === 'article') {
        const articleContent = post.content as any
        await navigator.clipboard.writeText(articleContent.title || '')
      }
      // For Post, copy the content
      else if (post.contentType.toLowerCase() === 'post') {
        const postContent = post.content as any
        await navigator.clipboard.writeText(postContent.content || '')
      }
      // For Poll, copy the question
      else if (post.contentType.toLowerCase() === 'poll') {
        const pollContent = post.content as any
        await navigator.clipboard.writeText(pollContent.question || '')
      }
      // For PressRelease, copy the headline
      else if (post.contentType.toLowerCase() === 'pressrelease') {
        const pressReleaseContent = post.content as any
        await navigator.clipboard.writeText(pressReleaseContent.headline || '')
      }
      // For Email, copy the subject
      else if (post.contentType.toLowerCase() === 'email') {
        const emailContent = post.content as any
        await navigator.clipboard.writeText(emailContent.subject || '')
      }
      // For other content types, copy the title
      else if (typeof post.content === 'object' && post.content !== null && 'title' in post.content) {
        await navigator.clipboard.writeText(post.content.title)
      }
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Failed to copy content:', err)
    }
  }

  // Reusable Copy Button Component
  const CopyButton = () => (
    <button
      onClick={handleCopyContent}
      className={cn(
        'flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
        copySuccess
          ? "text-success-500 bg-success-500/10"
          : "text-text-tertiary hover:text-text-primary hover:bg-dark-tertiary"
      )}
    >
      {copySuccess ? (
        <>
          <CheckCheck className="w-4 h-4" />
          Copied!
        </>
      ) : (
        <>
          <Copy className="w-4 h-4" />
          Copy
        </>
      )}
    </button>
  )

  const getReviewComments = () => {
    if (!post?.comments) return []
    if (reviewTab === 'open') {
      return post.comments.filter(c => c.status !== 'resolved')
    }
    return post.comments.filter(c => c.status === 'resolved')
  }

  // Show loading state
  if ((isQuickPost && quickPostLoading) || (!isQuickPost && isLoading)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-brand-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-text-secondary">Loading {isQuickPost ? 'quick post' : 'post'}...</p>
        </div>
      </div>
    )
  }

  // Show error state only if we have an error and no post data
  if (((isQuickPost && quickPostError) || (!isQuickPost && error)) && !post) {
    const errorMessage = isQuickPost ? quickPostError : error;
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-500 mb-4">{errorMessage || `${isQuickPost ? 'Quick post' : 'Post'} not found`}</p>
          <Button
            onClick={() => navigate(isQuickPost ? '/calendar' : `/campaigns/${campaignId}`)}
            variant="secondary"
          >
            Back to {isQuickPost ? 'Calendar' : 'Campaign'}
          </Button>
        </div>
      </div>
    )
  }
  
  // If post is still null after attempting to load, show loading
  if (!post) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-brand-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-text-secondary">Loading post content...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-xs text-text-tertiary">
        <button
          onClick={() => navigate('/campaigns')}
          className="hover:text-text-secondary transition-colors duration-200"
        >
          Campaigns
        </button>
        <ChevronRight className="w-3 h-3" />
        <button
          onClick={() => {
            if (isQuickPost || fromCalendar) {
              // Build URL with preserved filters for calendar
              const params = new URLSearchParams()
              if (preservedFilters.search) params.set('search', preservedFilters.search)
              if (preservedFilters.status !== 'all') params.set('status', preservedFilters.status)
              if (preservedFilters.channel !== 'all') params.set('channel', preservedFilters.channel)
              if (preservedFilters.source !== 'all') params.set('source', preservedFilters.source)
              if (preservedFilters.view !== 'month') params.set('view', preservedFilters.view)

              const queryString = params.toString()
              navigate(`/calendar${queryString ? `?${queryString}` : ''}`)
            } else if (fromCampaign) {
              // Build URL with preserved filters for campaign
              const params = new URLSearchParams()
              if (preservedFilters.tab !== 'overview') params.set('tab', preservedFilters.tab)
              if (preservedFilters.search) params.set('search', preservedFilters.search)
              if (preservedFilters.status !== 'all') params.set('status', preservedFilters.status)
              if (preservedFilters.channel !== 'all') params.set('channel', preservedFilters.channel)
              if (preservedFilters.view !== 'month') params.set('view', preservedFilters.view)

              const queryString = params.toString()
              navigate(`/campaigns/${campaignId}${queryString ? `?${queryString}` : '?tab=content'}`)
            } else {
              navigate(`/campaigns/${campaignId}?tab=content`)
            }
          }}
          className="hover:text-text-secondary transition-colors duration-200"
        >
          {isQuickPost ? 'Calendar' : (fromCalendar ? 'Calendar' : (currentCampaign?.name || 'Campaign'))}
        </button>
        <ChevronRight className="w-3 h-3" />
        <span className="text-text-secondary font-medium">{getContentTitle()}</span>
      </div>

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 flex-1 min-w-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              if (isQuickPost || fromCalendar) {
                // Build URL with preserved filters for calendar
                const params = new URLSearchParams()
                if (preservedFilters.search) params.set('search', preservedFilters.search)
                if (preservedFilters.status !== 'all') params.set('status', preservedFilters.status)
                if (preservedFilters.channel !== 'all') params.set('channel', preservedFilters.channel)
                if (preservedFilters.source !== 'all') params.set('source', preservedFilters.source)
                if (preservedFilters.view !== 'month') params.set('view', preservedFilters.view)

                const queryString = params.toString()
                navigate(`/calendar${queryString ? `?${queryString}` : ''}`)
              } else if (fromCampaign) {
                // Build URL with preserved filters for campaign
                const params = new URLSearchParams()
                if (preservedFilters.tab !== 'overview') params.set('tab', preservedFilters.tab)
                if (preservedFilters.search) params.set('search', preservedFilters.search)
                if (preservedFilters.status !== 'all') params.set('status', preservedFilters.status)
                if (preservedFilters.channel !== 'all') params.set('channel', preservedFilters.channel)
                if (preservedFilters.view !== 'month') params.set('view', preservedFilters.view)

                const queryString = params.toString()
                navigate(`/campaigns/${campaignId}${queryString ? `?${queryString}` : '?tab=content'}`)
              } else {
                navigate(`/campaigns/${campaignId}?tab=content`)
              }
            }}
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>

          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-3 mb-1">
              <h1 className="text-xl font-bold text-text-primary truncate max-w-sm">{getContentTitle()}</h1>
            </div>

            <div className="flex items-center gap-2 text-sm text-text-tertiary">
              {post.scheduledDate && (
                <>
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(post.scheduledDate)}</span>
                  <span>•</span>
                </>
              )}

              {/* Content Type */}
              <span className="px-3 py-1 bg-dark-secondary/80 text-text-secondary text-xs font-medium rounded-full border border-dark-quaternary/50">
                {post.contentType}
              </span>
            </div>
          </div>
        </div>

        {/* Header Actions - Only show for reviewers */}
        <div className="flex items-center gap-3 flex-shrink-0">
          {isUserReviewer() && (
            <>
              {post.status === 'approved' ? (
                <Button
                  variant="secondary"
                  size="sm"
                  disabled
                  className="bg-success-500/10 border-success-500/20 text-success-500 cursor-not-allowed"
                >
                  <Check className="w-4 h-4" />
                  Approved
                </Button>
              ) : (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleApprove}
                >
                  Approve
                </Button>
              )}
              <Button
                variant="secondary"
                size="sm"
                onClick={handleRequestChanges}
                className="hover:bg-error-500/10 hover:border-error-500/20 hover:text-error-500 transition-all duration-200"
              >
                Request Changes
              </Button>
            </>
          )}
          
          {/* Submit for Review - for creators when changes are requested */}
          {user.role === 'creator' && user.id === post.createdBy.id && post.status === 'changes_requested' && (
            <Button
              variant="primary"
              size="sm"
              onClick={handleSubmitForReview}
            >
              Submit for Review
            </Button>
          )}

          {/* Schedule Post Button - only visible when approved */}
          {post.status === 'approved' && (
            <Button
              variant="primary"
              size="sm"
              onClick={() => console.log('Schedule post')}
            >
              <Calendar className="w-4 h-4" />
              Schedule Post
            </Button>
          )}
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="grid grid-cols-5 gap-8">
        
        {/* Left Column - Content View/Edit (60%) */}
        <div className="col-span-3">
          <div className="card">
            <div className="p-8">
              {/* Content Header with Mode Toggle */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4 min-w-0 flex-1">
                  <h3 className="text-lg font-semibold text-text-primary">Content</h3>

                  {/* Mode Toggle */}
                  <div className="view-toggle">
                    <button
                      onClick={() => setViewMode('preview')}
                      className={cn(
                        'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                        viewMode === 'preview'
                          ? 'view-toggle-btn-active'
                          : 'text-text-tertiary hover:text-text-primary'
                      )}
                    >
                      Preview
                    </button>
                    <button
                      onClick={() => setViewMode('edit')}
                      className={cn(
                        'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150 relative',
                        viewMode === 'edit'
                          ? 'view-toggle-btn-active'
                          : 'text-text-tertiary hover:text-text-primary'
                      )}
                    >
                      Edit
                      {/* Unsaved Changes Dot Indicator */}
                      {hasUnsavedChanges && (
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-warning-500 rounded-full" />
                      )}
                    </button>
                  </div>


                </div>
                
                {/* Action Buttons */}
                <div className="flex items-center gap-3 flex-shrink-0">
                  {viewMode === 'preview' ? (
                    <>
                      {/* Version Selector - Only show if multiple versions exist */}
                      {contentVersions.length > 1 && (
                        <div className="relative z-20">
                          <select
                            value={currentVersion}
                            onChange={(e) => handleVersionSwitch(parseInt(e.target.value))}
                            disabled={isSwitchingVersion}
                            className="appearance-none bg-dark-secondary hover:bg-dark-tertiary border border-dark-quaternary hover:border-dark-tertiary rounded-lg px-3 py-2 pr-8 text-sm font-medium text-text-primary focus:outline-none focus:ring-2 focus:ring-brand-500/20 focus:border-brand-500 transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed min-w-[80px]"
                          >
                            {contentVersions.map((version) => (
                              <option key={version.version} value={version.version} className="bg-dark-secondary text-text-primary">
                                v{version.version}
                              </option>
                            ))}
                          </select>
                          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                            {isSwitchingVersion ? (
                              <div className="w-4 h-4 border-2 border-brand-500 border-t-transparent rounded-full animate-spin" />
                            ) : (
                              <svg className="w-4 h-4 text-text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                              </svg>
                            )}
                          </div>
                        </div>
                      )}
                      <Button
                        variant="gradient"
                        size="sm"
                        onClick={() => setIsAiModalOpen(true)}
                      >
                        <Sparkles className="w-4 h-4" />
                        Edit with AI
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={handleDiscardChanges}
                      >
                        <RotateCcw className="w-4 h-4" />
                        Discard
                      </Button>
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={handleSaveChanges}
                        disabled={!hasUnsavedChanges || isSaving}
                        className={cn(
                          "transition-colors duration-200",
                          hasUnsavedChanges && !isSaving
                            ? "bg-success-500/10 border-success-500/20 text-success-500 hover:bg-success-500/20 hover:border-success-500/30"
                            : "text-text-quaternary cursor-not-allowed"
                        )}
                      >
                        <Save className="w-4 h-4" />
                        {isSaving ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </>
                  )}
                </div>
              </div>
              
              {/* Content Renderer */}
              {viewMode === 'preview' ? (
                <div className="space-y-6">


                  {/* LinkedIn Article Preview */}
                  {post.contentType.toLowerCase() === 'linkedin' && !post.isQuickPost && typeof post.content === 'object' && post.content !== null && 'sections' in post.content && (
                    <div className="card">
                      <div className="max-w-4xl mx-auto">
                        {/* Author Info */}
                        <div className="flex items-center justify-between mb-8 pb-6 border-b border-dark-quaternary">
                          <div className="flex items-center gap-3">
                            <Avatar
                              src={post.createdBy.avatar}
                              name={post.createdBy.name}
                              className="w-12 h-12"
                            />
                            <div>
                              <div className="font-semibold text-text-primary">{post.createdBy.name}</div>
                              <div className="text-sm text-text-tertiary">Product Marketing Manager</div>
                              <div className="text-xs text-text-quaternary">Published on LinkedIn • 2h</div>
                            </div>
                          </div>

                          {/* Copy Button */}
                          <button
                            onClick={handleCopyContent}
                            className={cn(
                              'flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
                              copySuccess
                                ? "text-success-500 bg-success-500/10"
                                : "text-text-tertiary hover:text-text-primary hover:bg-dark-tertiary"
                            )}
                          >
                            {copySuccess ? (
                              <>
                                <CheckCheck className="w-4 h-4" />
                                Copied!
                              </>
                            ) : (
                              <>
                                <Copy className="w-4 h-4" />
                                Copy
                              </>
                            )}
                          </button>
                        </div>

                        {/* Article Sections */}
                        <div className="space-y-8">
                          {(post.content as BlogContent).sections.map((section) => (
                            <div key={section.id} className="space-y-4">
                              <h2 className="text-xl font-semibold text-text-primary">
                                {section.title}
                              </h2>
                              <p className="text-text-secondary leading-relaxed">
                                {section.content}
                              </p>
                            </div>
                          ))}
                        </div>

                        {/* Article Footer */}
                        <div className="mt-12 pt-8 border-t border-dark-quaternary">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-6">
                              <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                                <span>👍</span>
                                <span className="text-sm">Like</span>
                              </button>
                              <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                                <MessageCircle className="w-4 h-4" />
                                <span className="text-sm">Comment</span>
                              </button>
                              <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                                <span>↗️</span>
                                <span className="text-sm">Share</span>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}





                  {/* Blog/Article/Medium Preview */}
                  {(post.contentType.toLowerCase() === 'blog' || post.contentType.toLowerCase() === 'medium') && (
                    <div className="card">
                      <div className="max-w-4xl mx-auto">
                        {/* Featured Image */}
                        {(post.content as BlogContent).featuredImage && (
                          <div className="mb-8">
                            <img
                              src={(post.content as BlogContent).featuredImage}
                              alt="Featured image"
                              className="w-full rounded-lg"
                            />
                          </div>
                        )}

                        {/* Article Header */}
                        <div className="mb-8">
                          <h1 className="text-3xl font-bold text-text-primary mb-4">
                            {(post.content as BlogContent).title}
                          </h1>
                          {(post.content as BlogContent).subheading && (
                            <p className="text-lg text-text-secondary leading-relaxed">
                              {(post.content as BlogContent).subheading}
                            </p>
                          )}
                        </div>

                        {/* Article Sections */}
                        <div className="space-y-8">
                          {(post.content as BlogContent).sections.map((section) => (
                            <div key={section.id} className="space-y-4">
                              <h2 className="text-xl font-semibold text-text-primary">
                                {section.title}
                              </h2>
                              <MarkdownRenderer
                                content={section.content}
                                className="prose prose-invert max-w-none"
                                forceMarkdown={true}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}







                  {/* New Content Type Previews */}
                  {post.contentType.toLowerCase() === 'article' && (
                    <ArticlePreview
                      content={post.content as any}
                      createdBy={post.createdBy}
                      scheduledDate={post.scheduledDate}
                      copyButton={<CopyButton />}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'post' && (
                    <PostPreview
                      content={post.content as any}
                      createdBy={post.createdBy}
                      scheduledDate={post.scheduledDate}
                      copyButton={<CopyButton />}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'poll' && (
                    <PollPreview
                      content={post.content as any}
                      createdBy={post.createdBy}
                      scheduledDate={post.scheduledDate}
                      copyButton={<CopyButton />}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'video' && (
                    <VideoPreview
                      content={post.content as any}
                      createdBy={post.createdBy}
                      scheduledDate={post.scheduledDate}
                      copyButton={<CopyButton />}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'thread' && (
                    <ThreadPreview
                      content={post.content as any}
                      createdBy={post.createdBy}
                      scheduledDate={post.scheduledDate}
                      copyButton={<CopyButton />}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'pressrelease' && (
                    <PressReleasePreview
                      content={post.content as any}
                      createdBy={post.createdBy}
                      scheduledDate={post.scheduledDate}
                      copyButton={<CopyButton />}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'carousel' && (
                    <CarouselPreview
                      content={post.content as any}
                      createdBy={post.createdBy}
                      scheduledDate={post.scheduledDate}
                      copyButton={<CopyButton />}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'email' && (
                    <EmailPreview
                      content={post.content as any}
                      createdBy={post.createdBy}
                      scheduledDate={post.scheduledDate}
                      copyButton={<CopyButton />}
                    />
                  )}

                  {/* Fallback for other content types */}
                  {!['linkedin', 'facebook', 'email', 'blog', 'medium', 'article', 'post', 'poll', 'email', 'video', 'thread', 'pressrelease', 'carousel'].includes(post.contentType.toLowerCase()) && (
                    <div className="bg-dark-tertiary rounded-xl p-6 text-center">
                      <p className="text-sm text-text-tertiary">Preview for {post.contentType} content coming soon...</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-6">
                  {/* LinkedIn/Facebook Social Edit Form - REMOVED - Using PostEdit component instead */}
                  {false && (post.contentType.toLowerCase() === 'linkedin' || post.contentType.toLowerCase() === 'facebook') && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Title</label>
                        <input
                          type="text"
                          value={(editContent as SocialContent).title}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Post title..."
                        />
                      </div>

                      {/* Content */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Content</label>
                        <textarea
                          value={(editContent as SocialContent).content}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            content: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={8}
                          placeholder="What's happening?"
                        />
                        <div className="text-xs text-text-tertiary mt-1">
                          {(editContent as SocialContent).content.length} characters
                        </div>
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {(editContent as SocialContent).hashtags?.map((hashtag, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as SocialContent).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="search-input flex-1"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as SocialContent).hashtags || []).filter((_, i) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newHashtags = [...((editContent as SocialContent).hashtags || []), '']
                              handleContentChange({
                                ...editContent,
                                hashtags: newHashtags
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add hashtag
                          </button>
                        </div>
                      </div>

                      {/* Images */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Images</label>
                        <div className="space-y-3">
                          {(editContent as SocialContent).images?.map((image, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <div className="flex-1 flex items-center gap-3">
                                <img
                                  src={image}
                                  alt={`Image ${index + 1}`}
                                  className="w-12 h-12 rounded-lg object-cover border border-dark-quaternary"
                                />
                                <input
                                  type="url"
                                  value={image}
                                  onChange={(e) => {
                                    const newImages = [...((editContent as SocialContent).images || [])]
                                    newImages[index] = e.target.value
                                    handleContentChange({
                                      ...editContent,
                                      images: newImages
                                    })
                                  }}
                                  className="search-input flex-1"
                                  placeholder="Image URL..."
                                />
                              </div>
                              <button
                                onClick={() => {
                                  const newImages = ((editContent as SocialContent).images || []).filter((_, i) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    images: newImages
                                  })
                                }}
                                className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newImages = [...((editContent as SocialContent).images || []), '']
                              handleContentChange({
                                ...editContent,
                                images: newImages
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add image
                          </button>
                        </div>
                      </div>
                    </div>
                  )}










                  {/* Blog/Article/Medium Edit Form */}
                  {(post.contentType.toLowerCase() === 'blog' || post.contentType.toLowerCase() === 'medium') && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Article Title</label>
                        <input
                          type="text"
                          value={(editContent as BlogContent).title}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter article title..."
                        />
                      </div>

                      {/* Subheading */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Subheading (Optional)</label>
                        <input
                          type="text"
                          value={(editContent as BlogContent).subheading || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            subheading: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter subheading or subtitle..."
                        />
                      </div>

                      {/* Featured Image */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Featured Image URL (Optional)</label>
                        <input
                          type="url"
                          value={(editContent as BlogContent).featuredImage || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            featuredImage: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="https://example.com/image.jpg"
                        />
                      </div>

                      {/* Article Sections */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Article Sections</label>
                        <div className="space-y-4">
                          {(editContent as BlogContent).sections.map((section, index) => (
                            <div key={section.id} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">Section {index + 1}</span>
                                <button
                                  onClick={() => {
                                    const newSections = (editContent as BlogContent).sections.filter((_, i) => i !== index)
                                    handleContentChange({
                                      ...editContent,
                                      sections: newSections
                                    })
                                  }}
                                  className="p-1 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded transition-colors"
                                >
                                  <Minus className="w-4 h-4" />
                                </button>
                              </div>

                              <input
                                type="text"
                                value={section.title}
                                onChange={(e) => {
                                  const newSections = [...(editContent as BlogContent).sections]
                                  newSections[index] = { ...section, title: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    sections: newSections
                                  })
                                }}
                                className="w-full search-input"
                                placeholder="Section title..."
                              />

                              <textarea
                                value={section.content}
                                onChange={(e) => {
                                  const newSections = [...(editContent as BlogContent).sections]
                                  newSections[index] = { ...section, content: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    sections: newSections
                                  })
                                }}
                                className="w-full search-input resize-none"
                                rows={6}
                                placeholder="Section content..."
                              />
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSection = {
                                id: `section-${Date.now()}`,
                                title: '',
                                content: ''
                              }
                              handleContentChange({
                                ...editContent,
                                sections: [...(editContent as BlogContent).sections, newSection]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Section
                          </button>
                        </div>
                      </div>
                    </div>
                  )}





                  {/* Original Twitter Thread Edit Form - keeping for reference */}
                  {false && post.contentType.toLowerCase() === 'twitter' && (
                    <div className="space-y-6">
                      {/* Twitter Thread */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Twitter Thread</label>
                        <div className="space-y-4">
                          {(editContent as TwitterContent).tweets.map((tweet, index) => (
                            <div key={tweet.id} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">
                                  {index === 0 ? 'Main Tweet' : `Tweet ${index + 1}`}
                                </span>
                                {(editContent as TwitterContent).tweets.length > 1 && (
                                  <button
                                    onClick={() => {
                                      const newTweets = (editContent as TwitterContent).tweets.filter((_, i) => i !== index)
                                      handleContentChange({
                                        ...editContent,
                                        tweets: newTweets
                                      })
                                    }}
                                    className="p-1 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded transition-colors"
                                  >
                                    <Minus className="w-4 h-4" />
                                  </button>
                                )}
                              </div>

                              <textarea
                                value={tweet.content}
                                onChange={(e) => {
                                  const newTweets = [...(editContent as TwitterContent).tweets]
                                  newTweets[index] = { ...tweet, content: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    tweets: newTweets
                                  })
                                }}
                                className="w-full search-input resize-none"
                                rows={3}
                                placeholder="What's happening?"
                                maxLength={280}
                              />
                              <div className="text-xs text-text-tertiary text-right">
                                {tweet.content.length}/280 characters
                              </div>

                              {/* Tweet Hashtags */}
                              <div>
                                <label className="text-sm font-medium text-text-secondary block mb-2">Hashtags</label>
                                <div className="space-y-2">
                                  {(tweet.hashtags || []).map((hashtag, hashtagIndex) => (
                                    <div key={hashtagIndex} className="flex items-center gap-3">
                                      <input
                                        type="text"
                                        value={hashtag}
                                        onChange={(e) => {
                                          const newTweets = [...(editContent as TwitterContent).tweets]
                                          const newHashtags = [...(tweet.hashtags || [])]
                                          newHashtags[hashtagIndex] = e.target.value
                                          newTweets[index] = { ...tweet, hashtags: newHashtags }
                                          handleContentChange({
                                            ...editContent,
                                            tweets: newTweets
                                          })
                                        }}
                                        className="search-input flex-1"
                                        placeholder="#hashtag"
                                      />
                                      <button
                                        onClick={() => {
                                          const newTweets = [...(editContent as TwitterContent).tweets]
                                          const newHashtags = (tweet.hashtags || []).filter((_, i) => i !== hashtagIndex)
                                          newTweets[index] = { ...tweet, hashtags: newHashtags }
                                          handleContentChange({
                                            ...editContent,
                                            tweets: newTweets
                                          })
                                        }}
                                        className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                                      >
                                        <Minus className="w-4 h-4" />
                                      </button>
                                    </div>
                                  ))}
                                  <button
                                    onClick={() => {
                                      const newTweets = [...(editContent as TwitterContent).tweets]
                                      const newHashtags = [...(tweet.hashtags || []), '']
                                      newTweets[index] = { ...tweet, hashtags: newHashtags }
                                      handleContentChange({
                                        ...editContent,
                                        tweets: newTweets
                                      })
                                    }}
                                    className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                                  >
                                    <Plus className="w-4 h-4" />
                                    Add Hashtag
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newTweet = {
                                id: `tweet-${Date.now()}`,
                                content: '',
                                hashtags: []
                              }
                              handleContentChange({
                                ...editContent,
                                tweets: [...(editContent as TwitterContent).tweets, newTweet]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Tweet to Thread
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* New Content Type Edit Forms */}
                  {post.contentType.toLowerCase() === 'article' && (
                    <ArticleEdit
                      content={post.content as any}
                      editContent={editContent as any}
                      setEditContent={(content) => handleContentChange(content)}
                      hasUnsavedChanges={hasUnsavedChanges}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'post' && (
                    <PostEdit
                      content={post.content as any}
                      editContent={editContent as any}
                      setEditContent={(content) => handleContentChange(content)}
                      hasUnsavedChanges={hasUnsavedChanges}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'poll' && (
                    <PollEdit
                      content={post.content as any}
                      editContent={editContent as any}
                      setEditContent={(content) => handleContentChange(content)}
                      hasUnsavedChanges={hasUnsavedChanges}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'video' && (
                    <VideoEdit
                      content={post.content as any}
                      editContent={editContent as any}
                      setEditContent={(content) => handleContentChange(content)}
                      hasUnsavedChanges={hasUnsavedChanges}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'thread' && (
                    <ThreadEdit
                      content={post.content as any}
                      editContent={editContent as any}
                      setEditContent={(content) => handleContentChange(content)}
                      hasUnsavedChanges={hasUnsavedChanges}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'pressrelease' && (
                    <PressReleaseEdit
                      content={post.content as any}
                      editContent={editContent as any}
                      setEditContent={(content) => handleContentChange(content)}
                      hasUnsavedChanges={hasUnsavedChanges}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'carousel' && (
                    <CarouselEdit
                      content={post.content as any}
                      editContent={editContent as any}
                      setEditContent={(content) => handleContentChange(content)}
                      hasUnsavedChanges={hasUnsavedChanges}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                    />
                  )}

                  {post.contentType.toLowerCase() === 'email' && (
                    <EmailEdit
                      content={post.content as any}
                      editContent={editContent as any}
                      setEditContent={(content) => handleContentChange(content)}
                      hasUnsavedChanges={hasUnsavedChanges}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                    />
                  )}

                  {/* Fallback for other content types */}
                  {!['linkedin', 'facebook', 'email', 'blog', 'medium', 'article', 'post', 'poll', 'email', 'video', 'thread', 'pressrelease', 'carousel'].includes(post.contentType.toLowerCase()) && (
                    <div className="bg-dark-tertiary rounded-xl p-6 text-center">
                      <p className="text-sm text-text-tertiary">Edit form for {post.contentType} content coming soon...</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Content Management (40%) */}
        <div className="col-span-2 space-y-6">
          {/* Post Details Card */}
          <div className="card">
            <div className="px-6 py-6">
              <h3 className="text-lg font-semibold text-text-primary mb-6">Post Details</h3>

              <div className="space-y-4">
                {/* Creator Info */}
                <div className="flex items-center gap-3">
                  <Avatar
                    src={post.createdBy.avatar}
                    name={post.createdBy.name}
                    className="w-10 h-10"
                  />
                  <div>
                    <div className="font-medium text-text-primary text-sm">{post.createdBy.name}</div>
                    <div className="text-xs text-text-tertiary capitalize">{post.createdBy.role}</div>
                  </div>
                </div>

                {/* Platform */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-text-secondary">Platform</span>
                  <div className="flex items-center gap-2">
                    <PlatformIcon 
                      platform={post.platform || post.contentType} 
                      className={cn(
                        "w-4 h-4",
                        post.platform === 'linkedin' && "text-blue-500",
                        post.platform === 'email' && "text-purple-500",
                        post.platform === 'youtube' && "text-red-500",
                        post.platform === 'twitter' && "text-blue-400",
                        post.platform === 'facebook' && "text-blue-600",
                        post.platform === 'instagram' && "text-pink-500",
                        (post.platform === 'blog' || post.platform === 'medium') && "text-gray-500"
                      )}
                    />
                    <span className="text-sm text-text-primary capitalize">{post.platform || post.contentType}</span>
                  </div>
                </div>

                {/* Status */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-text-secondary">Status</span>
                  <Status status={post.status as ContentStatus} type="content" />
                </div>

                {/* Created Date */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-text-secondary">Created</span>
                  <span className="text-sm text-text-tertiary">{formatDate('2024-07-20T14:30:00Z')}</span>
                </div>

                {/* Scheduled Date */}
                {post.scheduledDate && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-text-secondary">Scheduled</span>
                    <span className="text-sm text-text-tertiary">{formatDate(post.scheduledDate)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content Review Card */}
          <div ref={commentSectionRef} className="card">
            <div className="flex-1">
              {/* Tab Navigation - Rounded toggle style like edit/preview */}
              <div className="px-6 pt-6 pb-4 border-b border-dark-quaternary">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Content Review</h3>
                <div className="view-toggle">
                  <button
                    onClick={() => setReviewTab('open')}
                    className={cn(
                      'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150 flex items-center gap-1.5',
                      reviewTab === 'open'
                        ? 'view-toggle-btn-active'
                        : 'text-text-tertiary hover:text-text-primary'
                    )}
                  >
                    Open
                    <span className="text-xs font-bold text-brand-500">
                      {post.comments?.filter(c => c.status !== 'resolved').length || 0}
                    </span>
                  </button>
                  <button
                    onClick={() => setReviewTab('resolved')}
                    className={cn(
                      'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150 flex items-center gap-1.5',
                      reviewTab === 'resolved'
                        ? 'view-toggle-btn-active'
                        : 'text-text-tertiary hover:text-text-primary'
                    )}
                  >
                    Resolved
                    <span className="text-xs font-bold text-brand-500">
                      {post.comments?.filter(c => c.status === 'resolved').length || 0}
                    </span>
                  </button>
                </div>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                <div className="space-y-6">
                  {/* Comment List */}
                  <div className="space-y-6 max-h-96 overflow-y-auto">
                    {getReviewComments().map((comment) => {
                      const typeConfig = getCommentTypeConfig(comment.type)
                      const statusConfig = getCommentStatusConfig(comment.status)
                      
                      return (
                        <div key={comment.id} className="flex gap-3">
                          <img 
                            src={comment.user.avatar} 
                            alt={comment.user.name}
                            className="w-8 h-8 rounded-full flex-shrink-0 mt-1"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="bg-dark-tertiary rounded-xl px-4 py-3 border border-dark-quaternary">
                              {/* Comment header with badges */}
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold text-text-primary text-sm">{comment.user.name}</span>
                                  <span className={cn(
                                    'px-2 py-0.5 rounded-full text-xs font-medium',
                                    comment.user.role === 'reviewer' ? 'bg-info-500/10 text-info-500 border border-info-500/20' : 'bg-dark-quaternary text-text-secondary border border-dark-quaternary'
                                  )}>
                                    {comment.user.role}
                                  </span>
                                </div>

                                {/* Comment actions for creator */}
                                {user.role === 'creator' && comment.user.id !== user.id && comment.status === 'open' && (
                                  <div className="flex items-center gap-2">
                                    {comment.type === 'question' && (
                                      <button
                                        onClick={() => handleAcknowledgeComment(comment.id)}
                                        className="text-xs text-info-500 hover:text-info-400 font-medium"
                                      >
                                        Acknowledge
                                      </button>
                                    )}
                                    {(comment.type === 'required_change' || comment.type === 'blocker') && (
                                      <button
                                        onClick={() => setResolvingComment(comment.id)}
                                        className="text-xs text-success-500 hover:text-success-400 font-medium"
                                      >
                                        Mark Resolved
                                      </button>
                                    )}
                                  </div>
                                )}
                              </div>

                              {/* Comment badges */}
                              <div className="flex items-center gap-2 mb-3">
                                {(comment.type === 'required_change' || comment.type === 'blocker') && (
                                <span className={cn(
                                  'inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium border',
                                  typeConfig.badge
                                )}>
                                  <span>{typeConfig.icon}</span>
                                  {typeConfig.label}
                                </span>
                                )}
                                
                                {comment.status !== 'open' && (
                                  <span className={cn(
                                    'inline-flex items-center gap-1.5 px-2 py-0.5 rounded-full text-xs font-medium border',
                                    statusConfig.badge
                                  )}>
                                    {statusConfig.icon}
                                    {statusConfig.label}
                                  </span>
                                )}
                              </div>

                              {/* Comment content */}
                              <p className="text-sm text-text-secondary leading-relaxed mb-3">{comment.content}</p>

                              {/* Creator response if exists */}
                              {comment.creatorResponse && (
                                <div className="bg-info-500/10 border border-info-500/20 rounded-lg p-3 mt-3">
                                  <div className="text-xs text-info-500 font-medium mb-1">Creator Response:</div>
                                  <p className="text-sm text-text-secondary">{comment.creatorResponse}</p>
                                </div>
                              )}

                              {/* Resolution form */}
                              {resolvingComment === comment.id && (
                                <div className="mt-3 pt-3 border-t border-dark-quaternary">
                                  <label className="text-xs font-medium text-text-secondary block mb-2">
                                    Resolution Response (optional):
                                  </label>
                                  <textarea
                                    value={resolutionResponse}
                                    onChange={(e) => setResolutionResponse(e.target.value)}
                                    placeholder="Describe how you addressed this feedback..."
                                    className="w-full search-input resize-none"
                                    rows={2}
                                  />
                                  <div className="flex items-center gap-2 mt-2">
                                    <button
                                      onClick={() => handleResolveComment(comment.id, resolutionResponse)}
                                      className="btn btn-primary text-xs"
                                    >
                                      Mark Resolved
                                    </button>
                                    <button
                                      onClick={() => {
                                        setResolvingComment(null)
                                        setResolutionResponse('')
                                      }}
                                      className="btn btn-secondary text-xs"
                                    >
                                      Cancel
                                    </button>
                                  </div>
                                </div>
                              )}

                              <div className="text-xs text-text-tertiary mt-3">
                                {formatDate(comment.timestamp)}
                                {comment.resolvedAt && (
                                  <span className="ml-2">
                                    • Resolved {formatDate(comment.resolvedAt)}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                    
                    {getReviewComments().length === 0 && (
                      <div className="text-center py-8">
                        <p className="text-sm text-text-tertiary">
                          {reviewTab === 'open'
                            ? 'No open comments.'
                            : 'No resolved comments.'}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Add Comment */}
                  <div className="space-y-4 pt-4 border-t border-dark-quaternary">
                    <textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Add your feedback..."
                      className="w-full search-input resize-none"
                      rows={3}
                    />
                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => setIsRequestChange(!isRequestChange)}
                        className={cn(
                          'flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 border',
                          isRequestChange
                            ? 'bg-error-500/10 text-error-500 border-error-500/20'
                            : 'bg-dark-tertiary text-text-tertiary border-dark-quaternary hover:bg-dark-quaternary'
                        )}
                      >
                        <span className={cn('w-2 h-2 rounded-full', isRequestChange ? 'bg-error-500' : 'bg-text-quaternary')} />
                        Request Change
                      </button>
                      <Button
                        variant="secondary"
                        onClick={handleAddComment}
                        disabled={!newComment.trim()}
                        size="sm"
                      >
                        Add Comment
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* AI Edit Modal */}
      {isAiModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsAiModalOpen(false)}
          />

          {/* Modal */}
          <div className="relative card p-8 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-text-primary">Edit with AI</h3>
                <p className="text-sm text-text-tertiary">
                  Improve your content with AI assistance
                  {contentVersions.length > 1 && (
                    <span className="ml-2 text-brand-500">• Will create version {currentVersion + 1}</span>
                  )}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsAiModalOpen(false)}
                className="text-text-quaternary hover:text-text-tertiary"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-6">
              <div>
                <label className="text-sm font-medium text-text-secondary block mb-3">
                  How would you like to improve this content?
                </label>
                <textarea
                  value={aiInstruction}
                  onChange={(e) => setAiInstruction(e.target.value)}
                  placeholder="e.g., Make it more professional, add emojis, change tone to casual, include a call-to-action..."
                  className="w-full search-input resize-none"
                  rows={4}
                />
              </div>

              <div className="flex items-center gap-3">
                <Button
                  variant="secondary"
                  onClick={() => setIsAiModalOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="gradient"
                  onClick={handleAiRegenerate}
                  disabled={!aiInstruction.trim() || isAiLoading}
                  loading={isAiLoading}
                  className="flex-1"
                >
                  {!isAiLoading && <Sparkles className="w-4 h-4" />}
                  Generate
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Submit for Review Modal */}
      {isSubmitReviewModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsSubmitReviewModalOpen(false)}
          />

          {/* Modal */}
          <div className="relative card p-8 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-text-primary">Submit for Review</h3>
                <p className="text-sm text-text-tertiary">Review resolution status before resubmitting</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSubmitReviewModalOpen(false)}
                className="text-text-quaternary hover:text-text-tertiary"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-6">
              <div className="p-4 bg-warning-500/10 border border-warning-500/20 rounded-xl">
                <div className="flex items-center gap-2 mb-3">
                  <AlertCircle className="w-5 h-5 text-warning-500" />
                  <h4 className="font-medium text-warning-500">Pending Required Changes</h4>
                </div>
                <p className="text-sm text-warning-500 mb-4">
                  You have {getRequiredChangesCount()} unresolved required changes. Please address them before resubmitting.
                </p>

                {/* List of unresolved required changes */}
                <div className="space-y-2">
                  {post.comments
                    .filter(c => (c.type === 'required_change' || c.type === 'blocker') && c.status === 'open')
                    .map(comment => {
                      const typeConfig = getCommentTypeConfig(comment.type)
                      return (
                        <div key={comment.id} className="flex items-start gap-3 p-3 bg-dark-secondary rounded-lg border border-warning-500/20">
                          <span className={cn(
                            'inline-flex items-center gap-1 px-2 py-0.5 rounded text-xs font-medium border flex-shrink-0',
                            typeConfig.badge
                          )}>
                            <span>{typeConfig.icon}</span>
                            {typeConfig.label}
                          </span>
                          <p className="text-sm text-text-secondary flex-1">{comment.content}</p>
                        </div>
                      )
                    })}
                </div>
              </div>

              <p className="text-sm text-text-tertiary">
                Go back to resolve the required changes, or explain why they cannot be addressed before resubmitting.
              </p>

              <div className="flex items-center gap-3">
                <Button
                  variant="secondary"
                  onClick={() => setIsSubmitReviewModalOpen(false)}
                  className="flex-1"
                >
                  Go Back
                </Button>
                <Button
                  variant="primary"
                  onClick={handleConfirmSubmitReview}
                  className="flex-1"
                >
                  Submit Anyway
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PostDetail 