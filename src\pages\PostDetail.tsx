import React, { useState, useRef, useEffect } from 'react'
import { useParams, useNavigate, useSearchParams } from 'react-router-dom'
import { useCampaignStore } from '@/stores/campaignStore'
import { api } from '@/stores/authStore'
import { ArrowLeft, Calendar, MessageCircle, Check, AlertCircle, Sparkles, Copy, CheckCheck, ChevronRight, ChevronLeft, BarChart3, TrendingUp, Users, Eye, X, Plus, Minus, Save, RotateCcw, Lock, Heart, Share2, Image } from 'lucide-react'
import { cn } from '@/utils/cn'
import { formatDate } from '@/utils/formatDate'
import { PostContent, ArticleContent, PollContent, EmailContent, VideoContent, ThreadContent, PressReleaseContent, ContentData } from '@/types/content'
import { mockContentPost, mockReviewerPost, mockEmailPost, mockYouTubePost, mockBlogPost, mockCarouselPost, mockTwitterPost, mockFacebookPost, mockLinkedInArticlePost, currentUser, creator<PERSON>ser } from '@/data/mockContent'
import Button from '@/components/ui/Button'
import { Status } from '@/components/ui'
import type { ContentStatus } from '@/components/ui'
import MarkdownRenderer from '@/components/MarkdownRenderer'

// LinkedIn Icon Component
const LinkedinIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
)

// YouTube Icon Component
const YouTubeIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
)

// Email Icon Component  
const EmailIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
  </svg>
)

// Avatar Component with fallback to initials
const Avatar = ({ src, name, className }: { src?: string; name: string; className?: string }) => {
  const [imageError, setImageError] = useState(false)
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }
  
  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-blue-500',
      'bg-green-500', 
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-yellow-500',
      'bg-red-500',
      'bg-teal-500'
    ]
    const index = name.length % colors.length
    return colors[index]
  }
  
  if (!src || imageError) {
    return (
      <div className={cn(
        'flex items-center justify-center text-white font-semibold rounded-full',
        getAvatarColor(name),
        className
      )}>
        {getInitials(name)}
      </div>
    )
  }
  
  return (
    <img
      src={src}
      alt={name}
      className={cn('rounded-full', className)}
      onError={() => setImageError(true)}
    />
  )
}

// Platform Icon Component
const PlatformIcon = ({ platform, className }: { platform: string; className?: string }) => {
  switch (platform.toLowerCase()) {
    case 'linkedin':
      return <LinkedinIcon className={className} />
    case 'youtube':
      return <YouTubeIcon className={className} />
    case 'email':
      return <EmailIcon className={className} />
    case 'twitter':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
        </svg>
      )
    case 'facebook':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      )
    case 'instagram':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
        </svg>
      )
    case 'blog':
    case 'medium':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"/>
        </svg>
      )
    default:
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      )
  }
}

const PostDetail: React.FC = () => {
  const { id: campaignId, postId, quickPostId } = useParams()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { currentCampaign, fetchCampaign, isLoading, error } = useCampaignStore()

  // Determine if this is a quick post or campaign post
  const isQuickPost = !!quickPostId
  const [quickPost, setQuickPost] = useState<any>(null)
  const [quickPostLoading, setQuickPostLoading] = useState(false)
  const [quickPostError, setQuickPostError] = useState<string | null>(null)

  // Check if we came from calendar
  const fromCalendar = searchParams.get('from') === 'calendar'
  
  // Fetch quick post data
  const fetchQuickPost = async (id: string) => {
    try {
      setQuickPostLoading(true)
      setQuickPostError(null)
      const response = await api.get(`/quick-posts/${id}`)
      setQuickPost(response.data.data)
    } catch (error: any) {
      console.error('Failed to fetch quick post:', error)
      setQuickPostError(error.response?.data?.message || 'Failed to fetch quick post')
    } finally {
      setQuickPostLoading(false)
    }
  }

  // Fetch campaign or quick post data when component mounts
  useEffect(() => {
    if (isQuickPost && quickPostId) {
      fetchQuickPost(quickPostId)
    } else if (campaignId) {
      fetchCampaign(campaignId)
    }
  }, [campaignId, quickPostId, isQuickPost, fetchCampaign])
  
  // Get mock post by ID for fallback
  const getMockPostById = (id: string) => {
    switch (id) {
      case '2': return mockReviewerPost
      case '3': return mockEmailPost
      case '4': return mockYouTubePost
      case '5': return mockBlogPost
      case '6': return mockCarouselPost
      case '7': return mockTwitterPost
      case '8': return mockFacebookPost
      case '9': return mockLinkedInArticlePost
      default: return mockContentPost
    }
  }

  // Get post data from quick post
  const getPostFromQuickPost = () => {
    console.log('🔍 getPostFromQuickPost called with:', { quickPostId, hasContent: !!quickPost?.generatedContent });

    if (!quickPost?.generatedContent || quickPost.generatedContent.length === 0) {
      console.log('❌ No quick post content available');
      return null;
    }

    // For quick posts, we typically have one piece of content
    const generatedContent = quickPost.generatedContent[0];
    console.log('📄 Found quick post content:', !!generatedContent);

    if (!generatedContent) {
      console.log('❌ No content in quick post');
      return null;
    }

    // Transform the quick post data to match the expected post structure
    const transformedPost = {
      id: quickPostId,
      contentType: quickPost.channel.toLowerCase(),
      platform: quickPost.channel.toLowerCase(),
      status: generatedContent.status === 'generated' ? 'pending_review' : generatedContent.status,
      scheduledDate: quickPost.postDate,
      createdBy: {
        name: 'Quick Post',
        avatar: '',
        role: 'AI Generated'
      },
      content: generatedContent.content || generatedContent.caption || generatedContent.tweet || generatedContent.postText || generatedContent.subject || generatedContent.blogTitle || 'Quick Post Content',
      hashtags: generatedContent.hashtags || [],
      metrics: {
        likes: 0,
        comments: 0,
        shares: 0,
        views: 0
      },
      comments: [], // Initialize empty comments array
      isQuickPost: true,
      quickPostData: quickPost
    };

    console.log('✅ Transformed quick post:', transformedPost.contentType);
    return transformedPost;
  };

  // Find the specific content item from the campaign's generated content
  const getPostFromCampaign = () => {
    console.log('🔍 getPostFromCampaign called with:', { postId, campaignId, hasContent: !!currentCampaign?.generatedContent });

    if (!currentCampaign?.generatedContent || !postId) {
      console.log('❌ No campaign content or postId, using mock data');
      return getMockPostById(postId || '1');
    }

    // Handle different postId formats
    let contentIndex;

    // Check if postId is in format "campaignId-index" (from calendar)
    if (postId.includes('-')) {
      const parts = postId.split('-');
      const lastPart = parts[parts.length - 1];
      if (!isNaN(parseInt(lastPart))) {
        contentIndex = parseInt(lastPart); // Already 0-based from calendar
        console.log('📅 Calendar format postId detected, contentIndex:', contentIndex);
      } else {
        console.log('❌ Invalid calendar format postId, using mock data');
        return getMockPostById(postId);
      }
    } else if (!isNaN(parseInt(postId))) {
      // Numeric postId (from campaign page)
      contentIndex = parseInt(postId) - 1; // postId is 1-based, array is 0-based
      console.log('🔢 Numeric postId detected, contentIndex:', contentIndex);
    } else {
      // If postId isn't in expected format, use mock data
      console.log('❌ Unknown postId format, using mock data');
      return getMockPostById(postId);
    }

    const generatedContent = currentCampaign.generatedContent[contentIndex];
    console.log('📄 Found generated content:', !!generatedContent, generatedContent?.topic);

    if (!generatedContent) {
      console.log('❌ No content at index', contentIndex, 'using mock data');
      return getMockPostById(postId); // Fallback to mock data if content not found
    }
    
    // Transform the real data to match the expected post structure
    const transformedPost = {
      id: postId,
      contentType: generatedContent.channel.toLowerCase(), // LinkedIn -> linkedin
      platform: generatedContent.channel.toLowerCase(), // Add platform field for sidebar display
      status: generatedContent.status === 'generated' ? 'pending_review' : generatedContent.status,
      scheduledDate: generatedContent.date,
      createdBy: {
        id: 'creator-1',
        name: 'Sarah Chen',
        role: 'creator',
        avatar: '/api/placeholder/32/32'
      },
      content: transformContentData(generatedContent),
      comments: [], // Start with empty comments for now
      activity: []
    };

    console.log('✅ Transformed post data:', transformedPost);
    return transformedPost;
  }
  
  // Transform the generated content data to match UI expectations
  const transformContentData = (generatedContent: any) => {
    console.log('🔄 Transforming content data:', generatedContent);
    const channel = generatedContent.channel.toLowerCase();
    console.log('📺 Channel:', channel, 'Content Type:', generatedContent.contentType);
    
    // For LinkedIn posts
    if (channel === 'linkedin' && generatedContent.contentType.toLowerCase() === 'post') {
      return {
        title: generatedContent.title || generatedContent.topic,
        content: generatedContent.content || generatedContent.topic,
        hashtags: generatedContent.hashtags || [],
        images: [] // No images in generated content yet
      }
    }
    
    // For LinkedIn articles and Medium articles
    if ((channel === 'linkedin' && generatedContent.contentType.toLowerCase() === 'article') || channel === 'medium') {
      // Transform backend sections structure to frontend expected structure
      const transformedSections = generatedContent.sections ?
        generatedContent.sections.map((section: any, index: number) => ({
          id: `section-${index + 1}`,
          title: section.Introduction || section.title || `Section ${index + 1}`,
          content: section.Description || section.content || ''
        })) : [
          {
            id: 'section-1',
            title: generatedContent.title || generatedContent.topic || 'Article Content',
            content: generatedContent.content || generatedContent.summary || generatedContent.topic || ''
          }
        ]

      return {
        title: generatedContent.title || generatedContent.topic,
        subheading: generatedContent.subheading || '',
        sections: transformedSections,
        summary: generatedContent.summary || '',
        featuredImage: generatedContent.featuredImage || null
      }
    }
    
    // For Twitter threads
    if (channel === 'twitter' && generatedContent.thread) {
      return {
        tweets: generatedContent.thread.map((tweet: any, index: number) => ({
          id: `tweet-${index}`,
          content: tweet.content,
          order: index + 1
        }))
      }
    }
    
    // For YouTube videos
    if (channel === 'youtube') {
      // Transform videoScript structure to match UI expectations
      let scriptSections = [];

      if (generatedContent.videoScript) {
        const videoScript = generatedContent.videoScript;

        // Add intro section
        if (videoScript.intro) {
          scriptSections.push({
            id: 'intro',
            type: 'intro',
            title: videoScript.intro.title,
            content: videoScript.intro.desc,
            timestamp: videoScript.intro.startTime
          });
        }

        // Add main sections
        if (videoScript.main && Array.isArray(videoScript.main)) {
          videoScript.main.forEach((section, index) => {
            scriptSections.push({
              id: `main-${index}`,
              type: 'main',
              title: section.title,
              content: section.desc,
              timestamp: section.startTime
            });
          });
        }

        // Add outro section
        if (videoScript.outro) {
          scriptSections.push({
            id: 'outro',
            type: 'outro',
            title: videoScript.outro.title,
            content: videoScript.outro.desc,
            timestamp: videoScript.outro.startTime
          });
        }
      }

      return {
        title: generatedContent.title || generatedContent.topic,
        description: generatedContent.desc || generatedContent.description || generatedContent.topic,
        hashtags: generatedContent.hashtags || [],
        script: scriptSections,
        thumbnailUrl: generatedContent.thumbnailUrl || null
      }
    }
    
    // For Email content
    if (channel === 'email') {
      return {
        subject: generatedContent.subject || generatedContent.topic,
        preheader: generatedContent.preheader || '',
        body: generatedContent.body || generatedContent.topic,
        footer: generatedContent.footer || '',
        sections: generatedContent.sections || []
      }
    }
    
    // Default fallback for other content types
    return {
      title: generatedContent.title || generatedContent.topic,
      content: generatedContent.content || generatedContent.topic,
      hashtags: generatedContent.hashtags || []
    }
  }
  
  const [post, setPost] = useState<any>(null)
  const user = currentUser // Use current user for now
  
  // Update post when campaign or quick post data is loaded
  useEffect(() => {
    if (isQuickPost) {
      console.log('🔄 Quick Post useEffect triggered:', { quickPostId, hasQuickPost: !!quickPost });
      const quickPostData = getPostFromQuickPost();
      console.log('📄 Got quick post data:', !!quickPostData, quickPostData?.contentType);
      if (quickPostData) {
        setPost(quickPostData);
        console.log('✅ Quick post set successfully');
      }
    } else {
      console.log('🔄 Campaign Post useEffect triggered:', { campaignId, postId, hasCampaign: !!currentCampaign });
      const campaignPost = getPostFromCampaign();
      console.log('📄 Got campaign post:', !!campaignPost, campaignPost?.contentType);
      if (campaignPost) {
        setPost(campaignPost);
        console.log('✅ Campaign post set successfully');
      }
    }
  }, [currentCampaign, postId, quickPost, quickPostId, isQuickPost])

  // Update editContent when post changes
  useEffect(() => {
    if (post?.content) {
      setEditContent(post.content)
    }
  }, [post])
  
  // View mode state
  const [viewMode, setViewMode] = useState<'preview' | 'edit'>('preview')

  // Edit state - clone the current content for editing
  const [editContent, setEditContent] = useState(post?.content || null)

  // Helper function to determine actual content type based on content structure
  const getActualContentType = (post: any) => {
    // If it's already a new AI API content type, return as is
    if (['Article', 'Post', 'Poll', 'Email', 'Video', 'Thread', 'PressRelease', 'Carousel'].includes(post.contentType)) {
      return post.contentType
    }

    // For legacy channel-based content types, determine actual type from content structure
    const content = post.content
    if (!content || typeof content !== 'object') return post.contentType

    // Check content structure to determine actual type
    if (content.sections && Array.isArray(content.sections)) {
      return 'Article' // Has sections = Article
    }
    // Handle blog/website content - typically has sections or article-like structure
    if (post.contentType === 'blog' || post.contentType === 'website-blog' || post.contentType === 'medium') {
      return content.sections ? 'Article' : 'Post' // Blog with sections = Article, otherwise Post
    }
    if (content.question && content.options) {
      return 'Poll' // Has question and options = Poll
    }
    if (content.subject && (content.body || content.sections)) {
      return 'Email' // Has subject and body/sections = Email
    }
    if (content.videoScript || (content.title && content.desc)) {
      return 'Video' // Has video script or title+desc = Video
    }
    if (content.thread && Array.isArray(content.thread)) {
      return 'Thread' // Has thread array = Thread
    }
    if (content.tweets && Array.isArray(content.tweets)) {
      return 'Thread' // Twitter tweets = Thread
    }
    if (content.headline && (content.lead_paragraph || content.body_sections)) {
      return 'PressRelease' // Has headline and press release structure = PressRelease
    }
    if (content.slides && Array.isArray(content.slides)) {
      return 'Carousel' // Has slides = Carousel
    }
    if (content.title && content.content) {
      return 'Post' // Has title and content = Post
    }

    // Default fallback
    return post.contentType
  }

  // Get the actual content type for rendering
  const actualContentType = post ? getActualContentType(post) : null
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  
  // Other existing state
  const [activeImageIndex, setActiveImageIndex] = useState(0)
  const [isAiModalOpen, setIsAiModalOpen] = useState(false)
  const [aiInstruction, setAiInstruction] = useState('')
  const [newComment, setNewComment] = useState('')
  const [isAiLoading, setIsAiLoading] = useState(false)
  const [copySuccess, setCopySuccess] = useState(false)
  const [reviewTab, setReviewTab] = useState<'open' | 'resolved'>('open')
  
  // New state for enhanced comments
  const [resolvingComment, setResolvingComment] = useState<string | null>(null)
  const [resolutionResponse, setResolutionResponse] = useState('')
  const [commentFilter, setCommentFilter] = useState<'all' | 'required_changes' | 'open' | 'resolved'>('all')

  // State for enhanced workflows
  const [isSubmitReviewModalOpen, setIsSubmitReviewModalOpen] = useState(false)
  
  // New state and ref for simplified request changes
  const [isRequestChange, setIsRequestChange] = useState(false)
  const commentSectionRef = useRef<HTMLDivElement>(null)

  // Utility functions
  const formatActivityDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getContentTitle = () => {
    if (!post || !post.content) return 'Loading...'

    // Handle quick posts where content is a string
    if (post.isQuickPost && typeof post.content === 'string') {
      return post.content.substring(0, 50) + (post.content.length > 50 ? '...' : '')
    }

    if (post.contentType === 'twitter') {
      const twitterContent = post.content as TwitterContent
      return twitterContent.tweets[0]?.content.substring(0, 50) + '...' || 'Twitter Thread'
    }

    // For campaign posts, content is an object
    if (typeof post.content === 'object' && post.content !== null && 'title' in post.content) {
      return post.content.title
    }

    return 'Untitled Content'
  }

  const getCommentTypeConfig = (type: string) => {
    switch (type) {
      case 'required_change':
        return {
          badge: 'bg-error-500/10 text-error-500 border-error-500/20',
          icon: <div className="w-1.5 h-1.5 bg-error-500 rounded-full" />,
          label: 'Required Change'
        }
      case 'blocker':
        return {
          badge: 'bg-error-500/20 text-error-400 border-error-500/30',
          icon: <div className="w-1.5 h-1.5 bg-error-400 rounded-full" />,
          label: 'Blocker'
        }
      case 'question':
        return {
          badge: 'bg-info-500/10 text-info-500 border-info-500/20',
          icon: <div className="w-1.5 h-1.5 bg-info-500 rounded-full" />,
          label: 'Question'
        }
      case 'comment':
      default:
        return {
          badge: 'bg-dark-tertiary text-text-secondary border-dark-quaternary',
          icon: <div className="w-1.5 h-1.5 bg-text-tertiary rounded-full" />,
          label: 'Comment'
        }
    }
  }

  const getCommentStatusConfig = (status: string) => {
    switch (status) {
      case 'resolved':
        return {
          badge: 'bg-success-500/10 text-success-500 border-success-500/20',
          icon: <Check className="w-3 h-3" />,
          label: 'Resolved'
        }
      case 'acknowledged':
        return {
          badge: 'bg-success-500/10 text-success-500 border-success-500/20',
          icon: <div className="w-1.5 h-1.5 bg-success-500 rounded-full" />,
          label: 'Acknowledged'
        }
      case 'open':
      default:
        return {
          badge: 'bg-warning-500/10 text-warning-500 border-warning-500/20',
          icon: <Lock className="w-3 h-3" />,
          label: 'Open'
        }
    }
  }

  // Handler functions
  const handleSaveChanges = async () => {
    // TODO: Implement save logic
    setHasUnsavedChanges(false)
    setViewMode('preview')
  }

  const handleDiscardChanges = () => {
    if (post?.content) {
      setEditContent(post.content)
    }
    setHasUnsavedChanges(false)
    setViewMode('preview')
  }

  const handleContentChange = (newContent: any) => {
    setEditContent(newContent)
    setHasUnsavedChanges(true)
  }

  const handleAiRegenerate = async () => {
    if (!aiInstruction.trim()) return
    
    setIsAiLoading(true)
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsAiLoading(false)
    setAiInstruction('')
    setIsAiModalOpen(false)
  }

  const handleAddComment = () => {
    if (!newComment.trim()) return

    const newCommentObj = {
      id: `comment-${Date.now()}`,
      user: user,
      content: newComment.trim(),
      timestamp: new Date().toISOString(),
      type: isRequestChange ? ('required_change' as const) : ('comment' as const),
      status: 'open' as const,
      targetSection: undefined,
      creatorResponse: undefined,
      resolvedAt: undefined,
      resolved: false,
    }

    setPost(prevPost => ({
      ...prevPost,
      comments: [...prevPost.comments, newCommentObj],
    }))

    setNewComment('')
    setIsRequestChange(false)
  }

  const handleResolveComment = (commentId: string, response?: string) => {
    // TODO: Implement resolve comment logic
    console.log('Resolving comment:', commentId, 'with response:', response)
    setResolvingComment(null)
    setResolutionResponse('')
  }

  const handleAcknowledgeComment = (commentId: string, response?: string) => {
    // TODO: Implement acknowledge comment logic
    console.log('Acknowledging comment:', commentId, 'with response:', response)
  }

  const getFilteredComments = () => {
    if (!post?.comments) return []
    
    switch (commentFilter) {
      case 'required_changes':
        return post.comments.filter(c => c.type === 'required_change' || c.type === 'blocker')
      case 'open':
        return post.comments.filter(c => c.status === 'open')
      case 'resolved':
        return post.comments.filter(c => c.status === 'resolved')
      default:
        return post.comments
    }
  }

  const getRequiredChangesCount = () => {
    if (!post?.comments) return 0
    
    return post.comments.filter(c => 
      (c.type === 'required_change' || c.type === 'blocker') && c.status === 'open'
    ).length
  }

  const handleApprove = () => {
    console.log('Post approved')
  }

  const handleRequestChanges = () => {
    commentSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' })
    setIsRequestChange(true)
  }

  const handleSubmitForReview = () => {
    const openRequiredChanges = getRequiredChangesCount()
    
    if (openRequiredChanges > 0) {
      setIsSubmitReviewModalOpen(true)
    } else {
      // No pending required changes, can submit directly
      console.log('Submitting for review - no pending required changes')
    }
  }

  const handleConfirmSubmitReview = () => {
    // TODO: Implement submit for review logic
    console.log('Confirmed submit for review')
    setIsSubmitReviewModalOpen(false)
  }

  const handleCopyContent = async () => {
    if (!post?.content) return
    
    try {
      // For social content, copy the main content
      if (post.contentType === 'linkedin' || post.contentType === 'instagram' || post.contentType === 'facebook') {
        const socialContent = post.content as SocialContent
        await navigator.clipboard.writeText(socialContent.content)
      }
      // For Twitter, copy the first tweet content
      else if (post.contentType === 'twitter') {
        const twitterContent = post.content as TwitterContent
        await navigator.clipboard.writeText(twitterContent.tweets[0]?.content || '')
      }
      // For carousel, copy the title
      else if (post.contentType === 'carousel') {
        const carouselContent = post.content as CarouselContent
        await navigator.clipboard.writeText(carouselContent.title)
      }
      // For other content types, copy the title
      else if (typeof post.content === 'object' && post.content !== null && 'title' in post.content) {
        await navigator.clipboard.writeText(post.content.title)
      }
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Failed to copy content:', err)
    }
  }

  const getReviewComments = () => {
    if (!post?.comments) return []
    if (reviewTab === 'open') {
      return post.comments.filter(c => c.status !== 'resolved')
    }
    return post.comments.filter(c => c.status === 'resolved')
  }

  // Show loading state
  if ((isQuickPost && quickPostLoading) || (!isQuickPost && isLoading)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-brand-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-text-secondary">Loading {isQuickPost ? 'quick post' : 'post'}...</p>
        </div>
      </div>
    )
  }

  // Show error state only if we have an error and no post data
  if (((isQuickPost && quickPostError) || (!isQuickPost && error)) && !post) {
    const errorMessage = isQuickPost ? quickPostError : error;
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-500 mb-4">{errorMessage || `${isQuickPost ? 'Quick post' : 'Post'} not found`}</p>
          <Button
            onClick={() => navigate(isQuickPost ? '/calendar' : `/campaigns/${campaignId}`)}
            variant="secondary"
          >
            Back to {isQuickPost ? 'Calendar' : 'Campaign'}
          </Button>
        </div>
      </div>
    )
  }
  
  // If post is still null after attempting to load, show loading
  if (!post) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-brand-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-text-secondary">Loading post content...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-xs text-text-tertiary">
        <button
          onClick={() => navigate('/campaigns')}
          className="hover:text-text-secondary transition-colors duration-200"
        >
          Campaigns
        </button>
        <ChevronRight className="w-3 h-3" />
        <button
          onClick={() => {
            if (isQuickPost || fromCalendar) {
              navigate('/calendar')
            } else {
              navigate(`/campaigns/${campaignId}?tab=content`)
            }
          }}
          className="hover:text-text-secondary transition-colors duration-200"
        >
          {isQuickPost ? 'Calendar' : (fromCalendar ? 'Calendar' : (currentCampaign?.name || 'Campaign'))}
        </button>
        <ChevronRight className="w-3 h-3" />
        <span className="text-text-secondary font-medium">{getContentTitle()}</span>
      </div>

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 flex-1 min-w-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              if (isQuickPost || fromCalendar) {
                navigate('/calendar')
              } else {
                navigate(`/campaigns/${campaignId}?tab=content`)
              }
            }}
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>

          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-3 mb-1">
              <h1 className="text-xl font-bold text-text-primary truncate max-w-sm">{getContentTitle()}</h1>

              {/* Status Indicator */}
              <Status status={post.status as ContentStatus} type="content" />
            </div>

            <div className="flex items-center gap-2 text-sm text-text-tertiary">
              {post.scheduledDate && (
                <>
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(post.scheduledDate)}</span>
                  <span>•</span>
                </>
              )}

              {/* User Perspective Indicator */}
              <Avatar
                src={user.avatar}
                name={user.name}
                className="w-4 h-4"
              />
              <span>
                Viewing as {user.name} ({user.role})
              </span>
            </div>
          </div>
        </div>

        {/* Header Actions */}
        <div className="flex items-center gap-3 flex-shrink-0">
          {user.role === 'reviewer' && user.id !== post.createdBy.id && (
            <>
              {post.status === 'approved' ? (
                <Button
                  variant="secondary"
                  size="sm"
                  disabled
                  className="bg-success-500/10 border-success-500/20 text-success-500 cursor-not-allowed"
                >
                  <Check className="w-4 h-4" />
                  Approved
                </Button>
              ) : (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleApprove}
                >
                  Approve
                </Button>
              )}
              <Button
                variant="secondary"
                size="sm"
                onClick={handleRequestChanges}
                className="hover:bg-error-500/10 hover:border-error-500/20 hover:text-error-500 transition-all duration-200"
              >
                Request Changes
              </Button>
            </>
          )}
          
          {/* Submit for Review - for creators when changes are requested */}
          {user.role === 'creator' && user.id === post.createdBy.id && post.status === 'changes_requested' && (
            <Button
              variant="primary"
              size="sm"
              onClick={handleSubmitForReview}
            >
              Submit for Review
            </Button>
          )}

          {/* Schedule Post Button - only visible when approved */}
          {post.status === 'approved' && (
            <Button
              variant="primary"
              size="sm"
              onClick={() => console.log('Schedule post')}
            >
              <Calendar className="w-4 h-4" />
              Schedule Post
            </Button>
          )}
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="grid grid-cols-5 gap-8">
        
        {/* Left Column - Content View/Edit (60%) */}
        <div className="col-span-3">
          <div className="card">
            <div className="p-8">
              {/* Content Header with Mode Toggle */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  <h3 className="text-lg font-semibold text-text-primary">Content</h3>

                  {/* Mode Toggle */}
                  <div className="view-toggle">
                    <button
                      onClick={() => setViewMode('preview')}
                      className={cn(
                        'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                        viewMode === 'preview'
                          ? 'view-toggle-btn-active'
                          : 'text-text-tertiary hover:text-text-primary'
                      )}
                    >
                      Preview
                    </button>
                    <button
                      onClick={() => setViewMode('edit')}
                      className={cn(
                        'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                        viewMode === 'edit'
                          ? 'view-toggle-btn-active'
                          : 'text-text-tertiary hover:text-text-primary'
                      )}
                    >
                      Edit
                    </button>
                  </div>

                  {/* Unsaved Changes Indicator */}
                  {hasUnsavedChanges && (
                    <div className="flex items-center gap-1.5 text-xs text-warning-500">
                      <div className="w-2 h-2 rounded-full bg-warning-500" />
                      <span>Unsaved changes</span>
                    </div>
                  )}
                </div>
                
                {/* Action Buttons */}
                {viewMode === 'preview' ? (
                  <div className="flex items-center gap-3">
                    <button
                      onClick={handleCopyContent}
                      className={cn(
                        'flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
                        copySuccess
                          ? "text-success-500 bg-success-500/10"
                          : "text-text-tertiary hover:text-text-primary hover:bg-dark-tertiary"
                      )}
                    >
                      {copySuccess ? (
                        <>
                          <CheckCheck className="w-4 h-4" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4" />
                          Copy
                        </>
                      )}
                    </button>
                    <Button
                      variant="gradient"
                      size="sm"
                      onClick={() => setIsAiModalOpen(true)}
                    >
                      <Sparkles className="w-4 h-4" />
                      Edit with AI
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-3">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={handleDiscardChanges}
                    >
                      <RotateCcw className="w-4 h-4" />
                      Discard
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={handleSaveChanges}
                      disabled={!hasUnsavedChanges}
                      className={cn(
                        "transition-colors duration-200",
                        hasUnsavedChanges
                          ? "bg-success-500/10 border-success-500/20 text-success-500 hover:bg-success-500/20 hover:border-success-500/30"
                          : "text-text-quaternary cursor-not-allowed"
                      )}
                    >
                      <Save className="w-4 h-4" />
                      Save Changes
                    </Button>
                  </div>
                )}
              </div>
              
              {/* Content Renderer */}
              {viewMode === 'preview' ? (
                <div className="space-y-6">
                  {/* Legacy Social Post Preview (Only if not mapped to new AI API types) */}
                  {(post.contentType === 'linkedin' || post.contentType === 'facebook' || post.contentType === 'instagram') && !post.isQuickPost && typeof post.content === 'object' && post.content !== null && 'content' in post.content && !(post.content as any).sections && actualContentType !== 'Post' && actualContentType !== 'Article' && (
                    <div className="card">
                      {/* Author Header */}
                      <div className="flex items-center gap-3 mb-4">
                        <Avatar
                          src={post.createdBy.avatar}
                          name={post.createdBy.name}
                          className="w-12 h-12"
                        />
                        <div>
                          <div className="font-semibold text-text-primary">{post.createdBy.name}</div>
                          <div className="text-sm text-text-tertiary">Product Marketing Manager</div>
                          <div className="text-xs text-text-quaternary">2h • 🌐</div>
                        </div>
                      </div>

                      {/* Post Content */}
                      <div className="space-y-4">
                        <MarkdownRenderer
                          content={(post.content as SocialContent).content}
                          className="text-sm"
                        />

                        {/* Hashtags */}
                        {(post.content as SocialContent).hashtags && (
                          <div className="flex flex-wrap gap-1">
                            {(post.content as SocialContent).hashtags?.map((tag, index) => (
                              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Image Carousel */}
                        {(post.content as SocialContent).images && (post.content as SocialContent).images!.length > 0 && (
                          <div className="relative">
                            <img 
                              src={(post.content as SocialContent).images![activeImageIndex]} 
                              alt="Post content"
                              className="w-full rounded-lg"
                            />
                            {(post.content as SocialContent).images!.length > 1 && (
                              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                                {(post.content as SocialContent).images!.map((_, index) => (
                                  <button
                                    key={index}
                                    onClick={() => setActiveImageIndex(index)}
                                    className={cn(
                                      'w-2 h-2 rounded-full transition-all duration-200',
                                      index === activeImageIndex ? 'bg-white scale-125' : 'bg-white/50 hover:bg-white/75'
                                    )}
                                  />
                                ))}
                              </div>
                            )}
                          </div>
                        )}

                        {/* Engagement Bar */}
                        <div className="flex items-center justify-between pt-3 border-t border-dark-quaternary">
                          <div className="flex items-center gap-6">
                            <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                              <span>👍</span>
                              <span className="text-sm">Like</span>
                            </button>
                            <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                              <MessageCircle className="w-4 h-4" />
                              <span className="text-sm">Comment</span>
                            </button>
                            <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                              <span>↗️</span>
                              <span className="text-sm">Share</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Legacy LinkedIn Article Preview (Only if not mapped to new AI API Article type) */}
                  {post.contentType === 'linkedin' && !post.isQuickPost && typeof post.content === 'object' && post.content !== null && 'sections' in post.content && actualContentType !== 'Article' && (
                    <div className="card">
                      <div className="max-w-4xl mx-auto">
                        {/* Author Info */}
                        <div className="flex items-center gap-3 mb-8 pb-6 border-b border-dark-quaternary">
                          <Avatar
                            src={post.createdBy.avatar}
                            name={post.createdBy.name}
                            className="w-12 h-12"
                          />
                          <div>
                            <div className="font-semibold text-text-primary">{post.createdBy.name}</div>
                            <div className="text-sm text-text-tertiary">Product Marketing Manager</div>
                            <div className="text-xs text-text-quaternary">Published on LinkedIn • 2h</div>
                          </div>
                        </div>

                        {/* Article Sections */}
                        <div className="space-y-8">
                          {(post.content as BlogContent).sections.map((section) => (
                            <div key={section.id} className="space-y-4">
                              <h2 className="text-xl font-semibold text-text-primary">
                                {section.title}
                              </h2>
                              <p className="text-text-secondary leading-relaxed">
                                {section.content}
                              </p>
                            </div>
                          ))}
                        </div>

                        {/* Article Footer */}
                        <div className="mt-12 pt-8 border-t border-dark-quaternary">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-6">
                              <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                                <span>👍</span>
                                <span className="text-sm">Like</span>
                              </button>
                              <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                                <MessageCircle className="w-4 h-4" />
                                <span className="text-sm">Comment</span>
                              </button>
                              <button className="flex items-center gap-2 text-text-tertiary hover:text-brand-500 transition-colors duration-200">
                                <span>↗️</span>
                                <span className="text-sm">Share</span>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Email Preview (Legacy and New AI API Content Type) */}
                  {(post.contentType === 'email' || post.contentType === 'Email' || actualContentType === 'Email') && (
                    <div className="card">
                      <div className="max-w-2xl mx-auto">
                        {/* Email Header */}
                        <div className="mb-6 pb-4 border-b border-dark-quaternary">
                          <div className="text-xs text-text-tertiary mb-1">Subject:</div>
                          <div className="font-semibold text-text-primary mb-2">
                            {(post.content as EmailContent).subject}
                          </div>
                          <div className="text-xs text-text-tertiary mb-1">Preheader:</div>
                          <div className="text-sm text-text-secondary">
                            {(post.content as EmailContent).preheader}
                          </div>
                        </div>

                        {/* Email Body */}
                        {(post.content as EmailContent).body && (
                          <div className="mb-6">
                            <MarkdownRenderer
                              content={(post.content as EmailContent).body || ''}
                              className="text-sm leading-relaxed"
                            />
                          </div>
                        )}

                        {/* Email Sections */}
                        <div className="space-y-6">
                          {(post.content as EmailContent).sections && (post.content as EmailContent).sections.length > 0 ? (
                            (post.content as EmailContent).sections.map((section) => (
                              <div key={section.id} className="space-y-3">
                                {section.type === 'hero' && (
                                  <div className="text-center">
                                    {section.imageUrl && (
                                      <img src={section.imageUrl} alt="" className="w-full rounded-lg mb-4" />
                                    )}
                                    <h2 className="text-xl font-bold text-text-primary mb-2">{section.title}</h2>
                                    <MarkdownRenderer
                                      content={section.content || ''}
                                      className="text-sm"
                                    />
                                  </div>
                                )}

                                {section.type === 'content' && (
                                  <div>
                                    <h3 className="text-lg font-semibold text-text-primary mb-2">{section.title}</h3>
                                    <MarkdownRenderer
                                      content={section.content || ''}
                                      className="text-sm"
                                    />
                                  </div>
                                )}

                                {section.type === 'cta' && (
                                  <div className="text-center py-4">
                                    <h3 className="text-lg font-semibold text-text-primary mb-3">{section.title}</h3>
                                    <MarkdownRenderer
                                      content={section.content || ''}
                                      className="text-sm mb-4"
                                    />
                                    <button className="btn btn-primary">
                                      {section.buttonText}
                                    </button>
                                  </div>
                                )}
                              </div>
                            ))
                          ) : (
                            /* Show main email body when no sections are available */
                            <div className="prose prose-invert max-w-none">
                              {(post.content as EmailContent).body ? (
                                <MarkdownRenderer
                                  content={(post.content as EmailContent).body}
                                  className="text-sm leading-relaxed"
                                />
                              ) : (
                                <div className="text-center py-8 text-text-tertiary">
                                  <p>Email content will be displayed here.</p>
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Email Footer */}
                        <div className="mt-8 pt-6 border-t border-dark-quaternary text-center">
                          <div className="text-xs text-text-tertiary whitespace-pre-line">
                            {(post.content as EmailContent).footer}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* YouTube Preview */}
                  {post.contentType === 'youtube' && (
                    <div className="card">
                      <div className="space-y-6">
                        {/* Video Thumbnail */}
                        {(post.content as YouTubeContent).thumbnailUrl && (
                          <div className="relative">
                            <img
                              src={(post.content as YouTubeContent).thumbnailUrl}
                              alt="Video thumbnail"
                              className="w-full rounded-lg"
                            />
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center">
                                <div className="w-0 h-0 border-l-[8px] border-l-white border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1"></div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Video Details */}
                        <div className="space-y-4">
                          <h3 className="text-lg font-semibold text-text-primary">{getContentTitle()}</h3>
                          <p className="text-sm text-text-secondary leading-relaxed">
                            {(post.content as YouTubeContent).description}
                          </p>

                          {/* Hashtags */}
                          {typeof post.content === 'object' && post.content !== null && 'hashtags' in post.content && post.content.hashtags && (
                            <div className="flex flex-wrap gap-1">
                              {post.content.hashtags.map((tag, index) => (
                                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Script Sections */}
                        <div className="space-y-4">
                          <h4 className="text-md font-semibold text-text-primary">Video Script</h4>
                          <div className="space-y-4">
                            {(post.content as YouTubeContent).script && Array.isArray((post.content as YouTubeContent).script) && (post.content as YouTubeContent).script.length > 0 ? (
                              (post.content as YouTubeContent).script.map((section) => (
                                <div key={section.id} className="bg-dark-tertiary rounded-lg p-4">
                                  <div className="flex items-center gap-3 mb-2">
                                    <span className="text-xs font-medium text-text-tertiary bg-dark-quaternary px-2 py-1 rounded">
                                      {section.timestamp}
                                    </span>
                                    <span className={cn(
                                      'text-xs font-medium px-2 py-1 rounded',
                                      section.type === 'intro' ? 'bg-success-500/10 text-success-500' :
                                      section.type === 'outro' ? 'bg-info-500/10 text-info-500' :
                                      'bg-dark-quaternary text-text-secondary'
                                    )}>
                                      {section.type}
                                    </span>
                                  </div>
                                  <h5 className="font-medium text-text-primary mb-2">{section.title}</h5>
                                  <MarkdownRenderer
                                    content={section.content}
                                    className="text-sm"
                                  />
                                </div>
                              ))
                            ) : (
                              <div className="bg-dark-tertiary rounded-lg p-4 text-center">
                                <p className="text-sm text-text-secondary">No video script available</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Blog/Article/Medium Preview */}
                  {(post.contentType === 'blog' || post.contentType === 'medium') && (
                    <div className="card">
                      <div className="max-w-4xl mx-auto">
                        {/* Featured Image */}
                        {(post.content as BlogContent).featuredImage && (
                          <div className="mb-8">
                            <img
                              src={(post.content as BlogContent).featuredImage}
                              alt="Featured image"
                              className="w-full rounded-lg"
                            />
                          </div>
                        )}

                        {/* Article Header */}
                        <div className="mb-8">
                          <h1 className="text-3xl font-bold text-text-primary mb-4">
                            {(post.content as BlogContent).title}
                          </h1>
                          {(post.content as BlogContent).subheading && (
                            <p className="text-lg text-text-secondary leading-relaxed">
                              {(post.content as BlogContent).subheading}
                            </p>
                          )}
                        </div>

                        {/* Article Sections */}
                        <div className="space-y-8">
                          {(post.content as BlogContent).sections.map((section) => (
                            <div key={section.id} className="space-y-4">
                              <h2 className="text-xl font-semibold text-text-primary">
                                {section.title}
                              </h2>
                              <MarkdownRenderer
                                content={section.content}
                                className="prose prose-invert max-w-none"
                                forceMarkdown={true}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* LinkedIn Carousel Preview */}
                  {post.contentType === 'carousel' && (
                    <div className="space-y-6">
                      {/* Carousel Header */}
                      <div className="card">
                        <div className="p-6">
                          <h3 className="text-lg font-semibold text-text-primary mb-4">
                            {(post.content as CarouselContent).title}
                          </h3>
                          {(post.content as CarouselContent).hashtags && (post.content as CarouselContent).hashtags!.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {(post.content as CarouselContent).hashtags!.map((tag, index) => (
                                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Carousel Slides */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {(post.content as CarouselContent).slides.map((slide, index) => (
                          <div key={slide.id} className="card">
                            <div className="p-6">
                              <div className="flex items-center justify-between mb-4">
                                <span className="text-xs font-medium text-text-tertiary bg-dark-tertiary px-2 py-1 rounded">
                                  Slide {index + 1}
                                </span>
                              </div>

                              {slide.imageUrl && (
                                <div className="mb-4">
                                  <img
                                    src={slide.imageUrl}
                                    alt={`Slide ${index + 1}`}
                                    className="w-full rounded-lg"
                                  />
                                </div>
                              )}

                              {slide.title && (
                                <h4 className="font-semibold text-text-primary mb-3">
                                  {slide.title}
                                </h4>
                              )}

                              <MarkdownRenderer
                                content={slide.content}
                                className="text-sm"
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Twitter Thread Preview */}
                  {post.contentType === 'twitter' && !post.isQuickPost && (
                    <div className="space-y-4">
                      {(post.content as TwitterContent).tweets.map((tweet, index) => (
                        <div key={tweet.id} className="card">
                          <div className="p-6">
                            <div className="flex items-start gap-3">
                              <Avatar
                                src={post.createdBy.avatar}
                                name={post.createdBy.name}
                                className="w-10 h-10 flex-shrink-0"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="font-semibold text-text-primary">{post.createdBy.name}</span>
                                  <span className="text-text-tertiary">@{post.createdBy.name.toLowerCase().replace(' ', '')}</span>
                                  <span className="text-text-tertiary">·</span>
                                  <span className="text-text-tertiary text-sm">now</span>
                                </div>

                                <div className="space-y-3">
                                  <MarkdownRenderer
                                    content={tweet.content}
                                    className="text-text-secondary leading-relaxed"
                                  />

                                  {tweet.hashtags && tweet.hashtags.length > 0 && (
                                    <div className="flex flex-wrap gap-1">
                                      {tweet.hashtags.map((tag, tagIndex) => (
                                        <span key={tagIndex} className="text-blue-400 hover:underline cursor-pointer text-sm">
                                          {tag}
                                        </span>
                                      ))}
                                    </div>
                                  )}
                                </div>

                                {index < (post.content as TwitterContent).tweets.length - 1 && (
                                  <div className="mt-4 flex items-center gap-2 text-text-tertiary text-sm">
                                    <div className="w-0.5 h-4 bg-text-quaternary"></div>
                                    <span>Thread continues...</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Quick Post Content Preview */}
                  {post.isQuickPost && (
                    <div className="card">
                      <div className="p-6">
                        <div className="flex items-start gap-3">
                          <Avatar
                            src={post.createdBy?.avatar}
                            name={post.createdBy?.name || 'User'}
                            className="w-10 h-10 flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="font-semibold text-text-primary">{post.createdBy?.name || 'User'}</span>
                              <span className="text-text-tertiary">@{(post.createdBy?.name || 'user').toLowerCase().replace(' ', '')}</span>
                              <span className="text-text-tertiary">·</span>
                              <span className="text-text-tertiary text-sm">now</span>
                            </div>

                            <div className="space-y-3">
                              <MarkdownRenderer
                                content={typeof post.content === 'string' ? post.content : ''}
                                className="text-text-secondary leading-relaxed"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Article Preview (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Article' || actualContentType === 'Article') && (
                    <div className="card">
                      <div className="max-w-4xl mx-auto">
                        {/* Article Header */}
                        <div className="mb-8">
                          {(post.content as any).title && (
                            <h1 className="text-3xl font-bold text-text-primary mb-4">
                              {(post.content as any).title}
                            </h1>
                          )}
                          {(post.content as any).subheading && (
                            <p className="text-lg text-text-secondary mb-6 italic">
                              {(post.content as any).subheading}
                            </p>
                          )}

                          {/* Author Info */}
                          <div className="flex items-center gap-3 pb-6 border-b border-dark-quaternary">
                            <Avatar
                              src={post.createdBy.avatar}
                              name={post.createdBy.name}
                              className="w-12 h-12"
                            />
                            <div>
                              <div className="font-semibold text-text-primary">{post.createdBy.name}</div>
                              <div className="text-sm text-text-tertiary">
                                {formatDate(post.scheduledDate || new Date().toISOString())}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Article Sections */}
                        {(post.content as any).sections && (post.content as any).sections.length > 0 ? (
                          <div className="space-y-8">
                            {(post.content as any).sections.map((section: any, index: number) => (
                              <div key={index} className="prose prose-invert max-w-none">
                                {section.Introduction && (
                                  <div className="mb-6">
                                    <h2 className="text-xl font-semibold text-text-primary mb-4">Introduction</h2>
                                    <MarkdownRenderer content={section.Introduction} />
                                  </div>
                                )}
                                {section.Description && (
                                  <div>
                                    <h2 className="text-xl font-semibold text-text-primary mb-4">Description</h2>
                                    <MarkdownRenderer content={section.Description} />
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          /* Fallback content when sections are missing */
                          <div className="prose prose-invert max-w-none">
                            {(post.content as any).content && (
                              <MarkdownRenderer content={(post.content as any).content} />
                            )}
                            {!(post.content as any).content && (
                              <div className="text-center py-8 text-text-tertiary">
                                <p>Article content will be displayed here once sections are added.</p>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Article Summary */}
                        {(post.content as any).summary && (
                          <div className="mt-8 p-6 bg-dark-tertiary rounded-lg">
                            <h3 className="text-lg font-semibold text-text-primary mb-3">Summary</h3>
                            <MarkdownRenderer content={(post.content as any).summary} />
                          </div>
                        )}

                        {/* Hashtags */}
                        {(post.content as any).hashtags && (post.content as any).hashtags.length > 0 && (
                          <div className="mt-8 pt-6 border-t border-dark-quaternary">
                            <div className="flex flex-wrap gap-2">
                              {(post.content as any).hashtags.map((tag: string, index: number) => (
                                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                  {tag.startsWith('#') ? tag : `#${tag}`}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Post Preview (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Post' || actualContentType === 'Post') && (
                    <div className="card">
                      {/* Author Header */}
                      <div className="flex items-center gap-3 mb-4">
                        <Avatar
                          src={post.createdBy.avatar}
                          name={post.createdBy.name}
                          className="w-12 h-12"
                        />
                        <div>
                          <div className="font-semibold text-text-primary">{post.createdBy.name}</div>
                          <div className="text-sm text-text-tertiary">Product Marketing Manager</div>
                          <div className="text-xs text-text-quaternary">
                            {formatDate(post.scheduledDate || new Date().toISOString())} • 🌐
                          </div>
                        </div>
                      </div>

                      {/* Post Content */}
                      <div className="space-y-4">
                        {(post.content as any).title && (
                          <h2 className="text-lg font-semibold text-text-primary">
                            {(post.content as any).title}
                          </h2>
                        )}

                        {(post.content as any).content && (
                          <MarkdownRenderer
                            content={(post.content as any).content}
                            className="text-sm"
                          />
                        )}

                        {/* Hashtags */}
                        {(post.content as any).hashtags && (post.content as any).hashtags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {(post.content as any).hashtags.map((tag: string, index: number) => (
                              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                {tag.startsWith('#') ? tag : `#${tag}`}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Post Actions */}
                      <div className="flex items-center justify-between pt-4 mt-4 border-t border-dark-quaternary">
                        <div className="flex items-center gap-6">
                          <button className="flex items-center gap-2 text-text-tertiary hover:text-text-secondary transition-colors">
                            <Heart className="w-5 h-5" />
                            <span className="text-sm">Like</span>
                          </button>
                          <button className="flex items-center gap-2 text-text-tertiary hover:text-text-secondary transition-colors">
                            <MessageCircle className="w-5 h-5" />
                            <span className="text-sm">Comment</span>
                          </button>
                          <button className="flex items-center gap-2 text-text-tertiary hover:text-text-secondary transition-colors">
                            <Share2 className="w-5 h-5" />
                            <span className="text-sm">Share</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Poll Preview (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Poll' || actualContentType === 'Poll') && (
                    <div className="card">
                      {/* Author Header */}
                      <div className="flex items-center gap-3 mb-4">
                        <Avatar
                          src={post.createdBy.avatar}
                          name={post.createdBy.name}
                          className="w-12 h-12"
                        />
                        <div>
                          <div className="font-semibold text-text-primary">{post.createdBy.name}</div>
                          <div className="text-sm text-text-tertiary">Product Marketing Manager</div>
                          <div className="text-xs text-text-quaternary">
                            {formatDate(post.scheduledDate || new Date().toISOString())} • 🌐
                          </div>
                        </div>
                      </div>

                      {/* Poll Content */}
                      <div className="space-y-4">
                        {(post.content as any).question && (
                          <h2 className="text-lg font-semibold text-text-primary">
                            {(post.content as any).question}
                          </h2>
                        )}

                        {/* Poll Options */}
                        {(post.content as any).options && (post.content as any).options.length > 0 && (
                          <div className="space-y-3">
                            {(post.content as any).options.map((option: any, index: number) => (
                              <div key={index} className="border border-dark-quaternary rounded-lg p-4 hover:bg-dark-tertiary transition-colors cursor-pointer">
                                <div className="flex items-center justify-between">
                                  <span className="text-text-primary">{option.option}</span>
                                  <span className="text-sm text-text-tertiary">{option.votes} votes</span>
                                </div>
                                <div className="mt-2 bg-dark-quaternary rounded-full h-2">
                                  <div
                                    className="bg-brand-500 h-2 rounded-full transition-all duration-300"
                                    style={{
                                      width: `${Math.max(5, (option.votes / Math.max(1, Math.max(...(post.content as any).options.map((o: any) => o.votes)))) * 100)}%`
                                    }}
                                  />
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Poll Expiration */}
                        {(post.content as any).expirationDate && (
                          <div className="text-sm text-text-tertiary">
                            Poll expires: {formatDate((post.content as any).expirationDate)}
                          </div>
                        )}

                        {/* Hashtags */}
                        {(post.content as any).hashtags && (post.content as any).hashtags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {(post.content as any).hashtags.map((tag: string, index: number) => (
                              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                {tag.startsWith('#') ? tag : `#${tag}`}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Video Preview (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Video' || actualContentType === 'Video') && (
                    <div className="card">
                      <div className="space-y-6">
                        {/* Video Thumbnail Placeholder */}
                        <div className="relative bg-black rounded-lg aspect-video flex items-center justify-center">
                          <div className="text-center">
                            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                              <div className="w-0 h-0 border-l-[12px] border-l-white border-y-[8px] border-y-transparent ml-1"></div>
                            </div>
                            <p className="text-white/80 text-sm">Video Preview</p>
                          </div>
                        </div>

                        {/* Video Info */}
                        <div className="space-y-4">
                          {(post.content as any).title && (
                            <h2 className="text-lg font-semibold text-text-primary">
                              {(post.content as any).title}
                            </h2>
                          )}

                          {(post.content as any).desc && (
                            <MarkdownRenderer
                              content={(post.content as any).desc}
                              className="text-sm"
                            />
                          )}

                          {/* Video Script Preview */}
                          {(post.content as any).videoScript && (
                            <div className="bg-dark-tertiary rounded-lg p-4">
                              <h3 className="text-sm font-semibold text-text-primary mb-3">Video Script</h3>
                              <div className="space-y-3 text-sm">
                                {(post.content as any).videoScript.intro && (
                                  <div>
                                    <span className="text-brand-400 font-medium">
                                      [{(post.content as any).videoScript.intro.startTime}] Intro:
                                    </span>
                                    <p className="text-text-secondary mt-1">
                                      {(post.content as any).videoScript.intro.desc}
                                    </p>
                                  </div>
                                )}
                                {(post.content as any).videoScript.main && (post.content as any).videoScript.main.length > 0 && (
                                  <div>
                                    <span className="text-brand-400 font-medium">Main Content:</span>
                                    {(post.content as any).videoScript.main.slice(0, 2).map((section: any, index: number) => (
                                      <div key={index} className="mt-1">
                                        <span className="text-text-tertiary">
                                          [{section.startTime}] {section.title}:
                                        </span>
                                        <p className="text-text-secondary">{section.desc}</p>
                                      </div>
                                    ))}
                                    {(post.content as any).videoScript.main.length > 2 && (
                                      <p className="text-text-quaternary text-xs mt-1">
                                        +{(post.content as any).videoScript.main.length - 2} more sections
                                      </p>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Hashtags */}
                          {(post.content as any).hashtags && (post.content as any).hashtags.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {(post.content as any).hashtags.map((tag: string, index: number) => (
                                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                  {tag.startsWith('#') ? tag : `#${tag}`}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Thread Preview (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Thread' || actualContentType === 'Thread') && (
                    <div className="space-y-4">
                      {(post.content as any).thread && (post.content as any).thread.map((threadPost: any, index: number) => (
                        <div key={index} className="card">
                          <div className="p-6">
                            <div className="flex items-start gap-3">
                              <Avatar
                                src={post.createdBy.avatar}
                                name={post.createdBy.name}
                                className="w-10 h-10"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="font-semibold text-text-primary">{post.createdBy.name}</span>
                                  <span className="text-text-tertiary text-sm">@{post.createdBy.name.toLowerCase().replace(' ', '')}</span>
                                  <span className="text-text-quaternary text-sm">·</span>
                                  <span className="text-text-quaternary text-sm">
                                    {formatDate(post.scheduledDate || new Date().toISOString())}
                                  </span>
                                </div>
                                <MarkdownRenderer
                                  content={threadPost.content}
                                  className="text-sm mb-3"
                                />

                                {/* Thread indicator */}
                                {index < (post.content as any).thread.length - 1 && (
                                  <div className="flex items-center gap-2 text-text-tertiary text-sm">
                                    <MessageCircle className="w-4 h-4" />
                                    <span>Thread continues...</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Hashtags for the thread */}
                      {(post.content as any).hashtags && (post.content as any).hashtags.length > 0 && (
                        <div className="card p-4">
                          <div className="flex flex-wrap gap-1">
                            {(post.content as any).hashtags.map((tag: string, index: number) => (
                              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                {tag.startsWith('#') ? tag : `#${tag}`}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Press Release Preview (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'PressRelease' || actualContentType === 'PressRelease') && (
                    <div className="card">
                      <div className="max-w-4xl mx-auto">
                        {/* Press Release Header */}
                        <div className="text-center mb-8 pb-6 border-b border-dark-quaternary">
                          <div className="text-xs text-text-tertiary uppercase tracking-wide mb-4">Press Release</div>
                          {(post.content as any).headline && (
                            <h1 className="text-3xl font-bold text-text-primary mb-4">
                              {(post.content as any).headline}
                            </h1>
                          )}
                          {(post.content as any).subheadline && (
                            <p className="text-lg text-text-secondary mb-4 italic">
                              {(post.content as any).subheadline}
                            </p>
                          )}
                          {(post.content as any).dateline && (
                            <p className="text-sm text-text-tertiary">
                              {(post.content as any).dateline}
                            </p>
                          )}
                        </div>

                        {/* Lead Paragraph */}
                        {(post.content as any).lead_paragraph && (
                          <div className="mb-8">
                            <p className="text-lg text-text-primary leading-relaxed font-medium">
                              {(post.content as any).lead_paragraph}
                            </p>
                          </div>
                        )}

                        {/* Body Sections */}
                        {(post.content as any).body_sections && (post.content as any).body_sections.length > 0 && (
                          <div className="space-y-6 mb-8">
                            {(post.content as any).body_sections.map((section: any, index: number) => (
                              <div key={index}>
                                {section.heading && (
                                  <h2 className="text-xl font-semibold text-text-primary mb-4">
                                    {section.heading}
                                  </h2>
                                )}
                                {section.content && (
                                  <MarkdownRenderer content={section.content} />
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Boilerplate */}
                        {(post.content as any).boilerplate && (
                          <div className="mb-8 p-6 bg-dark-tertiary rounded-lg">
                            <h3 className="text-sm font-semibold text-text-primary mb-3 uppercase tracking-wide">About the Company</h3>
                            <MarkdownRenderer content={(post.content as any).boilerplate} />
                          </div>
                        )}

                        {/* Contact Information */}
                        {(post.content as any).contact_info && (
                          <div className="border-t border-dark-quaternary pt-6">
                            <h3 className="text-sm font-semibold text-text-primary mb-4 uppercase tracking-wide">Media Contact</h3>
                            <div className="space-y-2 text-sm">
                              {(post.content as any).contact_info.name && (
                                <p className="text-text-primary">
                                  <span className="font-medium">Name:</span> {(post.content as any).contact_info.name}
                                </p>
                              )}
                              {(post.content as any).contact_info.email && (
                                <p className="text-text-primary">
                                  <span className="font-medium">Email:</span>
                                  <a href={`mailto:${(post.content as any).contact_info.email}`} className="text-brand-500 hover:underline ml-1">
                                    {(post.content as any).contact_info.email}
                                  </a>
                                </p>
                              )}
                              {(post.content as any).contact_info.phone && (
                                <p className="text-text-primary">
                                  <span className="font-medium">Phone:</span>
                                  <a href={`tel:${(post.content as any).contact_info.phone}`} className="text-brand-500 hover:underline ml-1">
                                    {(post.content as any).contact_info.phone}
                                  </a>
                                </p>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Hashtags */}
                        {(post.content as any).hashtags && (post.content as any).hashtags.length > 0 && (
                          <div className="mt-8 pt-6 border-t border-dark-quaternary">
                            <div className="flex flex-wrap gap-2">
                              {(post.content as any).hashtags.map((tag: string, index: number) => (
                                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                  {tag.startsWith('#') ? tag : `#${tag}`}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Carousel Preview (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Carousel' || actualContentType === 'Carousel') && (
                    <div className="card">
                      {/* Author Header */}
                      <div className="flex items-center gap-3 mb-4">
                        <Avatar
                          src={post.createdBy.avatar}
                          name={post.createdBy.name}
                          className="w-12 h-12"
                        />
                        <div>
                          <div className="font-semibold text-text-primary">{post.createdBy.name}</div>
                          <div className="text-sm text-text-tertiary">Product Marketing Manager</div>
                          <div className="text-xs text-text-quaternary">
                            {formatDate(post.scheduledDate || new Date().toISOString())} • 🌐
                          </div>
                        </div>
                      </div>

                      {/* Carousel Content */}
                      <div className="space-y-4">
                        {(post.content as any).title && (
                          <h2 className="text-lg font-semibold text-text-primary">
                            {(post.content as any).title}
                          </h2>
                        )}

                        {/* Carousel Slides */}
                        {(post.content as any).slides && (post.content as any).slides.length > 0 && (
                          <div className="space-y-4">
                            <div className="text-sm text-text-tertiary mb-3">
                              Carousel with {(post.content as any).slides.length} slide{(post.content as any).slides.length !== 1 ? 's' : ''}
                            </div>

                            {/* Slide Preview Container */}
                            <div className="relative bg-dark-tertiary rounded-lg p-6">
                              {/* Slide Indicator */}
                              <div className="flex justify-center gap-2 mb-4">
                                {(post.content as any).slides.map((_: any, index: number) => (
                                  <div
                                    key={index}
                                    className={`w-2 h-2 rounded-full ${
                                      index === 0 ? 'bg-brand-500' : 'bg-dark-quaternary'
                                    }`}
                                  />
                                ))}
                              </div>

                              {/* First Slide Preview */}
                              {(post.content as any).slides[0] && (
                                <div className="text-center space-y-4">
                                  {/* Image Placeholder */}
                                  <div className="w-full h-48 bg-dark-quaternary rounded-lg flex items-center justify-center mb-4">
                                    <div className="text-center">
                                      <div className="w-16 h-16 bg-white/10 rounded-lg flex items-center justify-center mb-2 mx-auto">
                                        <Image className="w-8 h-8 text-white/60" />
                                      </div>
                                      <p className="text-text-quaternary text-sm">
                                        {(post.content as any).slides[0].image_description || 'Slide Image'}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Slide Content */}
                                  {(post.content as any).slides[0].title && (
                                    <h3 className="text-lg font-semibold text-text-primary">
                                      {(post.content as any).slides[0].title}
                                    </h3>
                                  )}

                                  {(post.content as any).slides[0].content && (
                                    <MarkdownRenderer
                                      content={(post.content as any).slides[0].content}
                                      className="text-sm"
                                    />
                                  )}
                                </div>
                              )}

                              {/* Navigation Arrows */}
                              <div className="absolute inset-y-0 left-2 flex items-center">
                                <button className="w-8 h-8 bg-black/20 rounded-full flex items-center justify-center text-white/60">
                                  <ChevronLeft className="w-4 h-4" />
                                </button>
                              </div>
                              <div className="absolute inset-y-0 right-2 flex items-center">
                                <button className="w-8 h-8 bg-black/20 rounded-full flex items-center justify-center text-white/60">
                                  <ChevronRight className="w-4 h-4" />
                                </button>
                              </div>
                            </div>

                            {/* Slides Summary */}
                            {(post.content as any).slides.length > 1 && (
                              <div className="text-xs text-text-quaternary">
                                <div className="flex flex-wrap gap-2">
                                  {(post.content as any).slides.slice(1, 4).map((slide: any, index: number) => (
                                    <div key={index} className="bg-dark-quaternary rounded px-2 py-1">
                                      Slide {index + 2}: {slide.title?.substring(0, 20)}...
                                    </div>
                                  ))}
                                  {(post.content as any).slides.length > 4 && (
                                    <div className="bg-dark-quaternary rounded px-2 py-1">
                                      +{(post.content as any).slides.length - 4} more
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Call to Action */}
                        {(post.content as any).call_to_action && (
                          <div className="mt-4 p-3 bg-brand-500/10 border border-brand-500/20 rounded-lg">
                            <div className="text-sm font-medium text-brand-400 mb-1">Call to Action</div>
                            <p className="text-sm text-text-secondary">{(post.content as any).call_to_action}</p>
                          </div>
                        )}

                        {/* Hashtags */}
                        {(post.content as any).hashtags && (post.content as any).hashtags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {(post.content as any).hashtags.map((tag: string, index: number) => (
                              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                                {tag.startsWith('#') ? tag : `#${tag}`}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Post Actions */}
                      <div className="flex items-center justify-between pt-4 mt-4 border-t border-dark-quaternary">
                        <div className="flex items-center gap-6">
                          <button className="flex items-center gap-2 text-text-tertiary hover:text-text-secondary transition-colors">
                            <Heart className="w-5 h-5" />
                            <span className="text-sm">Like</span>
                          </button>
                          <button className="flex items-center gap-2 text-text-tertiary hover:text-text-secondary transition-colors">
                            <MessageCircle className="w-5 h-5" />
                            <span className="text-sm">Comment</span>
                          </button>
                          <button className="flex items-center gap-2 text-text-tertiary hover:text-text-secondary transition-colors">
                            <Share2 className="w-5 h-5" />
                            <span className="text-sm">Share</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Fallback for truly unsupported content types */}
                  {!['linkedin', 'email', 'youtube', 'blog', 'medium', 'website-blog', 'carousel', 'twitter', 'facebook', 'instagram', 'Article', 'Post', 'Poll', 'Video', 'Thread', 'PressRelease', 'Carousel', 'Email'].includes(post.contentType) && (
                    <div className="bg-dark-tertiary rounded-xl p-6 text-center">
                      <p className="text-sm text-text-tertiary">Preview for {post.contentType} content coming soon...</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-6">
                  {/* LinkedIn/Facebook Social Edit Form */}
                  {(post.contentType === 'linkedin' || post.contentType === 'facebook') && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Title</label>
                        <input
                          type="text"
                          value={(editContent as SocialContent).title}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Post title..."
                        />
                      </div>

                      {/* Content */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Content</label>
                        <textarea
                          value={(editContent as SocialContent).content}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            content: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={8}
                          placeholder="What's happening?"
                        />
                        <div className="text-xs text-text-tertiary mt-1">
                          {(editContent as SocialContent).content.length} characters
                        </div>
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {(editContent as SocialContent).hashtags?.map((hashtag, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as SocialContent).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="search-input flex-1"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as SocialContent).hashtags || []).filter((_, i) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newHashtags = [...((editContent as SocialContent).hashtags || []), '']
                              handleContentChange({
                                ...editContent,
                                hashtags: newHashtags
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add hashtag
                          </button>
                        </div>
                      </div>

                      {/* Images */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Images</label>
                        <div className="space-y-3">
                          {(editContent as SocialContent).images?.map((image, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <div className="flex-1 flex items-center gap-3">
                                <img
                                  src={image}
                                  alt={`Image ${index + 1}`}
                                  className="w-12 h-12 rounded-lg object-cover border border-dark-quaternary"
                                />
                                <input
                                  type="url"
                                  value={image}
                                  onChange={(e) => {
                                    const newImages = [...((editContent as SocialContent).images || [])]
                                    newImages[index] = e.target.value
                                    handleContentChange({
                                      ...editContent,
                                      images: newImages
                                    })
                                  }}
                                  className="search-input flex-1"
                                  placeholder="Image URL..."
                                />
                              </div>
                              <button
                                onClick={() => {
                                  const newImages = ((editContent as SocialContent).images || []).filter((_, i) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    images: newImages
                                  })
                                }}
                                className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newImages = [...((editContent as SocialContent).images || []), '']
                              handleContentChange({
                                ...editContent,
                                images: newImages
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add image
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Email Edit Form (Legacy and New AI API Content Type) */}
                  {(post.contentType === 'email' || post.contentType === 'Email' || actualContentType === 'Email') && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Title</label>
                        <input
                          type="text"
                          value={editContent.title}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                        />
                      </div>

                      {/* Subject */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Subject Line</label>
                        <input
                          type="text"
                          value={(editContent as EmailContent).subject}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            subject: e.target.value
                          })}
                          className="w-full search-input"
                        />
                      </div>

                      {/* Preheader */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Preheader</label>
                        <input
                          type="text"
                          value={(editContent as EmailContent).preheader}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            preheader: e.target.value
                          })}
                          className="w-full search-input"
                        />
                      </div>

                      {/* Email Sections */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Email Sections</label>
                        <div className="space-y-4">
                          {(editContent as EmailContent).sections.map((section, index) => (
                            <div key={section.id} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <select
                                  value={section.type}
                                  onChange={(e) => {
                                    const newSections = [...(editContent as EmailContent).sections]
                                    newSections[index] = { ...section, type: e.target.value as any }
                                    handleContentChange({
                                      ...editContent,
                                      sections: newSections
                                    })
                                  }}
                                  className="search-input"
                                >
                                  <option value="hero">Hero</option>
                                  <option value="content">Content</option>
                                  <option value="cta">Call to Action</option>
                                  <option value="divider">Divider</option>
                                </select>
                                <button
                                  onClick={() => {
                                    const newSections = (editContent as EmailContent).sections.filter((_, i) => i !== index)
                                    handleContentChange({
                                      ...editContent,
                                      sections: newSections
                                    })
                                  }}
                                  className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                              
                              {section.title !== undefined && (
                                <input
                                  type="text"
                                  value={section.title || ''}
                                  onChange={(e) => {
                                    const newSections = [...(editContent as EmailContent).sections]
                                    newSections[index] = { ...section, title: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      sections: newSections
                                    })
                                  }}
                                  className="w-full search-input"
                                  placeholder="Section title..."
                                />
                              )}

                              {section.content !== undefined && (
                                <textarea
                                  value={section.content || ''}
                                  onChange={(e) => {
                                    const newSections = [...(editContent as EmailContent).sections]
                                    newSections[index] = { ...section, content: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      sections: newSections
                                    })
                                  }}
                                  className="w-full search-input resize-none"
                                  rows={3}
                                  placeholder="Section content..."
                                />
                              )}

                              {section.type === 'cta' && (
                                <div className="grid grid-cols-2 gap-3">
                                  <input
                                    type="text"
                                    value={section.buttonText || ''}
                                    onChange={(e) => {
                                      const newSections = [...(editContent as EmailContent).sections]
                                      newSections[index] = { ...section, buttonText: e.target.value }
                                      handleContentChange({
                                        ...editContent,
                                        sections: newSections
                                      })
                                    }}
                                    className="search-input"
                                    placeholder="Button text..."
                                  />
                                  <input
                                    type="url"
                                    value={section.buttonUrl || ''}
                                    onChange={(e) => {
                                      const newSections = [...(editContent as EmailContent).sections]
                                      newSections[index] = { ...section, buttonUrl: e.target.value }
                                      handleContentChange({
                                        ...editContent,
                                        sections: newSections
                                      })
                                    }}
                                    className="search-input"
                                    placeholder="Button URL..."
                                  />
                                </div>
                              )}
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSection = {
                                id: `section-${Date.now()}`,
                                type: 'content' as const,
                                title: '',
                                content: ''
                              }
                              handleContentChange({
                                ...editContent,
                                sections: [...(editContent as EmailContent).sections, newSection]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add section
                          </button>
                        </div>
                      </div>

                      {/* Footer */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Footer</label>
                        <textarea
                          value={(editContent as EmailContent).footer}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            footer: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={3}
                        />
                      </div>
                    </div>
                  )}

                  {/* YouTube Edit Form */}
                  {post.contentType === 'youtube' && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Video Title</label>
                        <input
                          type="text"
                          value={editContent.title}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                        />
                      </div>

                      {/* Description */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Description</label>
                        <textarea
                          value={(editContent as YouTubeContent).description}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            description: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={4}
                        />
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {editContent.hashtags?.map((hashtag, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...(editContent.hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="search-input flex-1"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newHashtags = [...(editContent.hashtags || []), '']
                              handleContentChange({
                                ...editContent,
                                hashtags: newHashtags
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add hashtag
                          </button>
                        </div>
                      </div>

                      {/* Script Sections */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Video Script</label>
                        <div className="space-y-4">
                          {(editContent as YouTubeContent).script && Array.isArray((editContent as YouTubeContent).script) && (editContent as YouTubeContent).script.map((section, index) => (
                            <div key={section.id} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between gap-3">
                                <div className="flex items-center gap-3">
                                  <select
                                    value={section.type}
                                    onChange={(e) => {
                                      const newScript = [...(editContent as YouTubeContent).script]
                                      newScript[index] = { ...section, type: e.target.value as any }
                                      handleContentChange({
                                        ...editContent,
                                        script: newScript
                                      })
                                    }}
                                    className="search-input"
                                  >
                                    <option value="intro">Intro</option>
                                    <option value="main">Main</option>
                                    <option value="outro">Outro</option>
                                    <option value="timestamp">Timestamp</option>
                                  </select>
                                  <input
                                    type="text"
                                    value={section.timestamp || ''}
                                    onChange={(e) => {
                                      const newScript = [...(editContent as YouTubeContent).script]
                                      newScript[index] = { ...section, timestamp: e.target.value }
                                      handleContentChange({
                                        ...editContent,
                                        script: newScript
                                      })
                                    }}
                                    className="search-input w-20"
                                    placeholder="00:00"
                                  />
                                </div>
                                <button
                                  onClick={() => {
                                    const newScript = (editContent as YouTubeContent).script.filter((_, i) => i !== index)
                                    handleContentChange({
                                      ...editContent,
                                      script: newScript
                                    })
                                  }}
                                  className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                              
                              <input
                                type="text"
                                value={section.title}
                                onChange={(e) => {
                                  const newScript = [...(editContent as YouTubeContent).script]
                                  newScript[index] = { ...section, title: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    script: newScript
                                  })
                                }}
                                className="w-full search-input"
                                placeholder="Section title..."
                              />

                              <textarea
                                value={section.content}
                                onChange={(e) => {
                                  const newScript = [...(editContent as YouTubeContent).script]
                                  newScript[index] = { ...section, content: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    script: newScript
                                  })
                                }}
                                className="w-full search-input resize-none"
                                rows={3}
                                placeholder="Script content..."
                              />
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSection = {
                                id: `script-${Date.now()}`,
                                type: 'main' as const,
                                title: '',
                                content: '',
                                timestamp: ''
                              }
                              handleContentChange({
                                ...editContent,
                                script: [...((editContent as YouTubeContent).script || []), newSection]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add script section
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Blog/Article/Medium Edit Form */}
                  {(post.contentType === 'blog' || post.contentType === 'medium') && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Article Title</label>
                        <input
                          type="text"
                          value={(editContent as BlogContent).title}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter article title..."
                        />
                      </div>

                      {/* Subheading */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Subheading (Optional)</label>
                        <input
                          type="text"
                          value={(editContent as BlogContent).subheading || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            subheading: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter subheading or subtitle..."
                        />
                      </div>

                      {/* Featured Image */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Featured Image URL (Optional)</label>
                        <input
                          type="url"
                          value={(editContent as BlogContent).featuredImage || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            featuredImage: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="https://example.com/image.jpg"
                        />
                      </div>

                      {/* Article Sections */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Article Sections</label>
                        <div className="space-y-4">
                          {(editContent as BlogContent).sections.map((section, index) => (
                            <div key={section.id} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">Section {index + 1}</span>
                                <button
                                  onClick={() => {
                                    const newSections = (editContent as BlogContent).sections.filter((_, i) => i !== index)
                                    handleContentChange({
                                      ...editContent,
                                      sections: newSections
                                    })
                                  }}
                                  className="p-1 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded transition-colors"
                                >
                                  <Minus className="w-4 h-4" />
                                </button>
                              </div>

                              <input
                                type="text"
                                value={section.title}
                                onChange={(e) => {
                                  const newSections = [...(editContent as BlogContent).sections]
                                  newSections[index] = { ...section, title: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    sections: newSections
                                  })
                                }}
                                className="w-full search-input"
                                placeholder="Section title..."
                              />

                              <textarea
                                value={section.content}
                                onChange={(e) => {
                                  const newSections = [...(editContent as BlogContent).sections]
                                  newSections[index] = { ...section, content: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    sections: newSections
                                  })
                                }}
                                className="w-full search-input resize-none"
                                rows={6}
                                placeholder="Section content..."
                              />
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSection = {
                                id: `section-${Date.now()}`,
                                title: '',
                                content: ''
                              }
                              handleContentChange({
                                ...editContent,
                                sections: [...(editContent as BlogContent).sections, newSection]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Section
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* LinkedIn Carousel Edit Form */}
                  {post.contentType === 'carousel' && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Carousel Title</label>
                        <input
                          type="text"
                          value={(editContent as CarouselContent).title}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter carousel title..."
                        />
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {((editContent as CarouselContent).hashtags || []).map((hashtag, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as CarouselContent).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="search-input flex-1"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as CarouselContent).hashtags || []).filter((_, i) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newHashtags = [...((editContent as CarouselContent).hashtags || []), '']
                              handleContentChange({
                                ...editContent,
                                hashtags: newHashtags
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Hashtag
                          </button>
                        </div>
                      </div>

                      {/* Carousel Slides */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Carousel Slides</label>
                        <div className="space-y-4">
                          {(editContent as CarouselContent).slides.map((slide, index) => (
                            <div key={slide.id} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">Slide {index + 1}</span>
                                <button
                                  onClick={() => {
                                    const newSlides = (editContent as CarouselContent).slides.filter((_, i) => i !== index)
                                    handleContentChange({
                                      ...editContent,
                                      slides: newSlides
                                    })
                                  }}
                                  className="p-1 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded transition-colors"
                                >
                                  <Minus className="w-4 h-4" />
                                </button>
                              </div>

                              <input
                                type="text"
                                value={slide.title || ''}
                                onChange={(e) => {
                                  const newSlides = [...(editContent as CarouselContent).slides]
                                  newSlides[index] = { ...slide, title: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    slides: newSlides
                                  })
                                }}
                                className="w-full search-input"
                                placeholder="Slide title (optional)..."
                              />

                              <textarea
                                value={slide.content}
                                onChange={(e) => {
                                  const newSlides = [...(editContent as CarouselContent).slides]
                                  newSlides[index] = { ...slide, content: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    slides: newSlides
                                  })
                                }}
                                className="w-full search-input resize-none"
                                rows={4}
                                placeholder="Slide content..."
                              />

                              <input
                                type="url"
                                value={slide.imageUrl || ''}
                                onChange={(e) => {
                                  const newSlides = [...(editContent as CarouselContent).slides]
                                  newSlides[index] = { ...slide, imageUrl: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    slides: newSlides
                                  })
                                }}
                                className="w-full search-input"
                                placeholder="Image URL (optional)..."
                              />
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSlide = {
                                id: `slide-${Date.now()}`,
                                title: '',
                                content: '',
                                imageUrl: ''
                              }
                              handleContentChange({
                                ...editContent,
                                slides: [...(editContent as CarouselContent).slides, newSlide]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Slide
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Twitter Thread Edit Form */}
                  {post.contentType === 'twitter' && (
                    <div className="space-y-6">
                      {/* Twitter Thread */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Twitter Thread</label>
                        <div className="space-y-4">
                          {(editContent as TwitterContent).tweets.map((tweet, index) => (
                            <div key={tweet.id} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">
                                  {index === 0 ? 'Main Tweet' : `Tweet ${index + 1}`}
                                </span>
                                {(editContent as TwitterContent).tweets.length > 1 && (
                                  <button
                                    onClick={() => {
                                      const newTweets = (editContent as TwitterContent).tweets.filter((_, i) => i !== index)
                                      handleContentChange({
                                        ...editContent,
                                        tweets: newTweets
                                      })
                                    }}
                                    className="p-1 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded transition-colors"
                                  >
                                    <Minus className="w-4 h-4" />
                                  </button>
                                )}
                              </div>

                              <textarea
                                value={tweet.content}
                                onChange={(e) => {
                                  const newTweets = [...(editContent as TwitterContent).tweets]
                                  newTweets[index] = { ...tweet, content: e.target.value }
                                  handleContentChange({
                                    ...editContent,
                                    tweets: newTweets
                                  })
                                }}
                                className="w-full search-input resize-none"
                                rows={3}
                                placeholder="What's happening?"
                                maxLength={280}
                              />
                              <div className="text-xs text-text-tertiary text-right">
                                {tweet.content.length}/280 characters
                              </div>

                              {/* Tweet Hashtags */}
                              <div>
                                <label className="text-sm font-medium text-text-secondary block mb-2">Hashtags</label>
                                <div className="space-y-2">
                                  {(tweet.hashtags || []).map((hashtag, hashtagIndex) => (
                                    <div key={hashtagIndex} className="flex items-center gap-3">
                                      <input
                                        type="text"
                                        value={hashtag}
                                        onChange={(e) => {
                                          const newTweets = [...(editContent as TwitterContent).tweets]
                                          const newHashtags = [...(tweet.hashtags || [])]
                                          newHashtags[hashtagIndex] = e.target.value
                                          newTweets[index] = { ...tweet, hashtags: newHashtags }
                                          handleContentChange({
                                            ...editContent,
                                            tweets: newTweets
                                          })
                                        }}
                                        className="search-input flex-1"
                                        placeholder="#hashtag"
                                      />
                                      <button
                                        onClick={() => {
                                          const newTweets = [...(editContent as TwitterContent).tweets]
                                          const newHashtags = (tweet.hashtags || []).filter((_, i) => i !== hashtagIndex)
                                          newTweets[index] = { ...tweet, hashtags: newHashtags }
                                          handleContentChange({
                                            ...editContent,
                                            tweets: newTweets
                                          })
                                        }}
                                        className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                                      >
                                        <Minus className="w-4 h-4" />
                                      </button>
                                    </div>
                                  ))}
                                  <button
                                    onClick={() => {
                                      const newTweets = [...(editContent as TwitterContent).tweets]
                                      const newHashtags = [...(tweet.hashtags || []), '']
                                      newTweets[index] = { ...tweet, hashtags: newHashtags }
                                      handleContentChange({
                                        ...editContent,
                                        tweets: newTweets
                                      })
                                    }}
                                    className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                                  >
                                    <Plus className="w-4 h-4" />
                                    Add Hashtag
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newTweet = {
                                id: `tweet-${Date.now()}`,
                                content: '',
                                hashtags: []
                              }
                              handleContentChange({
                                ...editContent,
                                tweets: [...(editContent as TwitterContent).tweets, newTweet]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Tweet to Thread
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Article Edit Form (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Article' || actualContentType === 'Article') && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Article Title</label>
                        <input
                          type="text"
                          value={(editContent as any).title || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter article title..."
                        />
                      </div>

                      {/* Subheading */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Subheading</label>
                        <input
                          type="text"
                          value={(editContent as any).subheading || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            subheading: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter article subheading..."
                        />
                      </div>

                      {/* Article Sections */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Article Sections</label>
                        <div className="space-y-4">
                          {((editContent as any).sections || []).map((section: any, index: number) => (
                            <div key={index} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">Section {index + 1}</span>
                                {((editContent as any).sections || []).length > 1 && (
                                  <button
                                    onClick={() => {
                                      const newSections = ((editContent as any).sections || []).filter((_: any, i: number) => i !== index)
                                      handleContentChange({
                                        ...editContent,
                                        sections: newSections
                                      })
                                    }}
                                    className="text-red-400 hover:text-red-300 transition-colors"
                                  >
                                    <X className="w-4 h-4" />
                                  </button>
                                )}
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Introduction</label>
                                <textarea
                                  value={section.Introduction || ''}
                                  onChange={(e) => {
                                    const newSections = [...((editContent as any).sections || [])]
                                    newSections[index] = { ...section, Introduction: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      sections: newSections
                                    })
                                  }}
                                  className="w-full search-input resize-none"
                                  rows={3}
                                  placeholder="Section introduction..."
                                />
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Description</label>
                                <textarea
                                  value={section.Description || ''}
                                  onChange={(e) => {
                                    const newSections = [...((editContent as any).sections || [])]
                                    newSections[index] = { ...section, Description: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      sections: newSections
                                    })
                                  }}
                                  className="w-full search-input resize-none"
                                  rows={4}
                                  placeholder="Section description..."
                                />
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSection = { Introduction: '', Description: '' }
                              handleContentChange({
                                ...editContent,
                                sections: [...((editContent as any).sections || []), newSection]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Section
                          </button>
                        </div>
                      </div>

                      {/* Summary */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Summary</label>
                        <textarea
                          value={(editContent as any).summary || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            summary: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={3}
                          placeholder="Article summary..."
                        />
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {((editContent as any).hashtags || []).map((hashtag: string, index: number) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as any).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="flex-1 search-input"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as any).hashtags || []).filter((_: string, i: number) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              handleContentChange({
                                ...editContent,
                                hashtags: [...((editContent as any).hashtags || []), '']
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add Hashtag
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Post Edit Form (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Post' || actualContentType === 'Post') && (
                    <div className="space-y-6">
                      {/* Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Post Title</label>
                        <input
                          type="text"
                          value={(editContent as any).title || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter post title..."
                        />
                      </div>

                      {/* Content */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Post Content</label>
                        <textarea
                          value={(editContent as any).content || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            content: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={8}
                          placeholder="What's happening?"
                        />
                        <div className="text-xs text-text-tertiary mt-1">
                          {((editContent as any).content || '').length} characters
                        </div>
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {((editContent as any).hashtags || []).map((hashtag: string, index: number) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as any).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="flex-1 search-input"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as any).hashtags || []).filter((_: string, i: number) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              handleContentChange({
                                ...editContent,
                                hashtags: [...((editContent as any).hashtags || []), '']
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add Hashtag
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Poll Edit Form (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Poll' || actualContentType === 'Poll') && (
                    <div className="space-y-6">
                      {/* Poll Question */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Poll Question</label>
                        <input
                          type="text"
                          value={(editContent as any).question || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            question: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="What would you like to ask?"
                        />
                      </div>

                      {/* Poll Options */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Poll Options</label>
                        <div className="space-y-3">
                          {((editContent as any).options || []).map((option: any, index: number) => (
                            <div key={index} className="border border-dark-quaternary rounded-lg p-4 space-y-3">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">Option {index + 1}</span>
                                {((editContent as any).options || []).length > 2 && (
                                  <button
                                    onClick={() => {
                                      const newOptions = ((editContent as any).options || []).filter((_: any, i: number) => i !== index)
                                      handleContentChange({
                                        ...editContent,
                                        options: newOptions
                                      })
                                    }}
                                    className="text-red-400 hover:text-red-300 transition-colors"
                                  >
                                    <X className="w-4 h-4" />
                                  </button>
                                )}
                              </div>

                              <div>
                                <input
                                  type="text"
                                  value={option.option || ''}
                                  onChange={(e) => {
                                    const newOptions = [...((editContent as any).options || [])]
                                    newOptions[index] = { ...option, option: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      options: newOptions
                                    })
                                  }}
                                  className="w-full search-input"
                                  placeholder="Poll option text..."
                                />
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Votes</label>
                                <input
                                  type="number"
                                  value={option.votes || 0}
                                  onChange={(e) => {
                                    const newOptions = [...((editContent as any).options || [])]
                                    newOptions[index] = { ...option, votes: parseInt(e.target.value) || 0 }
                                    handleContentChange({
                                      ...editContent,
                                      options: newOptions
                                    })
                                  }}
                                  className="w-full search-input"
                                  min="0"
                                />
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newOption = { option: '', votes: 0 }
                              handleContentChange({
                                ...editContent,
                                options: [...((editContent as any).options || []), newOption]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Poll Option
                          </button>
                        </div>
                      </div>

                      {/* Expiration Date */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Poll Expiration</label>
                        <input
                          type="datetime-local"
                          value={(editContent as any).expirationDate ? new Date((editContent as any).expirationDate).toISOString().slice(0, 16) : ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            expirationDate: e.target.value ? new Date(e.target.value).toISOString() : ''
                          })}
                          className="w-full search-input"
                        />
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {((editContent as any).hashtags || []).map((hashtag: string, index: number) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as any).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="flex-1 search-input"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as any).hashtags || []).filter((_: string, i: number) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              handleContentChange({
                                ...editContent,
                                hashtags: [...((editContent as any).hashtags || []), '']
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add Hashtag
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Video Edit Form (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Video' || actualContentType === 'Video') && (
                    <div className="space-y-6">
                      {/* Video Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Video Title</label>
                        <input
                          type="text"
                          value={(editContent as any).title || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter video title..."
                        />
                      </div>

                      {/* Video Description */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Video Description</label>
                        <textarea
                          value={(editContent as any).desc || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            desc: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={4}
                          placeholder="Describe your video..."
                        />
                      </div>

                      {/* Video Script */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Video Script</label>

                        {/* Intro Section */}
                        <div className="space-y-4 mb-6">
                          <h4 className="text-sm font-medium text-text-primary">Intro</h4>
                          <div className="border border-dark-quaternary rounded-lg p-4 space-y-3">
                            <div>
                              <label className="text-xs font-medium text-text-tertiary block mb-2">Title</label>
                              <input
                                type="text"
                                value={(editContent as any).videoScript?.intro?.title || ''}
                                onChange={(e) => handleContentChange({
                                  ...editContent,
                                  videoScript: {
                                    ...(editContent as any).videoScript,
                                    intro: {
                                      ...(editContent as any).videoScript?.intro,
                                      title: e.target.value
                                    }
                                  }
                                })}
                                className="w-full search-input"
                                placeholder="Intro title..."
                              />
                            </div>
                            <div>
                              <label className="text-xs font-medium text-text-tertiary block mb-2">Start Time</label>
                              <input
                                type="text"
                                value={(editContent as any).videoScript?.intro?.startTime || ''}
                                onChange={(e) => handleContentChange({
                                  ...editContent,
                                  videoScript: {
                                    ...(editContent as any).videoScript,
                                    intro: {
                                      ...(editContent as any).videoScript?.intro,
                                      startTime: e.target.value
                                    }
                                  }
                                })}
                                className="w-full search-input"
                                placeholder="00:00"
                              />
                            </div>
                            <div>
                              <label className="text-xs font-medium text-text-tertiary block mb-2">Description</label>
                              <textarea
                                value={(editContent as any).videoScript?.intro?.desc || ''}
                                onChange={(e) => handleContentChange({
                                  ...editContent,
                                  videoScript: {
                                    ...(editContent as any).videoScript,
                                    intro: {
                                      ...(editContent as any).videoScript?.intro,
                                      desc: e.target.value
                                    }
                                  }
                                })}
                                className="w-full search-input resize-none"
                                rows={3}
                                placeholder="Intro description..."
                              />
                            </div>
                          </div>
                        </div>

                        {/* Main Sections */}
                        <div className="space-y-4 mb-6">
                          <h4 className="text-sm font-medium text-text-primary">Main Content</h4>
                          {((editContent as any).videoScript?.main || []).map((section: any, index: number) => (
                            <div key={index} className="border border-dark-quaternary rounded-lg p-4 space-y-3">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">Section {index + 1}</span>
                                <button
                                  onClick={() => {
                                    const newMain = ((editContent as any).videoScript?.main || []).filter((_: any, i: number) => i !== index)
                                    handleContentChange({
                                      ...editContent,
                                      videoScript: {
                                        ...(editContent as any).videoScript,
                                        main: newMain
                                      }
                                    })
                                  }}
                                  className="text-red-400 hover:text-red-300 transition-colors"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Title</label>
                                <input
                                  type="text"
                                  value={section.title || ''}
                                  onChange={(e) => {
                                    const newMain = [...((editContent as any).videoScript?.main || [])]
                                    newMain[index] = { ...section, title: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      videoScript: {
                                        ...(editContent as any).videoScript,
                                        main: newMain
                                      }
                                    })
                                  }}
                                  className="w-full search-input"
                                  placeholder="Section title..."
                                />
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Start Time</label>
                                <input
                                  type="text"
                                  value={section.startTime || ''}
                                  onChange={(e) => {
                                    const newMain = [...((editContent as any).videoScript?.main || [])]
                                    newMain[index] = { ...section, startTime: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      videoScript: {
                                        ...(editContent as any).videoScript,
                                        main: newMain
                                      }
                                    })
                                  }}
                                  className="w-full search-input"
                                  placeholder="00:30"
                                />
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Description</label>
                                <textarea
                                  value={section.desc || ''}
                                  onChange={(e) => {
                                    const newMain = [...((editContent as any).videoScript?.main || [])]
                                    newMain[index] = { ...section, desc: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      videoScript: {
                                        ...(editContent as any).videoScript,
                                        main: newMain
                                      }
                                    })
                                  }}
                                  className="w-full search-input resize-none"
                                  rows={3}
                                  placeholder="Section description..."
                                />
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSection = { title: '', startTime: '', desc: '' }
                              handleContentChange({
                                ...editContent,
                                videoScript: {
                                  ...(editContent as any).videoScript,
                                  main: [...((editContent as any).videoScript?.main || []), newSection]
                                }
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Main Section
                          </button>
                        </div>

                        {/* Hashtags */}
                        <div>
                          <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                          <div className="space-y-3">
                            {((editContent as any).hashtags || []).map((hashtag: string, index: number) => (
                              <div key={index} className="flex items-center gap-3">
                                <input
                                  type="text"
                                  value={hashtag}
                                  onChange={(e) => {
                                    const newHashtags = [...((editContent as any).hashtags || [])]
                                    newHashtags[index] = e.target.value
                                    handleContentChange({
                                      ...editContent,
                                      hashtags: newHashtags
                                    })
                                  }}
                                  className="flex-1 search-input"
                                  placeholder="#hashtag"
                                />
                                <button
                                  onClick={() => {
                                    const newHashtags = ((editContent as any).hashtags || []).filter((_: string, i: number) => i !== index)
                                    handleContentChange({
                                      ...editContent,
                                      hashtags: newHashtags
                                    })
                                  }}
                                  className="text-red-400 hover:text-red-300 transition-colors"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                            ))}
                            <button
                              onClick={() => {
                                handleContentChange({
                                  ...editContent,
                                  hashtags: [...((editContent as any).hashtags || []), '']
                                })
                              }}
                              className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                            >
                              <Plus className="w-4 h-4" />
                              Add Hashtag
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Thread Edit Form (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Thread' || actualContentType === 'Thread') && (
                    <div className="space-y-6">
                      {/* Thread Posts */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Thread Posts</label>
                        <div className="space-y-4">
                          {((editContent as any).thread || []).map((threadPost: any, index: number) => (
                            <div key={index} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">
                                  {index === 0 ? 'Main Post' : `Post ${index + 1}`}
                                </span>
                                {((editContent as any).thread || []).length > 1 && (
                                  <button
                                    onClick={() => {
                                      const newThread = ((editContent as any).thread || []).filter((_: any, i: number) => i !== index)
                                      handleContentChange({
                                        ...editContent,
                                        thread: newThread
                                      })
                                    }}
                                    className="text-red-400 hover:text-red-300 transition-colors"
                                  >
                                    <X className="w-4 h-4" />
                                  </button>
                                )}
                              </div>

                              <div>
                                <textarea
                                  value={threadPost.content || ''}
                                  onChange={(e) => {
                                    const newThread = [...((editContent as any).thread || [])]
                                    newThread[index] = { ...threadPost, content: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      thread: newThread
                                    })
                                  }}
                                  className="w-full search-input resize-none"
                                  rows={4}
                                  placeholder="What's happening in this post?"
                                />
                                <div className="text-xs text-text-tertiary mt-1">
                                  {(threadPost.content || '').length} characters
                                </div>
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newPost = { content: '' }
                              handleContentChange({
                                ...editContent,
                                thread: [...((editContent as any).thread || []), newPost]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Post to Thread
                          </button>
                        </div>
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {((editContent as any).hashtags || []).map((hashtag: string, index: number) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as any).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="flex-1 search-input"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as any).hashtags || []).filter((_: string, i: number) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              handleContentChange({
                                ...editContent,
                                hashtags: [...((editContent as any).hashtags || []), '']
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add Hashtag
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* PressRelease Edit Form (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'PressRelease' || actualContentType === 'PressRelease') && (
                    <div className="space-y-6">
                      {/* Headline */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Headline</label>
                        <input
                          type="text"
                          value={(editContent as any).headline || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            headline: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Press release headline..."
                        />
                      </div>

                      {/* Subheadline */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Subheadline</label>
                        <input
                          type="text"
                          value={(editContent as any).subheadline || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            subheadline: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Press release subheadline..."
                        />
                      </div>

                      {/* Dateline */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Dateline</label>
                        <input
                          type="text"
                          value={(editContent as any).dateline || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            dateline: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="City, State - Date"
                        />
                      </div>

                      {/* Lead Paragraph */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Lead Paragraph</label>
                        <textarea
                          value={(editContent as any).lead_paragraph || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            lead_paragraph: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={4}
                          placeholder="Opening paragraph that summarizes the key news..."
                        />
                      </div>

                      {/* Body Sections */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Body Sections</label>
                        <div className="space-y-4">
                          {((editContent as any).body_sections || []).map((section: any, index: number) => (
                            <div key={index} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">Section {index + 1}</span>
                                <button
                                  onClick={() => {
                                    const newSections = ((editContent as any).body_sections || []).filter((_: any, i: number) => i !== index)
                                    handleContentChange({
                                      ...editContent,
                                      body_sections: newSections
                                    })
                                  }}
                                  className="text-red-400 hover:text-red-300 transition-colors"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Heading</label>
                                <input
                                  type="text"
                                  value={section.heading || ''}
                                  onChange={(e) => {
                                    const newSections = [...((editContent as any).body_sections || [])]
                                    newSections[index] = { ...section, heading: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      body_sections: newSections
                                    })
                                  }}
                                  className="w-full search-input"
                                  placeholder="Section heading..."
                                />
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Content</label>
                                <textarea
                                  value={section.content || ''}
                                  onChange={(e) => {
                                    const newSections = [...((editContent as any).body_sections || [])]
                                    newSections[index] = { ...section, content: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      body_sections: newSections
                                    })
                                  }}
                                  className="w-full search-input resize-none"
                                  rows={4}
                                  placeholder="Section content..."
                                />
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSection = { heading: '', content: '' }
                              handleContentChange({
                                ...editContent,
                                body_sections: [...((editContent as any).body_sections || []), newSection]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Body Section
                          </button>
                        </div>
                      </div>

                      {/* Boilerplate */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Boilerplate</label>
                        <textarea
                          value={(editContent as any).boilerplate || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            boilerplate: e.target.value
                          })}
                          className="w-full search-input resize-none"
                          rows={4}
                          placeholder="Standard company description..."
                        />
                      </div>

                      {/* Contact Information */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Contact Information</label>
                        <div className="space-y-3">
                          <input
                            type="text"
                            value={(editContent as any).contact_info?.name || ''}
                            onChange={(e) => handleContentChange({
                              ...editContent,
                              contact_info: {
                                ...(editContent as any).contact_info,
                                name: e.target.value
                              }
                            })}
                            className="w-full search-input"
                            placeholder="Contact name"
                          />
                          <input
                            type="email"
                            value={(editContent as any).contact_info?.email || ''}
                            onChange={(e) => handleContentChange({
                              ...editContent,
                              contact_info: {
                                ...(editContent as any).contact_info,
                                email: e.target.value
                              }
                            })}
                            className="w-full search-input"
                            placeholder="Contact email"
                          />
                          <input
                            type="tel"
                            value={(editContent as any).contact_info?.phone || ''}
                            onChange={(e) => handleContentChange({
                              ...editContent,
                              contact_info: {
                                ...(editContent as any).contact_info,
                                phone: e.target.value
                              }
                            })}
                            className="w-full search-input"
                            placeholder="Contact phone"
                          />
                        </div>
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {((editContent as any).hashtags || []).map((hashtag: string, index: number) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as any).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="flex-1 search-input"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as any).hashtags || []).filter((_: string, i: number) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              handleContentChange({
                                ...editContent,
                                hashtags: [...((editContent as any).hashtags || []), '']
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add Hashtag
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Carousel Edit Form (New AI API Content Type or mapped from legacy) */}
                  {(post.contentType === 'Carousel' || actualContentType === 'Carousel') && (
                    <div className="space-y-6">
                      {/* Carousel Title */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Carousel Title</label>
                        <input
                          type="text"
                          value={(editContent as any).title || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            title: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter carousel title..."
                        />
                      </div>

                      {/* Carousel Slides */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Carousel Slides</label>
                        <div className="space-y-4">
                          {((editContent as any).slides || []).map((slide: any, index: number) => (
                            <div key={index} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-text-secondary">Slide {index + 1}</span>
                                {((editContent as any).slides || []).length > 1 && (
                                  <button
                                    onClick={() => {
                                      const newSlides = ((editContent as any).slides || []).filter((_: any, i: number) => i !== index)
                                      handleContentChange({
                                        ...editContent,
                                        slides: newSlides
                                      })
                                    }}
                                    className="text-red-400 hover:text-red-300 transition-colors"
                                  >
                                    <X className="w-4 h-4" />
                                  </button>
                                )}
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Slide Title</label>
                                <input
                                  type="text"
                                  value={slide.title || ''}
                                  onChange={(e) => {
                                    const newSlides = [...((editContent as any).slides || [])]
                                    newSlides[index] = { ...slide, title: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      slides: newSlides
                                    })
                                  }}
                                  className="w-full search-input"
                                  placeholder="Slide title..."
                                />
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Slide Content</label>
                                <textarea
                                  value={slide.content || ''}
                                  onChange={(e) => {
                                    const newSlides = [...((editContent as any).slides || [])]
                                    newSlides[index] = { ...slide, content: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      slides: newSlides
                                    })
                                  }}
                                  className="w-full search-input resize-none"
                                  rows={4}
                                  placeholder="Slide content..."
                                />
                              </div>

                              <div>
                                <label className="text-xs font-medium text-text-tertiary block mb-2">Image Description</label>
                                <input
                                  type="text"
                                  value={slide.image_description || ''}
                                  onChange={(e) => {
                                    const newSlides = [...((editContent as any).slides || [])]
                                    newSlides[index] = { ...slide, image_description: e.target.value }
                                    handleContentChange({
                                      ...editContent,
                                      slides: newSlides
                                    })
                                  }}
                                  className="w-full search-input"
                                  placeholder="Describe the image for this slide..."
                                />
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const newSlide = { title: '', content: '', image_description: '' }
                              handleContentChange({
                                ...editContent,
                                slides: [...((editContent as any).slides || []), newSlide]
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors w-full"
                          >
                            <Plus className="w-4 h-4" />
                            Add Slide
                          </button>
                        </div>
                      </div>

                      {/* Call to Action */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Call to Action</label>
                        <input
                          type="text"
                          value={(editContent as any).call_to_action || ''}
                          onChange={(e) => handleContentChange({
                            ...editContent,
                            call_to_action: e.target.value
                          })}
                          className="w-full search-input"
                          placeholder="Enter call to action text..."
                        />
                      </div>

                      {/* Hashtags */}
                      <div>
                        <label className="text-sm font-medium text-text-secondary block mb-3">Hashtags</label>
                        <div className="space-y-3">
                          {((editContent as any).hashtags || []).map((hashtag: string, index: number) => (
                            <div key={index} className="flex items-center gap-3">
                              <input
                                type="text"
                                value={hashtag}
                                onChange={(e) => {
                                  const newHashtags = [...((editContent as any).hashtags || [])]
                                  newHashtags[index] = e.target.value
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="flex-1 search-input"
                                placeholder="#hashtag"
                              />
                              <button
                                onClick={() => {
                                  const newHashtags = ((editContent as any).hashtags || []).filter((_: string, i: number) => i !== index)
                                  handleContentChange({
                                    ...editContent,
                                    hashtags: newHashtags
                                  })
                                }}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              handleContentChange({
                                ...editContent,
                                hashtags: [...((editContent as any).hashtags || []), '']
                              })
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm text-text-tertiary border border-dark-quaternary border-dashed rounded-lg hover:border-brand-500 hover:text-brand-500 transition-colors"
                          >
                            <Plus className="w-4 h-4" />
                            Add Hashtag
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Fallback for truly unsupported content types */}
                  {!['linkedin', 'email', 'youtube', 'blog', 'medium', 'carousel', 'twitter', 'facebook', 'instagram', 'Article', 'Post', 'Poll', 'Video', 'Thread', 'PressRelease', 'Carousel', 'Email'].includes(post.contentType) && (
                    <div className="bg-dark-tertiary rounded-xl p-6 text-center">
                      <p className="text-sm text-text-tertiary">Edit form for {post.contentType} content coming soon...</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Content Management (40%) */}
        <div className="col-span-2 space-y-6">
          {/* Post Details Card */}
          <div className="card">
            <div className="px-6 py-6">
              <h3 className="text-lg font-semibold text-text-primary mb-6">Post Details</h3>

              <div className="space-y-4">
                {/* Creator Info */}
                <div className="flex items-center gap-3">
                  <Avatar
                    src={post.createdBy.avatar}
                    name={post.createdBy.name}
                    className="w-10 h-10"
                  />
                  <div>
                    <div className="font-medium text-text-primary text-sm">{post.createdBy.name}</div>
                    <div className="text-xs text-text-tertiary capitalize">{post.createdBy.role}</div>
                  </div>
                </div>

                {/* Platform */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-text-secondary">Platform</span>
                  <div className="flex items-center gap-2">
                    <PlatformIcon 
                      platform={post.platform || post.contentType} 
                      className={cn(
                        "w-4 h-4",
                        post.platform === 'linkedin' && "text-blue-500",
                        post.platform === 'email' && "text-purple-500",
                        post.platform === 'youtube' && "text-red-500",
                        post.platform === 'twitter' && "text-blue-400",
                        post.platform === 'facebook' && "text-blue-600",
                        post.platform === 'instagram' && "text-pink-500",
                        (post.platform === 'blog' || post.platform === 'medium') && "text-gray-500"
                      )}
                    />
                    <span className="text-sm text-text-primary capitalize">{post.platform || post.contentType}</span>
                  </div>
                </div>

                {/* Status */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-text-secondary">Status</span>
                  <Status status={post.status as ContentStatus} type="content" />
                </div>

                {/* Created Date */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-text-secondary">Created</span>
                  <span className="text-sm text-text-tertiary">{formatDate('2024-07-20T14:30:00Z')}</span>
                </div>

                {/* Scheduled Date */}
                {post.scheduledDate && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-text-secondary">Scheduled</span>
                    <span className="text-sm text-text-tertiary">{formatDate(post.scheduledDate)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content Review Card */}
          <div ref={commentSectionRef} className="card">
            <div className="flex-1">
              {/* Tab Navigation - Rounded toggle style like edit/preview */}
              <div className="px-6 pt-6 pb-4 border-b border-dark-quaternary">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Content Review</h3>
                <div className="view-toggle">
                  <button
                    onClick={() => setReviewTab('open')}
                    className={cn(
                      'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150 flex items-center gap-1.5',
                      reviewTab === 'open'
                        ? 'view-toggle-btn-active'
                        : 'text-text-tertiary hover:text-text-primary'
                    )}
                  >
                    Open
                    <span className="text-xs font-bold text-brand-500">
                      {post.comments?.filter(c => c.status !== 'resolved').length || 0}
                    </span>
                  </button>
                  <button
                    onClick={() => setReviewTab('resolved')}
                    className={cn(
                      'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150 flex items-center gap-1.5',
                      reviewTab === 'resolved'
                        ? 'view-toggle-btn-active'
                        : 'text-text-tertiary hover:text-text-primary'
                    )}
                  >
                    Resolved
                    <span className="text-xs font-bold text-brand-500">
                      {post.comments?.filter(c => c.status === 'resolved').length || 0}
                    </span>
                  </button>
                </div>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                <div className="space-y-6">
                  {/* Comment List */}
                  <div className="space-y-6 max-h-96 overflow-y-auto">
                    {getReviewComments().map((comment) => {
                      const typeConfig = getCommentTypeConfig(comment.type)
                      const statusConfig = getCommentStatusConfig(comment.status)
                      
                      return (
                        <div key={comment.id} className="flex gap-3">
                          <img 
                            src={comment.user.avatar} 
                            alt={comment.user.name}
                            className="w-8 h-8 rounded-full flex-shrink-0 mt-1"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="bg-dark-tertiary rounded-xl px-4 py-3 border border-dark-quaternary">
                              {/* Comment header with badges */}
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-2">
                                  <span className="font-semibold text-text-primary text-sm">{comment.user.name}</span>
                                  <span className={cn(
                                    'px-2 py-0.5 rounded-full text-xs font-medium',
                                    comment.user.role === 'reviewer' ? 'bg-info-500/10 text-info-500 border border-info-500/20' : 'bg-dark-quaternary text-text-secondary border border-dark-quaternary'
                                  )}>
                                    {comment.user.role}
                                  </span>
                                </div>

                                {/* Comment actions for creator */}
                                {user.role === 'creator' && comment.user.id !== user.id && comment.status === 'open' && (
                                  <div className="flex items-center gap-2">
                                    {comment.type === 'question' && (
                                      <button
                                        onClick={() => handleAcknowledgeComment(comment.id)}
                                        className="text-xs text-info-500 hover:text-info-400 font-medium"
                                      >
                                        Acknowledge
                                      </button>
                                    )}
                                    {(comment.type === 'required_change' || comment.type === 'blocker') && (
                                      <button
                                        onClick={() => setResolvingComment(comment.id)}
                                        className="text-xs text-success-500 hover:text-success-400 font-medium"
                                      >
                                        Mark Resolved
                                      </button>
                                    )}
                                  </div>
                                )}
                              </div>

                              {/* Comment badges */}
                              <div className="flex items-center gap-2 mb-3">
                                {(comment.type === 'required_change' || comment.type === 'blocker') && (
                                <span className={cn(
                                  'inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium border',
                                  typeConfig.badge
                                )}>
                                  <span>{typeConfig.icon}</span>
                                  {typeConfig.label}
                                </span>
                                )}
                                
                                {comment.status !== 'open' && (
                                  <span className={cn(
                                    'inline-flex items-center gap-1.5 px-2 py-0.5 rounded-full text-xs font-medium border',
                                    statusConfig.badge
                                  )}>
                                    {statusConfig.icon}
                                    {statusConfig.label}
                                  </span>
                                )}
                              </div>

                              {/* Comment content */}
                              <p className="text-sm text-text-secondary leading-relaxed mb-3">{comment.content}</p>

                              {/* Creator response if exists */}
                              {comment.creatorResponse && (
                                <div className="bg-info-500/10 border border-info-500/20 rounded-lg p-3 mt-3">
                                  <div className="text-xs text-info-500 font-medium mb-1">Creator Response:</div>
                                  <p className="text-sm text-text-secondary">{comment.creatorResponse}</p>
                                </div>
                              )}

                              {/* Resolution form */}
                              {resolvingComment === comment.id && (
                                <div className="mt-3 pt-3 border-t border-dark-quaternary">
                                  <label className="text-xs font-medium text-text-secondary block mb-2">
                                    Resolution Response (optional):
                                  </label>
                                  <textarea
                                    value={resolutionResponse}
                                    onChange={(e) => setResolutionResponse(e.target.value)}
                                    placeholder="Describe how you addressed this feedback..."
                                    className="w-full search-input resize-none"
                                    rows={2}
                                  />
                                  <div className="flex items-center gap-2 mt-2">
                                    <button
                                      onClick={() => handleResolveComment(comment.id, resolutionResponse)}
                                      className="btn btn-primary text-xs"
                                    >
                                      Mark Resolved
                                    </button>
                                    <button
                                      onClick={() => {
                                        setResolvingComment(null)
                                        setResolutionResponse('')
                                      }}
                                      className="btn btn-secondary text-xs"
                                    >
                                      Cancel
                                    </button>
                                  </div>
                                </div>
                              )}

                              <div className="text-xs text-text-tertiary mt-3">
                                {formatDate(comment.timestamp)}
                                {comment.resolvedAt && (
                                  <span className="ml-2">
                                    • Resolved {formatDate(comment.resolvedAt)}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                    
                    {getReviewComments().length === 0 && (
                      <div className="text-center py-8">
                        <p className="text-sm text-text-tertiary">
                          {reviewTab === 'open'
                            ? 'No open comments.'
                            : 'No resolved comments.'}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Add Comment */}
                  <div className="space-y-4 pt-4 border-t border-dark-quaternary">
                    <textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Add your feedback..."
                      className="w-full search-input resize-none"
                      rows={3}
                    />
                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => setIsRequestChange(!isRequestChange)}
                        className={cn(
                          'flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 border',
                          isRequestChange
                            ? 'bg-error-500/10 text-error-500 border-error-500/20'
                            : 'bg-dark-tertiary text-text-tertiary border-dark-quaternary hover:bg-dark-quaternary'
                        )}
                      >
                        <span className={cn('w-2 h-2 rounded-full', isRequestChange ? 'bg-error-500' : 'bg-text-quaternary')} />
                        Request Change
                      </button>
                      <Button
                        variant="secondary"
                        onClick={handleAddComment}
                        disabled={!newComment.trim()}
                        size="sm"
                      >
                        Add Comment
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* AI Edit Modal */}
      {isAiModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsAiModalOpen(false)}
          />

          {/* Modal */}
          <div className="relative card p-8 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-text-primary">Edit with AI</h3>
                <p className="text-sm text-text-tertiary">Improve your content with AI assistance</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsAiModalOpen(false)}
                className="text-text-quaternary hover:text-text-tertiary"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-6">
              <div>
                <label className="text-sm font-medium text-text-secondary block mb-3">
                  How would you like to improve this content?
                </label>
                <textarea
                  value={aiInstruction}
                  onChange={(e) => setAiInstruction(e.target.value)}
                  placeholder="e.g., Make it more professional, add emojis, change tone to casual, include a call-to-action..."
                  className="w-full search-input resize-none"
                  rows={4}
                />
              </div>

              <div className="flex items-center gap-3">
                <Button
                  variant="secondary"
                  onClick={() => setIsAiModalOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="gradient"
                  onClick={handleAiRegenerate}
                  disabled={!aiInstruction.trim() || isAiLoading}
                  loading={isAiLoading}
                  className="flex-1"
                >
                  {!isAiLoading && <Sparkles className="w-4 h-4" />}
                  Generate
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Submit for Review Modal */}
      {isSubmitReviewModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsSubmitReviewModalOpen(false)}
          />

          {/* Modal */}
          <div className="relative card p-8 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-text-primary">Submit for Review</h3>
                <p className="text-sm text-text-tertiary">Review resolution status before resubmitting</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSubmitReviewModalOpen(false)}
                className="text-text-quaternary hover:text-text-tertiary"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-6">
              <div className="p-4 bg-warning-500/10 border border-warning-500/20 rounded-xl">
                <div className="flex items-center gap-2 mb-3">
                  <AlertCircle className="w-5 h-5 text-warning-500" />
                  <h4 className="font-medium text-warning-500">Pending Required Changes</h4>
                </div>
                <p className="text-sm text-warning-500 mb-4">
                  You have {getRequiredChangesCount()} unresolved required changes. Please address them before resubmitting.
                </p>

                {/* List of unresolved required changes */}
                <div className="space-y-2">
                  {post.comments
                    .filter(c => (c.type === 'required_change' || c.type === 'blocker') && c.status === 'open')
                    .map(comment => {
                      const typeConfig = getCommentTypeConfig(comment.type)
                      return (
                        <div key={comment.id} className="flex items-start gap-3 p-3 bg-dark-secondary rounded-lg border border-warning-500/20">
                          <span className={cn(
                            'inline-flex items-center gap-1 px-2 py-0.5 rounded text-xs font-medium border flex-shrink-0',
                            typeConfig.badge
                          )}>
                            <span>{typeConfig.icon}</span>
                            {typeConfig.label}
                          </span>
                          <p className="text-sm text-text-secondary flex-1">{comment.content}</p>
                        </div>
                      )
                    })}
                </div>
              </div>

              <p className="text-sm text-text-tertiary">
                Go back to resolve the required changes, or explain why they cannot be addressed before resubmitting.
              </p>

              <div className="flex items-center gap-3">
                <Button
                  variant="secondary"
                  onClick={() => setIsSubmitReviewModalOpen(false)}
                  className="flex-1"
                >
                  Go Back
                </Button>
                <Button
                  variant="primary"
                  onClick={handleConfirmSubmitReview}
                  className="flex-1"
                >
                  Submit Anyway
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PostDetail 