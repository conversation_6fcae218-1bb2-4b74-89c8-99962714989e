// Updated ContentType to match new AI API schema (9 content types)
export type ContentType = 'Article' | 'Post' | 'Poll' | 'Email' | 'Video' | 'Thread' | 'PressRelease' | 'Carousel'

// User type definition
export interface User {
  id: string
  name: string
  avatar: string
  role: 'creator' | 'reviewer' | 'admin'
}

// Base content interface with common fields from AI API
export interface BaseContent {
  // Common fields (included in ALL content types)
  date: string // ISO format
  channel: string
  contentType: ContentType
  topic: string
  hashtags: string[]
}

// Post content (Facebook, Instagram, LinkedIn, Twitter)
export interface PostContent extends BaseContent {
  title: string
  content: string
}

// Article content (Medium, Blog/Website)
export interface ArticleContent extends BaseContent {
  title: string
  subheading?: string
  sections: ArticleSection[]
  summary?: string
}

export interface ArticleSection {
  Introduction: string
  Description: string
}

// Poll content (Social platforms with poll support)
export interface PollContent extends BaseContent {
  question: string
  options: string[] // Simple array of option strings
  expirationDate: string // ISO format
}

// Email content (Email/Newsletter content)
export interface EmailContent extends BaseContent {
  subject: string
  body: string
  preheader?: string
  sections: EmailSection[]
  footer?: string
}

export interface EmailSection {
  type: string
  title: string
  content: string
}

// Video content (YouTube, Instagram, Facebook video)
export interface VideoContent extends BaseContent {
  title: string
  desc: string
  videoScript: VideoScript
}

export interface VideoScript {
  intro: VideoScriptSection
  main: VideoScriptSection[]
  outro: VideoScriptSection
}

export interface VideoScriptSection {
  title: string
  startTime: string
  desc: string
}

// Thread content (Twitter/X and other threaded content)
export interface ThreadContent extends BaseContent {
  thread: ThreadPost[]
}

export interface ThreadPost {
  content: string
}

// Press Release content
export interface PressReleaseContent extends BaseContent {
  headline: string
  subheadline?: string
  dateline: string
  lead_paragraph: string
  body_sections: PressReleaseSection[]
  boilerplate: string
  contact_info: ContactInfo
}

export interface PressReleaseSection {
  heading: string
  content: string
}

export interface ContactInfo {
  name?: string
  email?: string
  phone?: string
}

// Carousel content (Instagram, LinkedIn, Facebook carousel posts)
export interface CarouselContent extends BaseContent {
  title: string
  slides: CarouselSlide[]
  call_to_action?: string
}

export interface CarouselSlide {
  title: string
  content: string
  image_description?: string
}

// Union type for all content types
export type ContentData = PostContent | ArticleContent | PollContent | EmailContent | VideoContent | ThreadContent | PressReleaseContent | CarouselContent

export interface ContentPost {
  id: string
  contentType: ContentType
  platform: 'instagram' | 'linkedin' | 'twitter' | 'facebook' | 'email' | 'youtube' | 'blog' | 'medium' | 'website' | 'press-release'

  // Union type for different content structures based on new AI API schema
  content: ContentData

  status: 'draft' | 'pending_review' | 'approved' | 'published' | 'changes_requested'
  scheduledDate?: string
  createdBy: {
    id: string
    name: string
    avatar: string
    role: 'creator' | 'reviewer' | 'admin'
  }
  metrics?: {
    likes: number
    comments: number
    shares: number
    impressions: number
  }
  aiEditHistory: AIEdit[]
  comments: Comment[]
  reviewHistory: ReviewAction[]

  // Additional fields for compatibility
  isQuickPost?: boolean
}

export interface AIEdit {
  id: string
  timestamp: string
  instruction: string
  previousContent: string
  newContent: string
  user: {
    id: string
    name: string
  }
}

export interface Comment {
  id: string
  content: string
  timestamp: string
  user: User
  type: 'comment' | 'required_change' | 'question' | 'blocker'
  status: 'open' | 'resolved' | 'acknowledged'
  targetSection?: string // "content", "hashtags", "images", "email_section_1", etc.
  resolvedBy?: string // user id who resolved it
  resolvedAt?: string // timestamp
  creatorResponse?: string // response from creator when resolving
  resolved: boolean
}

export interface ReviewAction {
  id: string
  type: 'request_changes' | 'approve' | 'submit_for_review'
  timestamp: string
  user: User
  comments: string[] // comment IDs associated with this action
  requiredChangesCount: number
  blockersCount: number
} 