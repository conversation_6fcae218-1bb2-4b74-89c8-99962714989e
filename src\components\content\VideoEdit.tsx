import React from 'react'
import { X, Plus, Clock, Play } from 'lucide-react'
import But<PERSON> from '@/components/ui/Button'

interface VideoScriptSection {
  title: string
  startTime: string
  desc: string
}

interface VideoScript {
  intro: VideoScriptSection
  main: VideoScriptSection[]
  outro: VideoScriptSection
}

interface VideoContent {
  title: string
  desc: string
  videoScript?: VideoScript
  hashtags?: string[]
}

interface VideoEditProps {
  content: VideoContent
  editContent: VideoContent
  setEditContent: (content: VideoContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const VideoEdit: React.FC<VideoEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof VideoContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateScriptSection = (section: 'intro' | 'outro', field: keyof VideoScriptSection, value: string) => {
    const updatedScript = {
      ...editContent.videoScript,
      [section]: {
        ...editContent.videoScript?.[section],
        [field]: value
      }
    } as VideoScript
    updateField('videoScript', updatedScript)
  }

  const updateMainSection = (index: number, field: keyof VideoScriptSection, value: string) => {
    const updatedMain = [...(editContent.videoScript?.main || [])]
    updatedMain[index] = { ...updatedMain[index], [field]: value }
    
    const updatedScript = {
      ...editContent.videoScript,
      main: updatedMain
    } as VideoScript
    updateField('videoScript', updatedScript)
  }

  const addMainSection = () => {
    const newSection: VideoScriptSection = {
      title: '',
      startTime: '00:00',
      desc: ''
    }
    
    const updatedScript = {
      ...editContent.videoScript,
      main: [...(editContent.videoScript?.main || []), newSection]
    } as VideoScript
    updateField('videoScript', updatedScript)
  }

  const removeMainSection = (index: number) => {
    const updatedMain = (editContent.videoScript?.main || []).filter((_, i) => i !== index)
    
    const updatedScript = {
      ...editContent.videoScript,
      main: updatedMain
    } as VideoScript
    updateField('videoScript', updatedScript)
  }

  const initializeVideoScript = () => {
    const defaultScript: VideoScript = {
      intro: { title: '', startTime: '00:00', desc: '' },
      main: [{ title: '', startTime: '00:30', desc: '' }],
      outro: { title: '', startTime: '05:00', desc: '' }
    }
    updateField('videoScript', defaultScript)
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  return (
    <div className="space-y-6">
      {/* Video Title */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">
          <Play className="w-4 h-4 inline mr-2" />
          Video Title
        </label>
        <input
          type="text"
          value={editContent.title || ''}
          onChange={(e) => updateField('title', e.target.value)}
          className="w-full search-input"
          placeholder="Enter video title..."
        />
      </div>

      {/* Video Description */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Video Description</label>
        <textarea
          value={editContent.desc || ''}
          onChange={(e) => updateField('desc', e.target.value)}
          rows={4}
          className="w-full search-input resize-none"
          placeholder="Describe your video content..."
        />
      </div>

      {/* Video Script */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Video Script</label>
          {!editContent.videoScript && (
            <Button
              onClick={initializeVideoScript}
              variant="secondary"
              size="sm"
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Script
            </Button>
          )}
        </div>

        {editContent.videoScript && (
          <div className="space-y-4">
            {/* Intro Section */}
            <div className="p-4 bg-dark-secondary rounded-lg border-l-4 border-green-500">
              <div className="flex items-center gap-2 mb-3">
                <Clock className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium text-green-400">Intro Section</span>
              </div>
              
              <div className="space-y-3">
                <div className="flex gap-3">
                  <input
                    type="text"
                    value={editContent.videoScript.intro?.startTime || ''}
                    onChange={(e) => updateScriptSection('intro', 'startTime', e.target.value)}
                    className="w-20 search-input text-sm"
                    placeholder="00:00"
                  />
                  <input
                    type="text"
                    value={editContent.videoScript.intro?.title || ''}
                    onChange={(e) => updateScriptSection('intro', 'title', e.target.value)}
                    className="flex-1 search-input"
                    placeholder="Intro title..."
                  />
                </div>
                <textarea
                  value={editContent.videoScript.intro?.desc || ''}
                  onChange={(e) => updateScriptSection('intro', 'desc', e.target.value)}
                  rows={2}
                  className="w-full search-input resize-none"
                  placeholder="Intro description..."
                />
              </div>
            </div>

            {/* Main Sections */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-text-secondary">Main Content</span>
                <Button
                  onClick={addMainSection}
                  variant="secondary"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Section
                </Button>
              </div>
              
              <div className="space-y-3">
                {(editContent.videoScript.main || []).map((section, index) => (
                  <div key={index} className="p-4 bg-dark-secondary rounded-lg border-l-4 border-blue-500">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-blue-500" />
                        <span className="text-sm font-medium text-blue-400">
                          Main Section {index + 1}
                        </span>
                      </div>
                      <button
                        onClick={() => removeMainSection(index)}
                        className="text-red-400 hover:text-red-300 p-1"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex gap-3">
                        <input
                          type="text"
                          value={section.startTime}
                          onChange={(e) => updateMainSection(index, 'startTime', e.target.value)}
                          className="w-20 search-input text-sm"
                          placeholder="00:00"
                        />
                        <input
                          type="text"
                          value={section.title}
                          onChange={(e) => updateMainSection(index, 'title', e.target.value)}
                          className="flex-1 search-input"
                          placeholder="Section title..."
                        />
                      </div>
                      <textarea
                        value={section.desc}
                        onChange={(e) => updateMainSection(index, 'desc', e.target.value)}
                        rows={2}
                        className="w-full search-input resize-none"
                        placeholder="Section description..."
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Outro Section */}
            <div className="p-4 bg-dark-secondary rounded-lg border-l-4 border-orange-500">
              <div className="flex items-center gap-2 mb-3">
                <Clock className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium text-orange-400">Outro Section</span>
              </div>
              
              <div className="space-y-3">
                <div className="flex gap-3">
                  <input
                    type="text"
                    value={editContent.videoScript.outro?.startTime || ''}
                    onChange={(e) => updateScriptSection('outro', 'startTime', e.target.value)}
                    className="w-20 search-input text-sm"
                    placeholder="00:00"
                  />
                  <input
                    type="text"
                    value={editContent.videoScript.outro?.title || ''}
                    onChange={(e) => updateScriptSection('outro', 'title', e.target.value)}
                    className="flex-1 search-input"
                    placeholder="Outro title..."
                  />
                </div>
                <textarea
                  value={editContent.videoScript.outro?.desc || ''}
                  onChange={(e) => updateScriptSection('outro', 'desc', e.target.value)}
                  rows={2}
                  className="w-full search-input resize-none"
                  placeholder="Outro description..."
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="secondary"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 search-input"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default VideoEdit
