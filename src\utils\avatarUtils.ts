/**
 * Utility functions for avatar handling
 */

/**
 * Generate a consistent avatar color based on name
 * Uses the same algorithm across all components
 */
export const getAvatarColor = (name: string): string => {
  const colors = [
    'bg-blue-400',
    'bg-emerald-400',
    'bg-violet-400',
    'bg-rose-400',
    'bg-indigo-400',
    'bg-amber-400',
    'bg-red-400',
    'bg-teal-400',
    'bg-cyan-400',
    'bg-orange-400',
    'bg-lime-400',
    'bg-fuchsia-400'
  ]

  // Use character code sum for more consistent distribution
  const charSum = name.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0)
  const index = charSum % colors.length
  return colors[index]
}

/**
 * Generate initials from a name
 */
export const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
