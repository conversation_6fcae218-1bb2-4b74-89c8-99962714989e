/**
 * Utility functions for avatar handling
 */

/**
 * Generate a consistent avatar color based on name
 * Uses the same algorithm across all components
 */
export const getAvatarColor = (name: string): string => {
  const colors = [
    'bg-blue-500',
    'bg-green-500', 
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-yellow-500',
    'bg-red-500',
    'bg-teal-500'
  ]
  
  // Use character code sum for more consistent distribution
  const charSum = name.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0)
  const index = charSum % colors.length
  return colors[index]
}

/**
 * Generate initials from a name
 */
export const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
