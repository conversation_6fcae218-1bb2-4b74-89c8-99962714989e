/**
 * Utility functions for avatar handling
 */

/**
 * Generate a consistent avatar color based on name
 * Uses the same algorithm across all components
 */
export const getAvatarColor = (name: string): string => {
  const colors = [
    'bg-slate-500',
    'bg-gray-500',
    'bg-zinc-500',
    'bg-neutral-500',
    'bg-stone-500',
    'bg-slate-600',
    'bg-gray-600',
    'bg-zinc-600',
    'bg-neutral-600',
    'bg-stone-600',
    'bg-slate-400',
    'bg-gray-400'
  ]

  // Use character code sum for more consistent distribution
  const charSum = name.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0)
  const index = charSum % colors.length
  return colors[index]
}

/**
 * Generate initials from a name
 */
export const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
