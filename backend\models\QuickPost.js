import mongoose from 'mongoose';

// Quick Post Schema
const quickPostSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  brandId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Brand',
    required: [true, 'Brand ID is required']
  },
  
  // Quick Post Information
  postDate: {
    type: Date,
    required: [true, 'Post date is required']
  },
  contentInstructions: {
    type: String,
    required: [true, 'Content instructions are required'],
    trim: true
  },
  destinationUrl: {
    type: String,
    trim: true
  },
  channel: {
    type: String,
    required: [true, 'Channel is required'],
    enum: ['linkedin', 'twitter', 'facebook', 'instagram', 'youtube', 'email', 'blog']
  },
  
  // Optional Selections
  selectedProducts: [{
    type: String // Product IDs or names
  }],
  selectedICPs: [{
    type: String // ICP IDs or names
  }],
  selectedBrandFiles: [{
    type: String // Brand document filenames
  }],
  quickContentFiles: [{
    type: String // Quick post specific document filenames
  }],
  
  // Generated Content from AI - Updated for new API response schema
  generatedContent: [{
    // Common fields (included in ALL content types)
    date: {
      type: String,
      required: true
    },
    channel: {
      type: String,
      required: true
    },
    contentType: {
      type: String,
      required: true
      // Removed enum constraint to allow case-insensitive content types from AI API
    },
    topic: {
      type: String,
      required: true
    },
    hashtags: {
      type: [String],
      required: true
    },

    // Content-specific fields (varies by content type)

    // For Post (Facebook, Instagram, LinkedIn, Twitter):
    title: String,
    content: String,

    // For Article (Medium, Blog/Website):
    subheading: String,
    sections: mongoose.Schema.Types.Mixed, // Will store different structures based on contentType
    summary: String,

    // For Poll (Social platforms with poll support):
    question: String,
    options: [String], // Simple array of option strings
    expirationDate: String,

    // For Email (Email/Newsletter content):
    subject: String,
    body: String,
    preheader: String,
    footer: String,

    // For Video (YouTube, Instagram, Facebook video):
    desc: String,
    videoScript: {
      intro: {
        title: String,
        startTime: String,
        desc: String
      },
      main: [{
        title: String,
        startTime: String,
        desc: String
      }],
      outro: {
        title: String,
        startTime: String,
        desc: String
      }
    },

    // For Thread (Twitter/X and other threaded content):
    thread: [{
      content: {
        type: String,
        required: true
      }
    }],

    // For Press Release (Press release content):
    headline: String,
    subheadline: String,
    dateline: String,
    lead_paragraph: String,
    body_sections: [{
      heading: String,
      content: String
    }],
    boilerplate: String,
    contact_info: {
      name: String,
      email: String,
      phone: String
    },

    // For Carousel (Instagram, LinkedIn, Facebook carousel posts):
    slides: [{
      title: String,
      content: String,
      image_description: String
    }],
    call_to_action: String,

    // Content status
    status: {
      type: String,
      enum: ['generated', 'reviewed', 'approved', 'published'],
      default: 'generated'
    },
    generatedAt: {
      type: Date,
      default: Date.now
    },

    // Content Versioning (for AI regeneration)
    version: {
      type: Number,
      default: 1
    },
    isCurrentVersion: {
      type: Boolean,
      default: true
    },
    versionHistory: [{
      version: {
        type: Number,
        required: true
      },
      contentData: {
        type: mongoose.Schema.Types.Mixed,
        required: true
      },
      generatedAt: {
        type: Date,
        required: true
      },
      regenerationInstruction: {
        type: String,
        trim: true
      },
      generatedBy: {
        type: String,
        enum: ['ai_generation', 'ai_regeneration', 'manual_edit'],
        default: 'ai_generation'
      }
    }],
    // Store current active content data (for easy access)
    currentVersionData: {
      type: mongoose.Schema.Types.Mixed
    }
  }],
  
  // Quick Post Status
  status: {
    type: String,
    enum: ['draft', 'generated', 'reviewed', 'approved', 'published', 'archived'],
    default: 'draft'
  },
  
  // AI Generation Status
  isAIGenerated: {
    type: Boolean,
    default: false
  },
  
  // Quick Post Session ID (for document uploads)
  quickPostId: String,
  
  // Metadata
  progress: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
quickPostSchema.index({ userId: 1, brandId: 1 });
quickPostSchema.index({ postDate: 1 });
quickPostSchema.index({ channel: 1 });
quickPostSchema.index({ status: 1 });
quickPostSchema.index({ createdAt: -1 });

// Virtual for formatted post date
quickPostSchema.virtual('formattedPostDate').get(function() {
  if (!this.postDate) return '';
  return this.postDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

// Virtual for content count
quickPostSchema.virtual('contentCount').get(function() {
  return this.generatedContent ? this.generatedContent.length : 0;
});

// Virtual for published content count
quickPostSchema.virtual('publishedContentCount').get(function() {
  if (!this.generatedContent) return 0;
  return this.generatedContent.filter(content => content.status === 'published').length;
});

// Method to get content by channel
quickPostSchema.methods.getContentByChannel = function(channel) {
  if (!this.generatedContent) return [];
  return this.generatedContent.filter(content => content.channel === channel);
};

// Method to update content status
quickPostSchema.methods.updateContentStatus = function(contentId, newStatus) {
  const content = this.generatedContent.id(contentId);
  if (content) {
    content.status = newStatus;
    return this.save();
  }
  throw new Error('Content not found');
};

// Static method to find posts by date range
quickPostSchema.statics.findByDateRange = function(userId, startDate, endDate) {
  return this.find({
    userId,
    postDate: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ postDate: 1 });
};

// Static method to find posts by channel
quickPostSchema.statics.findByChannel = function(userId, channel) {
  return this.find({
    userId,
    channel
  }).sort({ createdAt: -1 });
};

// Content versioning methods
quickPostSchema.methods.createContentVersion = function(contentIndex, newContentData, regenerationInstruction = '', generatedBy = 'ai_regeneration') {
  if (!this.generatedContent[contentIndex]) {
    throw new Error('Content not found at index ' + contentIndex);
  }

  const content = this.generatedContent[contentIndex];
  const currentVersion = content.version || 1;
  const newVersion = currentVersion + 1;

  // Store current content in version history
  if (!content.versionHistory) {
    content.versionHistory = [];
  }

  content.versionHistory.push({
    version: currentVersion,
    contentData: content.currentVersionData || this.extractContentData(content),
    generatedAt: content.generatedAt || new Date(),
    regenerationInstruction: content.regenerationInstruction || '',
    generatedBy: content.generatedBy || 'ai_generation'
  });

  // Update content with new version
  content.version = newVersion;
  content.currentVersionData = newContentData;
  content.regenerationInstruction = regenerationInstruction;
  content.generatedBy = generatedBy;
  content.generatedAt = new Date();

  // Update the main content fields with new data
  Object.assign(content, newContentData);

  return this.save();
};

quickPostSchema.methods.switchContentVersion = function(contentIndex, targetVersion) {
  if (!this.generatedContent[contentIndex]) {
    throw new Error('Content not found at index ' + contentIndex);
  }

  const content = this.generatedContent[contentIndex];

  if (targetVersion === content.version) {
    return this; // Already on target version
  }

  // Find target version in history
  const targetVersionData = content.versionHistory.find(v => v.version === targetVersion);
  if (!targetVersionData) {
    throw new Error('Version ' + targetVersion + ' not found');
  }

  // Store current version in history if not already there
  const currentVersionInHistory = content.versionHistory.find(v => v.version === content.version);
  if (!currentVersionInHistory) {
    content.versionHistory.push({
      version: content.version,
      contentData: content.currentVersionData || this.extractContentData(content),
      generatedAt: content.generatedAt,
      regenerationInstruction: content.regenerationInstruction || '',
      generatedBy: content.generatedBy || 'ai_generation'
    });
  }

  // Switch to target version
  content.version = targetVersion;
  content.currentVersionData = targetVersionData.contentData;
  content.regenerationInstruction = targetVersionData.regenerationInstruction;
  content.generatedBy = targetVersionData.generatedBy;
  content.generatedAt = targetVersionData.generatedAt;

  // Update main content fields
  Object.assign(content, targetVersionData.contentData);

  return this.save();
};

quickPostSchema.methods.extractContentData = function(content) {
  const { version, isCurrentVersion, versionHistory, currentVersionData, _id, ...contentData } = content.toObject ? content.toObject() : content;
  return contentData;
};

// Pre-save middleware to update progress
quickPostSchema.pre('save', function(next) {
  if (this.isModified('generatedContent') && this.generatedContent.length > 0) {
    this.isAIGenerated = true;
    this.status = 'generated';
    this.progress = 100;
  }
  next();
});

const QuickPost = mongoose.model('QuickPost', quickPostSchema);

export default QuickPost;
