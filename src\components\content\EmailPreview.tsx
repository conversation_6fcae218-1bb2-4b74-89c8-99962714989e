import React from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/MarkdownRenderer'

interface EmailSection {
  type: string
  title: string
  content: string
  buttonText?: string
}

interface EmailContent {
  subject: string
  body: string
  preheader?: string
  sections?: EmailSection[]
  footer?: string
  hashtags?: string[]
}

interface EmailPreviewProps {
  content: EmailContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
  copyButton?: React.ReactNode
}

const EmailPreview: React.FC<EmailPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="card">
      <div className="max-w-2xl mx-auto">
        {/* Email Header */}
        <div className="mb-6 pb-4 border-b border-dark-quaternary">
          <div className="text-xs text-text-tertiary mb-1">Subject:</div>
          <div className="font-semibold text-text-primary mb-2">
            {content.subject}
          </div>
          <div className="text-xs text-text-tertiary mb-1">Preheader:</div>
          <div className="text-sm text-text-secondary">
            {content.preheader}
          </div>
        </div>

        {/* Email Body */}
        {content.body && (
          <div className="mb-6">
            <MarkdownRenderer
              content={content.body || ''}
              className="text-sm leading-relaxed"
            />
          </div>
        )}

        {/* Email Sections */}
        {content.sections && content.sections.length > 0 && (
          <div className="space-y-6">
            {content.sections.map((section, index) => (
              <div key={index} className="space-y-3">
                {section.type === 'hero' && (
                  <div className="text-center">
                    <h2 className="text-xl font-bold text-text-primary mb-2">{section.title}</h2>
                    <MarkdownRenderer
                      content={section.content || ''}
                      className="text-sm"
                    />
                  </div>
                )}

                {section.type === 'content' && (
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary mb-2">{section.title}</h3>
                    <MarkdownRenderer
                      content={section.content || ''}
                      className="text-sm"
                    />
                  </div>
                )}

                {section.type === 'cta' && (
                  <div className="text-center py-4">
                    <h3 className="text-lg font-semibold text-text-primary mb-3">{section.title}</h3>
                    <MarkdownRenderer
                      content={section.content || ''}
                      className="text-sm mb-4"
                    />
                    {section.buttonText && (
                      <button className="btn btn-primary">
                        {section.buttonText}
                      </button>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Email Footer */}
        <div className="mt-8 pt-6 border-t border-dark-quaternary text-center">
          <div className="text-xs text-text-tertiary whitespace-pre-line">
            {content.footer}
          </div>
        </div>
      </div>
    </div>
  )
}

export default EmailPreview
