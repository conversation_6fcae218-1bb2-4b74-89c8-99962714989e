import React from 'react'
import { <PERSON>, Eye } from 'lucide-react'
import <PERSON><PERSON><PERSON>enderer from '@/components/MarkdownRenderer'

interface EmailSection {
  type: string
  title: string
  content: string
  buttonText?: string
}

interface EmailContent {
  subject: string
  body: string
  preheader?: string
  sections?: EmailSection[]
  footer?: string
  hashtags?: string[]
}

interface EmailPreviewProps {
  content: EmailContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const EmailPreview: React.FC<EmailPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="card">
      <div className="max-w-2xl mx-auto">
        {/* Email Header */}
        <div className="mb-6 pb-4 border-b border-dark-quaternary">
          <div className="flex items-center gap-2 mb-2">
            <Mail className="w-5 h-5 text-brand-500" />
            <span className="text-sm font-medium text-text-secondary">Email Preview</span>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-xs text-text-tertiary min-w-[60px]">Subject:</span>
              <span className="text-sm font-semibold text-text-primary">{content.subject}</span>
            </div>
            
            {content.preheader && (
              <div className="flex items-center gap-2">
                <Eye className="w-3 h-3 text-text-tertiary" />
                <span className="text-xs text-text-tertiary">{content.preheader}</span>
              </div>
            )}
          </div>
        </div>

        {/* Email Body */}
        <div className="bg-white text-gray-900 rounded-lg p-6 mb-6">
          {/* Main Body Content */}
          {content.body && (
            <div className="mb-6">
              <MarkdownRenderer content={content.body} className="prose prose-gray max-w-none" />
            </div>
          )}

          {/* Email Sections */}
          {content.sections && content.sections.length > 0 && (
            <div className="space-y-6">
              {content.sections.map((section, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-4">
                  {section.type === 'hero' && (
                    <div className="text-center py-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                      <h2 className="text-2xl font-bold text-gray-900 mb-4">{section.title}</h2>
                      <p className="text-gray-700">{section.content}</p>
                    </div>
                  )}
                  
                  {section.type === 'content' && (
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">{section.title}</h3>
                      <MarkdownRenderer content={section.content} className="prose prose-gray max-w-none" />
                    </div>
                  )}
                  
                  {section.type === 'cta' && (
                    <div className="text-center py-6 bg-gray-50 rounded-lg">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">{section.title}</h3>
                      <p className="text-gray-700 mb-4">{section.content}</p>
                      {section.buttonText && (
                        <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                          {section.buttonText}
                        </button>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Email Footer */}
          {content.footer && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="text-xs text-gray-500 text-center">
                <MarkdownRenderer content={content.footer} className="prose prose-gray prose-sm max-w-none" />
              </div>
            </div>
          )}
        </div>

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {content.hashtags.map((tag, index) => (
              <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                {tag.startsWith('#') ? tag : `#${tag}`}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default EmailPreview
