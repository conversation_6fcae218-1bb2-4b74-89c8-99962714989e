import React from 'react'
import { X, Plus, Image } from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'

interface PostContent {
  title: string
  content: string
  hashtags?: string[]
  images?: string[]
}

interface PostEditProps {
  content: PostContent
  editContent: PostContent
  setEditContent: (content: PostContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const PostEdit: React.FC<PostEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof PostContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  const addImage = () => {
    updateField('images', [...(editContent.images || []), ''])
  }

  const removeImage = (index: number) => {
    const updatedImages = (editContent.images || []).filter((_, i) => i !== index)
    updateField('images', updatedImages)
  }

  return (
    <div className="space-y-6">
      {/* Title */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Post Title</label>
        <input
          type="text"
          value={editContent.title || ''}
          onChange={(e) => updateField('title', e.target.value)}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
          placeholder="Enter post title..."
        />
      </div>

      {/* Content */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Post Content</label>
        <textarea
          value={editContent.content || ''}
          onChange={(e) => updateField('content', e.target.value)}
          rows={6}
          className="w-full search-input resize-none"
          placeholder="What's on your mind?"
        />
      </div>

      {/* Images */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Images</label>
          <Button
            onClick={addImage}
            variant="secondary"
            size="sm"
            className="flex items-center gap-2"
          >
            <Image className="w-4 h-4" />
            Add Image
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.images || []).map((image, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={image}
                onChange={(e) => {
                  const updatedImages = [...(editContent.images || [])]
                  updatedImages[index] = e.target.value
                  updateField('images', updatedImages)
                }}
                className="flex-1 search-input"
                placeholder="Image URL or description"
              />
              <button
                onClick={() => removeImage(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="secondary"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 search-input"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default PostEdit
