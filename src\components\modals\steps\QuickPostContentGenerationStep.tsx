import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Loader2, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { cn } from '@/utils/cn';
import Button from '@/components/ui/Button';
import { useBrandStore } from '@/stores/brandStore';
import { api } from '@/stores/authStore';
import { QuickPostFormData, QuickPostGenerationRequest, GeneratedContent } from '@/types/quickPost';

interface QuickPostContentGenerationStepProps {
  formData: QuickPostFormData;
  onBack: () => void;
  onComplete: (generatedContent: GeneratedContent[], quickPostId?: string) => void;
}

const QuickPostContentGenerationStep: React.FC<QuickPostContentGenerationStepProps> = ({
  formData,
  onBack,
  onComplete
}) => {
  const { brand, activeBrand } = useBrandStore();
  const currentBrand = activeBrand || brand;
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('Preparing request...');
  const hasGeneratedRef = useRef(false);

  useEffect(() => {
    console.log('🔄 QuickPostContentGenerationStep mounted, hasGeneratedRef.current:', hasGeneratedRef.current);
    if (!hasGeneratedRef.current) {
      hasGeneratedRef.current = true;
      generateContent();
    }
  }, []);

  const generateContent = async () => {
    console.log('🚀 generateContent called, hasGeneratedRef.current:', hasGeneratedRef.current);

    if (hasGeneratedRef.current && generatedContent.length > 0) {
      console.log('⚠️ Generation already completed, skipping...');
      return;
    }

    if (!currentBrand) {
      setError('No brand selected');
      return;
    }

    // hasGeneratedRef.current is already set to true in useEffect
    setIsGenerating(true);
    setError(null);
    setProgress(10);
    setCurrentStep('Preparing brand information...');

    try {
      // Build the AI request payload
      const request: QuickPostGenerationRequest = {
        brandId: currentBrand._id,
        brandInformation: {
          name: currentBrand.name,
          about: currentBrand.about,
          story: currentBrand.story,
          location: currentBrand.location,
          targetGeography: currentBrand.targetGeography,
          website: currentBrand.website,
          size: currentBrand.size,
          type: currentBrand.type,
          industry: currentBrand.industry,
          socialMedia: currentBrand.socialMedia?.map(sm => ({
            platform: sm.platform,
            url: sm.url
          })) || [],
          contactInfo: {
            phone: currentBrand.contactInfo?.phone || [],
            email: currentBrand.contactInfo?.email || []
          },
          keyTeamMembers: currentBrand.keyTeamMembers?.map(member => ({
            name: member.name,
            'role/title': member.role
          })) || []
        },
        brandIdentity: {
          brandVoice: currentBrand.brandIdentity?.brandVoice || [],
          coreMessagingPillars: currentBrand.brandIdentity?.coreMessagingPillars,
          primaryKeywords: currentBrand.brandIdentity?.primaryKeywords,
          negativeKeywords: currentBrand.brandIdentity?.negativeKeywords
        },
        quickContentInfo: {
          postDate: formData.postDate,
          contentInstructions: formData.contentInstructions,
          destinationUrl: formData.destinationUrl,
          Channel: formData.channel
        },
        quickContentFiles: formData.quickContentFiles || [],
        selectedBrandFiles: formData.selectedBrandFiles || []
      };

      setProgress(30);
      setCurrentStep('Processing selected products...');

      // Add selected products if any
      if (formData.selectedProducts && formData.selectedProducts.length > 0) {
        request['products/services'] = (currentBrand.products || [])
          .filter((product, index) => {
            const productId = product._id || product.id || `brand-product-${index}`;
            return formData.selectedProducts!.includes(productId);
          })
          .map(product => ({
            name: product.name || '',
            category: product.category || '',
            description: product.description || '',
            features: product.features || '',
            customerBenefits: product.customerBenefits || '',
            uniqueSellingPropositions: product.uniqueSellingPropositions || '',
            targetUseCases: product.targetUseCases || '',
            pricing: product.pricing || ''
          }));
      }

      setProgress(50);
      setCurrentStep('Processing selected ICPs...');

      // Add selected ICPs if any
      if (formData.selectedICPs && formData.selectedICPs.length > 0) {
        request.ICPs = (currentBrand.icps || [])
          .filter((icp, index) => {
            const icpId = icp._id || icp.id || `brand-icp-${index}`;
            return formData.selectedICPs!.includes(icpId);
          })
          .map(icp => ({
            profileName: icp.profileName || '',
            demographics: icp.demographics || '',
            psychographics: icp.psychographics || '',
            goalsAndMotivations: icp.goalsAndMotivations || '',
            painPointsAndChallenges: icp.painPointsAndChallenges || '',
            purchaseBehavior: icp.purchaseBehavior || '',
            decisionMakerRole: Array.isArray(icp.decisionMakerRole) ? icp.decisionMakerRole : [],
            levelOfAwareness: Array.isArray(icp.levelOfAwareness) ? icp.levelOfAwareness : [],
            'preferredContentChannels/Platforms': icp.preferredContentChannels || ''
          }));
      }

      setProgress(70);
      setCurrentStep('Generating content with AI...');

      console.log('🚀 Calling quick post generation API...');
      console.log('📤 Request payload:', JSON.stringify(request, null, 2));

      // Call the AI API
      const response = await api.post('/ai/quick-generate', request);

      setProgress(90);
      setCurrentStep('Processing generated content...');

      console.log('✅ Quick post content generated successfully');
      console.log('📥 Response data:', response.data);

      const content = response.data.data || [];
      const quickPostId = response.data.quickPostId;
      setGeneratedContent(content);

      setProgress(100);
      setCurrentStep('Content generation complete!');

      // Small delay to show completion
      setTimeout(() => {
        setIsGenerating(false);
        onComplete(content, quickPostId);
      }, 1000);

    } catch (error: any) {
      console.error('❌ Quick post generation failed:', error);
      setIsGenerating(false);
      setError(error.response?.data?.error?.message || error.message || 'Failed to generate content');
    }
  };

  const handleRetry = () => {
    setGeneratedContent([]);
    setError(null);
    setProgress(0);
    hasGeneratedRef.current = false; // Reset the ref to allow retry
    generateContent();
  };

  if (error) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              icon={ArrowLeft}
              onClick={onBack}
              className="text-text-tertiary hover:text-text-primary"
            >
              Back
            </Button>
            <div>
              <h1 className="text-xl font-semibold text-text-primary">Content Generation</h1>
              <p className="text-sm text-text-tertiary">Generate your quick post content</p>
            </div>
          </div>
        </div>

        {/* Error State */}
        <div className="card p-8 text-center">
          <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-6" />
          <h3 className="text-lg font-semibold text-text-primary mb-3">Generation Failed</h3>
          <p className="text-text-secondary mb-6 max-w-md mx-auto">{error}</p>
          
          <div className="flex gap-3 justify-center">
            <Button
              variant="secondary"
              onClick={onBack}
            >
              Go Back
            </Button>
            <Button
              variant="gradient"
              onClick={handleRetry}
              icon={RefreshCw}
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            icon={ArrowLeft}
            onClick={onBack}
            className="text-text-tertiary hover:text-text-primary"
            disabled={isGenerating}
          >
            Back
          </Button>
          <div>
            <h1 className="text-xl font-semibold text-text-primary">Content Generation</h1>
            <p className="text-sm text-text-tertiary">Generate your quick post content</p>
          </div>
        </div>
      </div>

      {/* Generation Progress */}
      <div className="card p-8">
        <div className="text-center">
          <div className="flex items-center justify-center mb-6">
            {isGenerating ? (
              <Loader2 className="w-16 h-16 text-brand-500 animate-spin" />
            ) : (
              <CheckCircle className="w-16 h-16 text-green-400" />
            )}
          </div>
          
          <h3 className="text-lg font-semibold text-text-primary mb-3">
            {isGenerating ? 'Generating Content...' : 'Content Generated!'}
          </h3>
          
          <p className="text-text-secondary mb-6">
            {currentStep}
          </p>

          {/* Progress Bar */}
          <div className="w-full bg-dark-quaternary rounded-full h-2 mb-6">
            <div 
              className="bg-gradient-to-r from-brand-500 to-brand-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${progress}%` }}
            />
          </div>

          <div className="text-sm text-text-tertiary">
            {progress}% Complete
          </div>
        </div>
      </div>

      {/* Generated Content Preview (if available) */}
      {generatedContent.length > 0 && (
        <div className="card p-6">
          <h3 className="font-semibold text-text-primary mb-4">Generated Content Preview</h3>
          <div className="space-y-4">
            {generatedContent.slice(0, 2).map((content, index) => {
              // Extract preview text based on content type
              const getPreviewText = (content: GeneratedContent) => {
                switch (content.contentType) {
                  case 'Post':
                    return content.content || content.title || 'Post content generated...';
                  case 'Article':
                    return content.title || content.sections?.[0]?.Introduction || 'Article generated...';
                  case 'Poll':
                    return content.question || 'Poll question generated...';
                  case 'Email':
                    return content.subject || content.body || 'Email content generated...';
                  case 'Video':
                    return content.title || content.desc || 'Video content generated...';
                  case 'Thread':
                    return content.thread?.[0]?.content || 'Thread content generated...';
                  case 'PressRelease':
                    return content.headline || content.lead_paragraph || 'Press release generated...';
                  case 'Carousel':
                    return content.title || content.slides?.[0]?.title || 'Carousel generated...';
                  default:
                    // Legacy fallback
                    return content.content || content.caption || content.tweet || content.postText || 'Content generated...';
                }
              };

              return (
                <div key={index} className="p-4 bg-dark-tertiary rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xs font-medium text-brand-400 uppercase tracking-wide">
                      {content.channel} • {content.contentType}
                    </span>
                  </div>
                  <p className="text-sm text-text-secondary line-clamp-3">
                    {getPreviewText(content)}
                  </p>
                  {content.hashtags && content.hashtags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {content.hashtags.slice(0, 3).map((tag, idx) => (
                        <span key={idx} className="text-xs px-2 py-1 bg-brand-500/10 text-brand-400 rounded">
                          {tag.startsWith('#') ? tag : `#${tag}`}
                        </span>
                      ))}
                      {content.hashtags.length > 3 && (
                        <span className="text-xs text-text-quaternary">+{content.hashtags.length - 3}</span>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
            {generatedContent.length > 2 && (
              <p className="text-xs text-text-tertiary text-center">
                +{generatedContent.length - 2} more content pieces generated
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuickPostContentGenerationStep;
