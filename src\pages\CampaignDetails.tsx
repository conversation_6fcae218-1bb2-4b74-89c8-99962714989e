import React, { useState, useEffect } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { useCampaignStore } from '@/stores/campaignStore'
import { cn } from '@/utils/cn'
import { formatDate } from '@/utils/formatDate'
import Button from '@/components/ui/Button'
import { Status } from '@/components/ui'
import type { CampaignStatus, ContentStatus } from '@/components/ui'
import {
  ArrowLeft,
  Calendar,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Search,
  Eye,
  Globe,
  MoreVertical,
  MoreHorizontal,
  ChevronUp,
  TrendingUp,
  Users,
  Clock,
  BarChart3,
  Mail
} from 'lucide-react'
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

// Channel Icons
const LinkedinIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
)

const TwitterIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
  </svg>
)

const InstagramIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
)

const YouTubeIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
)

const MediumIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"/>
  </svg>
)

// Mock campaign data
const mockCampaignData = {
  id: 'camp_123',
  name: 'Q1 Product Launch Campaign',
  status: 'active' as CampaignStatus,
  brand: 'Tata Consultancy Services',
  startDate: '2024-01-15',
  endDate: '2024-03-15',
  totalWeeks: 8,
  channels: ['LinkedIn', 'Twitter', 'Instagram', 'Blog'],
  stats: {
    totalPosts: 45,
    published: 23,
    scheduled: 12,
    changes_needed: 3,
    pending_review: 7
  }
}

// Mock content data with simplified single status
const mockContentData = [
  {
    id: 1,
    date: "2025-07-05",
    channel: "LinkedIn",
    type: "Article",
    title: "Digital Transformation in 2024: A Complete Guide",
    status: "published" as ContentStatus,
    engagement: { views: 1250, likes: 89, comments: 12 }
  },
  {
    id: 2,
    date: "2025-07-05",
    channel: "Twitter",
    type: "Thread",
    title: "Quick tips for digital transformation",
    status: "published" as ContentStatus,
    engagement: { views: 890, likes: 45, comments: 8 }
  },
  {
    id: 3,
    date: "2025-07-08",
    channel: "Twitter",
    type: "Thread",
    title: "5 AI trends reshaping business operations",
    status: "scheduled" as ContentStatus,
    engagement: { views: 0, likes: 0, comments: 0 }
  },
  {
    id: 4,
    date: "2025-07-10",
    channel: "Blog",
    type: "Case Study",
    title: "Fortune 500 Digital Transformation Success Story",
    status: "changes_needed" as ContentStatus,
    engagement: { views: 0, likes: 0, comments: 0 }
  },
  {
    id: 5,
    date: "2025-07-12",
    channel: "Instagram",
    type: "Carousel",
    title: "Behind the scenes at TCS Innovation Lab",
    status: "pending_review" as ContentStatus,
    engagement: { views: 0, likes: 0, comments: 0 }
  },
  {
    id: 6,
    date: "2025-07-15",
    channel: "LinkedIn",
    type: "Article",
    title: "The Future of AI in Business Operations",
    status: "scheduled" as ContentStatus,
    engagement: { views: 0, likes: 0, comments: 0 }
  },
  {
    id: 7,
    date: "2025-07-18",
    channel: "Instagram",
    type: "Story",
    title: "Tech innovation showcase",
    status: "pending_review" as ContentStatus,
    engagement: { views: 0, likes: 0, comments: 0 }
  },
  {
    id: 8,
    date: "2025-07-20",
    channel: "LinkedIn",
    type: "Post",
    title: "Client success story highlights",
    status: "published" as ContentStatus,
    engagement: { views: 650, likes: 34, comments: 7 }
  },
  {
    id: 9,
    date: "2025-07-22",
    channel: "Twitter",
    type: "Post",
    title: "Weekly tech roundup",
    status: "scheduled" as ContentStatus,
    engagement: { views: 0, likes: 0, comments: 0 }
  }
]

// Helper function to extract title from different content types
const getContentTitle = (content: any): string => {
  if (!content) return 'Untitled Content'

  // Handle new AI API content types
  switch (content.contentType) {
    case 'Article':
      return content.title || content.topic || 'Article'
    case 'Post':
      return content.title || content.topic || 'Post'
    case 'Poll':
      return content.question || content.topic || 'Poll'
    case 'Email':
      return content.subject || content.topic || 'Email'
    case 'Video':
      return content.title || content.topic || 'Video'
    case 'Thread':
      return content.thread?.[0]?.content?.substring(0, 50) + '...' || content.topic || 'Thread'
    case 'PressRelease':
      return content.headline || content.topic || 'Press Release'
    case 'Carousel':
      return content.title || content.topic || 'Carousel'
    default:
      // Legacy fallback for old content types
      return content.content || content.caption || content.tweet || content.postText ||
             content.subject || content.blogTitle || content.title || content.topic || 'Content'
  }
}

const channelConfig = {
  LinkedIn: { Icon: LinkedinIcon, color: 'text-blue-700', bg: 'bg-blue-50' },
  Twitter: { Icon: TwitterIcon, color: 'text-gray-900', bg: 'bg-gray-50' },
  Instagram: { Icon: InstagramIcon, color: 'text-pink-600', bg: 'bg-pink-50' },
  Blog: { Icon: Globe, color: 'text-green-600', bg: 'bg-green-50' },
  YouTube: { Icon: YouTubeIcon, color: 'text-red-600', bg: 'bg-red-50' },
  Email: { Icon: Mail, color: 'text-purple-600', bg: 'bg-purple-50' },
  Medium: { Icon: MediumIcon, color: 'text-gray-800', bg: 'bg-gray-50' }
}

const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'published', label: 'Published' },
  { value: 'scheduled', label: 'Scheduled' },
  { value: 'changes_needed', label: 'Changes Needed' },
  { value: 'pending_review', label: 'Pending Review' }
]

const channelOptions = [
  { value: 'all', label: 'All Channels' },
  { value: 'LinkedIn', label: 'LinkedIn' },
  { value: 'Twitter', label: 'Twitter' },
  { value: 'Instagram', label: 'Instagram' },
  { value: 'Blog', label: 'Blog' }
]

// Generate chart data from real campaign data
const generateChannelDistributionData = (contentData: any[]) => {
  const channelCounts: Record<string, number> = {}
  const channelColors: Record<string, string> = {
    LinkedIn: '#0A66C2',
    Twitter: '#1DA1F2', 
    Instagram: '#E4405F',
    Blog: '#10B981',
    Facebook: '#1877F2',
    YouTube: '#FF0000'
  }
  
  // Count posts per channel
  contentData.forEach(item => {
    channelCounts[item.channel] = (channelCounts[item.channel] || 0) + 1
  })
  
  const totalPosts = contentData.length
  
  return Object.entries(channelCounts).map(([channel, posts]) => ({
    name: channel,
    value: totalPosts > 0 ? Math.round((posts / totalPosts) * 100) : 0,
    color: channelColors[channel] || '#6366f1',
    posts
  }))
}

// Generate weekly data from real campaign content
const generateWeeklyData = (contentData: any[], campaign: any) => {
  if (!campaign || !contentData.length) return []
  
  const startDate = new Date(campaign.startDate)
  const endDate = new Date(campaign.endDate)
  const weeklyData = []
  
  // Calculate number of weeks
  const timeDiff = endDate.getTime() - startDate.getTime()
  const totalWeeks = Math.ceil(timeDiff / (1000 * 60 * 60 * 24 * 7))
  
  for (let week = 0; week < Math.min(totalWeeks, 12); week++) {
    const weekStart = new Date(startDate)
    weekStart.setDate(startDate.getDate() + (week * 7))
    const weekEnd = new Date(weekStart)
    weekEnd.setDate(weekStart.getDate() + 6)
    
    const postsInWeek = contentData.filter(item => {
      const itemDate = new Date(item.date)
      return itemDate >= weekStart && itemDate <= weekEnd
    }).length
    
    weeklyData.push({
      period: `Week ${week + 1}`,
      posts: postsInWeek,
      isHighlighted: false
    })
  }
  
  return weeklyData
}

// Generate daily data from real campaign content (last 7 days of content)
const generateDailyData = (contentData: any[]) => {
  if (!contentData.length) return []
  
  // Get the last 7 days that have content
  const dates = contentData.map(item => new Date(item.date)).sort((a, b) => b.getTime() - a.getTime())
  const uniqueDates = [...new Set(dates.map(d => d.toDateString()))].slice(0, 7)
  
  return uniqueDates.map((dateStr, index) => {
    const date = new Date(dateStr)
    const postsOnDay = contentData.filter(item => 
      new Date(item.date).toDateString() === dateStr
    ).length
    
    return {
      period: date.toLocaleDateString('en-US', { day: 'numeric', weekday: 'short' }),
      posts: postsOnDay,
      isHighlighted: index === 0 // Highlight the most recent day
    }
  }).reverse()
}

// Channel breakdown data for selected period
const getChannelBreakdown = (isDaily: boolean, selectedIndex: number) => {
  if (isDaily) {
    const dailyBreakdowns = [
      [{ channel: 'LinkedIn', posts: 2, color: '#0A66C2' }, { channel: 'Twitter', posts: 1, color: '#1DA1F2' }],
      [{ channel: 'Instagram', posts: 1, color: '#E4405F' }, { channel: 'Blog', posts: 1, color: '#10B981' }],
      [{ channel: 'LinkedIn', posts: 2, color: '#0A66C2' }, { channel: 'Twitter', posts: 1, color: '#1DA1F2' }, { channel: 'Instagram', posts: 1, color: '#E4405F' }],
      [{ channel: 'Blog', posts: 1, color: '#10B981' }],
      [{ channel: 'LinkedIn', posts: 2, color: '#0A66C2' }, { channel: 'Twitter', posts: 2, color: '#1DA1F2' }, { channel: 'Instagram', posts: 1, color: '#E4405F' }],
      [],
      [{ channel: 'Blog', posts: 1, color: '#10B981' }, { channel: 'Instagram', posts: 1, color: '#E4405F' }]
    ]
    return dailyBreakdowns[selectedIndex] || []
  } else {
    const weeklyBreakdowns = [
      [{ channel: 'LinkedIn', posts: 3, color: '#0A66C2' }, { channel: 'Twitter', posts: 2, color: '#1DA1F2' }, { channel: 'Instagram', posts: 1, color: '#E4405F' }],
      [{ channel: 'LinkedIn', posts: 4, color: '#0A66C2' }, { channel: 'Twitter', posts: 2, color: '#1DA1F2' }, { channel: 'Instagram', posts: 1, color: '#E4405F' }, { channel: 'Blog', posts: 1, color: '#10B981' }],
      [{ channel: 'LinkedIn', posts: 2, color: '#0A66C2' }, { channel: 'Twitter', posts: 2, color: '#1DA1F2' }, { channel: 'Blog', posts: 1, color: '#10B981' }],
      [{ channel: 'LinkedIn', posts: 3, color: '#0A66C2' }, { channel: 'Twitter', posts: 2, color: '#1DA1F2' }, { channel: 'Instagram', posts: 2, color: '#E4405F' }],
      [{ channel: 'LinkedIn', posts: 4, color: '#0A66C2' }, { channel: 'Twitter', posts: 3, color: '#1DA1F2' }, { channel: 'Instagram', posts: 1, color: '#E4405F' }, { channel: 'Blog', posts: 1, color: '#10B981' }],
      [{ channel: 'LinkedIn', posts: 2, color: '#0A66C2' }, { channel: 'Twitter', posts: 1, color: '#1DA1F2' }, { channel: 'Blog', posts: 1, color: '#10B981' }],
      [{ channel: 'LinkedIn', posts: 3, color: '#0A66C2' }, { channel: 'Twitter', posts: 2, color: '#1DA1F2' }, { channel: 'Instagram', posts: 1, color: '#E4405F' }],
      [{ channel: 'LinkedIn', posts: 4, color: '#0A66C2' }, { channel: 'Twitter', posts: 2, color: '#1DA1F2' }, { channel: 'Instagram', posts: 1, color: '#E4405F' }, { channel: 'Blog', posts: 1, color: '#10B981' }]
    ]
    return weeklyBreakdowns[selectedIndex] || []
  }
}

type ViewMode = 'week' | 'month' | 'all'
type SortField = 'title' | 'status' | 'channel' | 'date'
type SortDirection = 'asc' | 'desc'

// Custom Circular Chart Component
const CircularChannelChart = ({ contentData }: { contentData: any[] }) => {
  const channelDistributionData = generateChannelDistributionData(contentData)
  const totalPosts = channelDistributionData.reduce((sum, item) => sum + item.posts, 0)
  const radius = 110
  const strokeWidth = 20
  const normalizedRadius = radius - strokeWidth * 0.5
  const circumference = normalizedRadius * 2 * Math.PI

  // Gap between segments in degrees - increased for more spacing
  const gapDegrees = 15
  const gapInCircumference = (gapDegrees / 360) * circumference
  const totalGaps = channelDistributionData.length * gapInCircumference
  const availableCircumference = circumference - totalGaps

  let cumulativeLength = 0

  // Calculate SVG dimensions with padding for glow effects
  const glowPadding = 20
  const svgSize = (radius * 2) + (glowPadding * 2)
  const centerOffset = radius + glowPadding

  return (
    <div className="flex flex-col items-center">
      {/* Circular Chart */}
      <div className="relative">
        <svg
          height={svgSize}
          width={svgSize}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            stroke="#374151"
            fill="transparent"
            strokeWidth={strokeWidth}
            r={normalizedRadius}
            cx={centerOffset}
            cy={centerOffset}
            opacity={0.1}
          />

          {/* Data segments */}
          {channelDistributionData.map((item, index) => {
            const percentage = (item.posts / totalPosts) * 100
            const segmentLength = (percentage / 100) * availableCircumference
            const strokeDasharray = `${segmentLength} ${circumference}`
            const strokeDashoffset = -(cumulativeLength + (index * gapInCircumference))

            const segment = (
              <circle
                key={item.name}
                stroke={item.color}
                fill="transparent"
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                r={normalizedRadius}
                cx={centerOffset}
                cy={centerOffset}
                strokeDasharray={strokeDasharray}
                strokeDashoffset={strokeDashoffset}
                className="transition-all duration-300"
                style={{
                  filter: `drop-shadow(0 0 12px ${item.color}40)`,
                }}
              />
            )

            cumulativeLength += segmentLength
            return segment
          })}
        </svg>

        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="text-4xl font-light text-text-primary">{totalPosts}</div>
          <div className="text-sm text-text-tertiary">posts</div>
        </div>
      </div>

      {/* Legend with unfilled circles in two columns */}
      <div className="mt-16 grid grid-cols-2 gap-x-12 gap-y-4 w-full max-w-lg">
        {channelDistributionData.map((item) => {
          return (
            <div key={item.name} className="flex items-center gap-3">
              {/* Unfilled circle indicator */}
              <div
                className="w-3 h-3 rounded-full border-2 flex-shrink-0"
                style={{ borderColor: item.color }}
              />
              <span className="text-sm text-text-secondary flex-1 min-w-0">{item.name}</span>
              <span className="text-sm font-medium text-text-primary flex-shrink-0">{item.value}%</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Post Volume Chart Component
const PostVolumeChart = ({ contentData, campaign }: { contentData: any[], campaign: any }) => {
  const [isDaily, setIsDaily] = useState(false)
  const [currentPeriod, setCurrentPeriod] = useState(0)

  const weeklyData = generateWeeklyData(contentData, campaign)
  const dailyData = generateDailyData(contentData)
  const currentData = isDaily ? dailyData : weeklyData
  const maxPeriods = currentData.length

  // Update highlighted item based on current period
  const chartData = currentData.map((item, index) => ({
    ...item,
    isHighlighted: index === currentPeriod
  }))

  const selectedBreakdown = getChannelBreakdown(isDaily, currentPeriod)
  const selectedPeriodData = currentData[currentPeriod]

  const navigatePeriod = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && currentPeriod > 0) {
      setCurrentPeriod(currentPeriod - 1)
    } else if (direction === 'next' && currentPeriod < maxPeriods - 1) {
      setCurrentPeriod(currentPeriod + 1)
    }
  }

  return (
    <div className="card">
      {/* Header with proper layout */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-text-primary">Post Volume</h3>

        <div className="flex items-center gap-4">
          {/* Navigation */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => navigatePeriod('prev')}
              disabled={currentPeriod === 0}
              className="p-1.5 text-text-tertiary hover:text-text-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            <span className="text-sm font-medium text-text-primary min-w-[60px] text-center">
              {selectedPeriodData?.period}
            </span>
            <button
              onClick={() => navigatePeriod('next')}
              disabled={currentPeriod === maxPeriods - 1}
              className="p-1.5 text-text-tertiary hover:text-text-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>

          {/* View Toggle */}
          <div className="view-toggle">
            <button
              onClick={() => {setIsDaily(false); setCurrentPeriod(0)}}
              className={cn(
                'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                !isDaily
                  ? 'view-toggle-btn-active'
                  : 'text-text-tertiary hover:text-text-primary'
              )}
            >
              Weekly
            </button>
            <button
              onClick={() => {setIsDaily(true); setCurrentPeriod(0)}}
              className={cn(
                'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                isDaily
                  ? 'view-toggle-btn-active'
                  : 'text-text-tertiary hover:text-text-primary'
              )}
            >
              Daily
            </button>
          </div>

          <button className="text-text-tertiary hover:text-text-secondary">
            <MoreHorizontal className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Compact Breakdown Section */}
      <div className="mb-4">
        <h4 className="text-xs font-medium text-text-tertiary uppercase tracking-wider mb-2">
          {isDaily ? 'Daily' : 'Weekly'} Breakdown
        </h4>
        <div className="flex items-center gap-6">
          {selectedBreakdown.length > 0 ? (
            selectedBreakdown.map((item) => (
              <div key={item.channel} className="flex items-center gap-2">
                <div
                  className="w-2.5 h-2.5 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-xs text-text-secondary">{item.channel}</span>
                <span className="text-xs font-medium text-text-primary">{item.posts}</span>
              </div>
            ))
          ) : (
            <div className="text-xs text-text-tertiary">No posts</div>
          )}

          <div className="flex items-center gap-2 ml-auto">
            <span className="text-xs font-medium text-text-primary">Total</span>
            <span className="text-xs font-medium text-text-primary">
              {selectedPeriodData?.posts || 0}
            </span>
          </div>
        </div>
      </div>

      {/* Full Width Chart */}
      <div className="h-96">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 40 }}
            barCategoryGap="15%"
          >
            <CartesianGrid
              strokeDasharray="2 2"
              stroke="#2a2a2a"
              horizontal={true}
              vertical={false}
            />
            <XAxis
              dataKey="period"
              tick={{ fill: '#6b7280', fontSize: 12, fontWeight: 400 }}
              axisLine={false}
              tickLine={false}
              tickMargin={16}
            />
            <YAxis hide />
            <Tooltip
              contentStyle={{
                backgroundColor: '#1f2937',
                border: '1px solid #374151',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',
                fontSize: '13px',
                fontWeight: 500
              }}
              labelStyle={{
                color: '#ffffff',
                fontWeight: 500
              }}
              itemStyle={{
                color: '#ffffff'
              }}
              cursor={{ fill: 'rgba(96, 165, 250, 0.1)', stroke: 'rgba(96, 165, 250, 0.3)', strokeWidth: 1 }}
              formatter={(value) => [`${value}`, 'Posts']}
              labelFormatter={(label) => `${label}`}
            />
            <Bar
              dataKey="posts"
              radius={[4, 4, 0, 0]}
              maxBarSize={80}
            >
              {chartData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry.isHighlighted ? '#60a5fa' : '#4b5563'}
                  stroke={entry.isHighlighted ? '#3b82f6' : '#374151'}
                  strokeWidth={1}
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
type TabMode = 'overview' | 'content' | 'research'

const CampaignDetails: React.FC = () => {
  const navigate = useNavigate()
  const { campaignId } = useParams()
  const { currentCampaign, fetchCampaign, isLoading, error } = useCampaignStore()

  // Get tab from URL search params
  const [searchParams, setSearchParams] = useSearchParams()
  const tabFromUrl = searchParams.get('tab') as TabMode || 'overview'
  
  // Fetch campaign data when component mounts
  useEffect(() => {
    if (campaignId) {
      fetchCampaign(campaignId)
    }
  }, [campaignId, fetchCampaign])

  // Sync activeTab with URL parameter
  useEffect(() => {
    setActiveTab(tabFromUrl)
  }, [tabFromUrl])
  
  // Transform real campaign data to match component expectations
  const campaign = currentCampaign ? {
    id: currentCampaign._id,
    name: currentCampaign.name,
    status: currentCampaign.status as CampaignStatus,
    brand: currentCampaign.brandId?.name || 'Unknown Brand',
    startDate: currentCampaign.startDate,
    endDate: currentCampaign.endDate,
    totalWeeks: Math.ceil((new Date(currentCampaign.endDate).getTime() - new Date(currentCampaign.startDate).getTime()) / (1000 * 60 * 60 * 24 * 7)),
    channels: currentCampaign.targetChannels || [],
    stats: {
      totalPosts: currentCampaign.generatedContent?.length || 0,
      published: currentCampaign.generatedContent?.filter(c => c.status === 'published').length || 0,
      scheduled: currentCampaign.generatedContent?.filter(c => c.status === 'generated').length || 0,
      changes_needed: 0,
      pending_review: 0
    }
  } : null
  
  // Transform generated content to match component expectations
  const contentData = currentCampaign?.generatedContent?.map((content, index) => ({
    id: index + 1,
    date: content.date,
    channel: content.channel,
    type: content.contentType,
    title: getContentTitle(content),
    status: (content.status === 'generated' ? 'scheduled' : content.status) as ContentStatus,
    engagement: { views: 0, likes: 0, comments: 0 } // Mock engagement data for now
  })) || []
  const [activeTab, setActiveTab] = useState<TabMode>(tabFromUrl)
  const [viewMode, setViewMode] = useState<ViewMode>('month')
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [channelFilter, setChannelFilter] = useState('all')
  const [statusOpen, setStatusOpen] = useState(false)
  const [channelOpen, setChannelOpen] = useState(false)
  const [sortField, setSortField] = useState<SortField>('date')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [dayPopup, setDayPopup] = useState<{ date: Date; content: any[]; position: { x: number; y: number } } | null>(null)

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-brand-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-text-secondary">Loading campaign...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error || !campaign) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error || 'Campaign not found'}</p>
          <Button onClick={() => navigate('/campaigns')} variant="secondary">
            Back to Campaigns
          </Button>
        </div>
      </div>
    )
  }

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Filter content based on search and filters
  const filteredContent = contentData.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.type.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter
    const matchesChannel = channelFilter === 'all' || item.channel === channelFilter
    
    return matchesSearch && matchesStatus && matchesChannel
  })

  // Generate calendar days for monthly view
  const generateMonthlyCalendar = () => {
    const year = selectedDate.getFullYear()
    const month = selectedDate.getMonth()
    
    // First day of the month
    const firstDay = new Date(year, month, 1)
    
    // Start from the first Sunday before or on the first day
    const startDate = new Date(firstDay)
    startDate.setDate(firstDay.getDate() - firstDay.getDay())
    
    // Generate 42 days (6 weeks)
    const days = []
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)
      
      const isCurrentMonth = currentDate.getMonth() === month
      const isToday = currentDate.toDateString() === new Date().toDateString()
      
      // Get content for this day
      const dayContent = filteredContent.filter(item => {
        const itemDate = new Date(item.date)
        return itemDate.toDateString() === currentDate.toDateString()
      })
      
      days.push({
        date: currentDate,
        dayNumber: currentDate.getDate(),
        isCurrentMonth,
        isToday,
        content: dayContent
      })
    }
    
    return days
  }

  // Generate week view (7 days starting from selected date's week)
  const generateWeeklyCalendar = () => {
    const startOfWeek = new Date(selectedDate)
    const day = selectedDate.getDay()
    const diff = selectedDate.getDate() - day // First day is Sunday
    startOfWeek.setDate(diff)

    const days = []
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startOfWeek)
      currentDate.setDate(startOfWeek.getDate() + i)
      
      const isToday = currentDate.toDateString() === new Date().toDateString()
      
      // Get content for this day
      const dayContent = filteredContent.filter(item => {
        const itemDate = new Date(item.date)
        return itemDate.toDateString() === currentDate.toDateString()
      })
      
      days.push({
        date: currentDate,
        dayName: currentDate.toLocaleDateString('en-US', { weekday: 'short' }),
        dayNumber: currentDate.getDate(),
        monthName: currentDate.toLocaleDateString('en-US', { month: 'short' }),
        isToday,
        content: dayContent,
        hasContent: dayContent.length > 0
      })
    }
    
    return days
  }

  const monthlyDays = generateMonthlyCalendar()
  const weeklyDays = generateWeeklyCalendar()

  // Navigation functions
  const goToPreviousMonth = () => {
    const newDate = new Date(selectedDate)
    newDate.setMonth(selectedDate.getMonth() - 1)
    setSelectedDate(newDate)
  }

  const goToNextMonth = () => {
    const newDate = new Date(selectedDate)
    newDate.setMonth(selectedDate.getMonth() + 1)
    setSelectedDate(newDate)
  }

  const goToPreviousWeek = () => {
    const newDate = new Date(selectedDate)
    newDate.setDate(selectedDate.getDate() - 7)
    setSelectedDate(newDate)
  }

  const goToNextWeek = () => {
    const newDate = new Date(selectedDate)
    newDate.setDate(selectedDate.getDate() + 7)
    setSelectedDate(newDate)
  }

  const goToToday = () => {
    setSelectedDate(new Date())
  }

  // Handle navigating to post detail
  const handleContentClick = (contentId: number) => {
    // Navigate to the post detail screen
    navigate(`/campaigns/${campaignId}/posts/${contentId}`)
  }

  // Channel meta function (matching StrategyStep)
  const getChannelMeta = (channel: string) => {
    const key = channel.toLowerCase()
    const defaults = { Icon: Globe, gradient: 'linear-gradient(180deg,#6366f1 0%,#4f46e5 100%)' }
    const gradients: Record<string, string> = {
      linkedin: 'linear-gradient(180deg,#0A66C2 0%,#004182 100%)',
      twitter: 'linear-gradient(180deg,#1DA1F2 0%,#0D8BDE 100%)',
      instagram: 'linear-gradient(180deg,#F58529 0%,#DD2A7B 100%)',
      facebook: 'linear-gradient(180deg,#1877F2 0%,#0e4ead 100%)',
      youtube: 'linear-gradient(180deg,#FF0000 0%,#BB0000 100%)',
      medium: 'linear-gradient(180deg,#444444 0%,#000000 100%)',
      blog: 'linear-gradient(180deg,#34d399 0%,#059669 100%)',
      email: 'linear-gradient(180deg,#8B5CF6 0%,#7C3AED 100%)',
    }
    const base = channelConfig[channel as keyof typeof channelConfig]
    return base
      ? { Icon: base.Icon, gradient: gradients[key] || defaults.gradient }
      : defaults
  }

  // Professional monthly calendar view with connected grid
  const renderMonthlyCalendarView = () => (
    <>
      <div className="space-y-6">
      {/* Calendar Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={goToPreviousMonth}
            className="w-8 h-8 p-0 rounded-full"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <h3 className="text-base font-medium text-text-primary">
            {selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </h3>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={goToNextMonth}
            className="w-8 h-8 p-0 rounded-full"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        <Button
          variant="secondary"
          size="sm"
          onClick={goToToday}
        >
          Today
        </Button>
      </div>

      {/* Modern Calendar Grid with Card Styling */}
      <div className="card">
        {/* Day Headers */}
        <div className="grid grid-cols-7 border-b border-dark-quaternary/50">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="text-center text-xs font-medium text-text-tertiary uppercase tracking-wider py-4 border-r border-dark-quaternary/30 last:border-r-0">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Grid with Square Boxes */}
        <div className="grid grid-cols-7">
          {monthlyDays.map((day, index) => (
            <div
              key={index}
              className={cn(
                'aspect-square border-r border-b border-dark-quaternary/30 last:border-r-0 cursor-pointer',
                'transition-all duration-200 hover:bg-brand-500/10 flex flex-col relative',
                day.isCurrentMonth ? 'bg-transparent' : 'bg-dark-primary/50',
                // Remove bottom border for last row
                index >= 35 && 'border-b-0'
              )}
              onClick={(e) => {
                if (day.content.length > 0) {
                  const rect = e.currentTarget.getBoundingClientRect()
                  setDayPopup({
                    date: day.date,
                    content: day.content,
                    position: { x: rect.right + 10, y: rect.top }
                  })
                }
              }}
            >
              {/* Today highlight overlay - Enhanced visibility */}
              {day.isToday && (
                <div
                  className="absolute inset-0 pointer-events-none"
                  style={{
                    margin: '1px',
                    background: 'linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(99, 102, 241, 0.12) 100%)',
                    border: '2px solid rgba(99, 102, 241, 0.6)',
                    borderRadius: '8px',
                    boxShadow: `
                      0 0 0 1px rgba(99, 102, 241, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.05)
                    `
                  }}
                />
              )}

              {/* Content container */}
              <div className="p-3 flex flex-col h-full relative z-10">
                {/* Day Number - Elegant styling */}
                <div className={cn(
                  'text-sm font-medium mb-3',
                  day.isCurrentMonth ? 'text-text-secondary' : 'text-text-quaternary',
                  day.isToday && 'text-brand-500 font-semibold'
                )}>
                  {day.dayNumber}
                </div>

                {/* Content Entries - Enhanced Visibility Cards */}
                <div className="space-y-1 flex-1 overflow-hidden">
                  {day.content.slice(0, 2).map((item) => {
                    const channelConfig_item = channelConfig[item.channel as keyof typeof channelConfig]
                    const Icon = channelConfig_item?.Icon || Globe

                    return (
                      <div
                        key={item.id}
                        className={cn(
                          'rounded-lg p-2 cursor-pointer text-xs transition-all duration-200',
                          'hover:shadow-dark-sm shadow-sm hover:shadow-md'
                        )}
                        style={{
                          // Top-to-bottom gradient with subtle contrast and fading border effect using box-shadow
                          background: 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)',
                          boxShadow: `
                            0 1px 3px rgba(0, 0, 0, 0.3),
                            0 0 0 1px rgba(255, 255, 255, 0.08),
                            inset 0 1px 0 rgba(255, 255, 255, 0.06),
                            inset 0 -1px 0 rgba(0, 0, 0, 0.2)
                          `
                        }}
                        title={item.title}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleContentClick(item.id)
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'linear-gradient(to bottom, #383838 0%, #2f2f2f 100%)'
                          e.currentTarget.style.boxShadow = `
                            0 2px 6px rgba(0, 0, 0, 0.4),
                            0 0 0 1px rgba(99, 102, 241, 0.3),
                            inset 0 1px 0 rgba(255, 255, 255, 0.1),
                            inset 0 -1px 0 rgba(0, 0, 0, 0.3)
                          `
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)'
                          e.currentTarget.style.boxShadow = `
                            0 1px 3px rgba(0, 0, 0, 0.3),
                            0 0 0 1px rgba(255, 255, 255, 0.08),
                            inset 0 1px 0 rgba(255, 255, 255, 0.06),
                            inset 0 -1px 0 rgba(0, 0, 0, 0.2)
                          `
                        }}
                      >
                        <div className="flex items-center gap-2">
                          <Icon className={cn("w-3 h-3 flex-shrink-0", channelConfig_item?.color || 'text-text-tertiary')} />
                          <span className="truncate font-medium flex-1 text-text-primary text-xs">
                            {item.title}
                          </span>
                          <div className="flex-shrink-0">
                            <div className={cn('w-1.5 h-1.5 rounded-full', item.status === 'published' ? 'bg-success-500' : item.status === 'scheduled' ? 'bg-info-500' : item.status === 'changes_needed' ? 'bg-error-500' : 'bg-warning-500')} />
                          </div>
                        </div>
                      </div>
                    )
                  })}

                  {/* Show "+X more" if there are more entries - Enhanced styling */}
                  {day.content.length > 2 && (
                    <div
                      className={cn(
                        'text-xs text-text-tertiary hover:text-brand-400 px-2 py-1.5 rounded-lg',
                        'cursor-pointer transition-all duration-200'
                      )}
                      style={{
                        background: 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)',
                        boxShadow: `
                          0 1px 2px rgba(0, 0, 0, 0.2),
                          0 0 0 1px rgba(255, 255, 255, 0.15),
                          inset 0 1px 0 rgba(255, 255, 255, 0.05)
                        `,
                        borderRadius: '8px',
                        position: 'relative'
                      }}
                      onClick={(e) => {
                        e.stopPropagation()
                        const rect = e.currentTarget.getBoundingClientRect()
                        setDayPopup({
                          date: day.date,
                          content: day.content,
                          position: { x: rect.right + 10, y: rect.top }
                        })
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(to bottom, #383838 0%, #2f2f2f 100%)'
                        e.currentTarget.style.boxShadow = `
                          0 1px 2px rgba(0, 0, 0, 0.2),
                          0 0 0 1px rgba(99, 102, 241, 0.4),
                          inset 0 1px 0 rgba(255, 255, 255, 0.08)
                        `
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)'
                        e.currentTarget.style.boxShadow = `
                          0 1px 2px rgba(0, 0, 0, 0.2),
                          0 0 0 1px rgba(255, 255, 255, 0.15),
                          inset 0 1px 0 rgba(255, 255, 255, 0.05)
                        `
                      }}
                    >
                      +{day.content.length - 2} more
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Day Content Popup - Redesigned */}
      {dayPopup && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
            onClick={() => setDayPopup(null)}
          />

          {/* Popup Content */}
          <div
            className="fixed z-50 w-80 max-h-96 overflow-hidden"
            style={{
              left: `${Math.min(dayPopup.position.x, window.innerWidth - 320)}px`,
              top: `${Math.min(dayPopup.position.y, window.innerHeight - 400)}px`,
              background: 'linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 50%, #1a1a1a 100%)',
              borderRadius: '16px',
              boxShadow: `
                0 8px 32px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2)
              `
            }}
          >
            {/* Header */}
            <div className="p-4 border-b border-dark-quaternary/30">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-text-primary">
                  {dayPopup.date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric'
                  })}
                </h3>
                <button
                  onClick={() => setDayPopup(null)}
                  className="w-6 h-6 rounded-full bg-dark-quaternary/50 hover:bg-dark-quaternary/70
                           text-text-tertiary hover:text-text-primary transition-all duration-200
                           flex items-center justify-center text-sm font-medium"
                >
                  ×
                </button>
              </div>
              <p className="text-xs text-text-tertiary mt-1">
                {dayPopup.content.length} post{dayPopup.content.length !== 1 ? 's' : ''} scheduled
              </p>
            </div>

            {/* Content List */}
            <div className="p-4 space-y-3 max-h-80 overflow-y-auto">
              {dayPopup.content.map((item) => {
                const channelConfig_item = channelConfig[item.channel as keyof typeof channelConfig]
                const Icon = channelConfig_item?.Icon || Globe

                return (
                  <div
                    key={item.id}
                    className={cn(
                      'rounded-lg p-3 cursor-pointer transition-all duration-200',
                      'hover:shadow-dark-sm'
                    )}
                    style={{
                      // Top-to-bottom gradient with subtle contrast and fading border effect using box-shadow
                      background: 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)',
                      boxShadow: `
                        0 1px 3px rgba(0, 0, 0, 0.3),
                        0 0 0 1px rgba(255, 255, 255, 0.08),
                        inset 0 1px 0 rgba(255, 255, 255, 0.06),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.2)
                      `
                    }}
                    onClick={() => {
                      handleContentClick(item.id)
                      setDayPopup(null)
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(to bottom, #383838 0%, #2f2f2f 100%)'
                      e.currentTarget.style.boxShadow = `
                        0 2px 6px rgba(0, 0, 0, 0.4),
                        0 0 0 1px rgba(99, 102, 241, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.3)
                      `
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(to bottom, #323232 0%, #2a2a2a 100%)'
                      e.currentTarget.style.boxShadow = `
                        0 1px 3px rgba(0, 0, 0, 0.3),
                        0 0 0 1px rgba(255, 255, 255, 0.08),
                        inset 0 1px 0 rgba(255, 255, 255, 0.06),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.2)
                      `
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <Icon className={cn("w-4 h-4 flex-shrink-0 mt-0.5", channelConfig_item?.color || 'text-text-tertiary')} />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-text-primary line-clamp-2">
                          {item.title}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs text-text-tertiary bg-dark-quaternary/30 px-2 py-0.5 rounded">
                            {item.type}
                          </span>
                          <div className="flex items-center gap-1">
                            <div className={cn('w-1.5 h-1.5 rounded-full', item.status === 'published' ? 'bg-success-500' : item.status === 'scheduled' ? 'bg-info-500' : item.status === 'changes_needed' ? 'bg-error-500' : 'bg-warning-500')} />
                            <span className="text-xs text-text-tertiary capitalize">
                              {item.status.replace('_', ' ')}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </>
      )}
      </div>
    </>
  )

  // Weekly view - simplified without sub-headings
  const renderWeeklyCalendarView = () => {
    // Calculate current week number
    const currentWeekIndex = Math.floor((selectedDate.getTime() - new Date(campaign.startDate).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1
    const totalWeeks = campaign.totalWeeks || 8

    return (
      <div className="space-y-6">
        {/* Week Navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={goToPreviousWeek}
              className="w-8 h-8 p-0 rounded-full"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            
            <div className="text-center">
              <h3 className="text-sm font-semibold text-text-primary">
                {weeklyDays[0]?.monthName} {weeklyDays[0]?.dayNumber} - {weeklyDays[6]?.monthName} {weeklyDays[6]?.dayNumber}, {selectedDate.getFullYear()}
              </h3>
              <p className="text-xs text-text-tertiary">
                Week {currentWeekIndex} of {totalWeeks}
              </p>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={goToNextWeek}
              className="w-8 h-8 p-0 rounded-full"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          <Button
            variant="secondary"
            size="sm"
            onClick={goToToday}
          >
            Today
          </Button>
        </div>

        {/* Day-by-Day Content */}
        <div className="space-y-6">
          {weeklyDays.map((day) => {
            const hasContent = day.content.length > 0
            const isToday = day.isToday

            return (
              <div key={day.date.toDateString()}>
                {/* Day Header */}
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className={cn(
                      "text-sm font-semibold",
                      hasContent
                        ? (isToday ? "text-green-400" : "text-text-primary")
                        : "text-text-quaternary"
                    )}>
                      {day.dayName}
                    </h4>
                    <p className={cn(
                      "text-xs",
                      hasContent
                        ? (isToday ? "text-green-500" : "text-text-tertiary")
                        : "text-text-quaternary"
                    )}>
                      {day.dayNumber} {day.monthName}
                    </p>
                  </div>
                </div>

                {/* Content Items */}
                <div className="space-y-3">
                  {day.content.length === 0 ? (
                    <div className="text-xs text-text-quaternary italic py-8 text-center border-2 border-dashed border-dark-quaternary rounded-lg">
                      No content planned for this day
                    </div>
                  ) : (
                    day.content.map((item) => {
                      const channelConfig_item = channelConfig[item.channel as keyof typeof channelConfig]
                      const Icon = channelConfig_item?.Icon || Globe

                      // Channel color functions (matching CalendarDraftStep)
                      const getChannelColor = (channel: string) => {
                        const colors = {
                          LinkedIn: "text-blue-400",
                          Twitter: "text-gray-300",
                          Instagram: "text-pink-400",
                          Facebook: "text-blue-400",
                          YouTube: "text-red-400",
                          Blog: "text-green-400",
                          Email: "text-purple-400",
                          Medium: "text-gray-400"
                        }
                        return colors[channel as keyof typeof colors] || "text-gray-400"
                      }

                      const getChannelBgColor = (channel: string) => {
                        const colors = {
                          LinkedIn: "bg-blue-500/10",
                          Twitter: "bg-gray-500/10",
                          Instagram: "bg-pink-500/10",
                          Facebook: "bg-blue-500/10",
                          YouTube: "bg-red-500/10",
                          Blog: "bg-green-500/10",
                          Email: "bg-purple-500/10",
                          Medium: "bg-gray-500/10"
                        }
                        return colors[channel as keyof typeof colors] || "bg-gray-500/10"
                      }

                      return (
                        <div
                          key={item.id}
                          className="border border-dark-quaternary rounded-lg p-4 hover:shadow-dark-sm transition-shadow cursor-pointer"
                          onClick={() => handleContentClick(item.id)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3 flex-1">
                              {/* Channel Icon */}
                              <div className={cn(
                                "w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0",
                                getChannelBgColor(item.channel)
                              )}>
                                <Icon className={cn("w-5 h-5", getChannelColor(item.channel))} />
                              </div>

                              {/* Content Details */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="text-sm font-medium text-text-primary">{item.channel}</span>
                                  <span className="text-sm text-text-tertiary">•</span>
                                  <span className="text-sm text-text-secondary">{item.type}</span>
                                </div>

                                <h5 className="text-sm font-medium text-text-primary leading-snug mb-3">{item.title}</h5>

                                {/* Status */}
                                <Status status={item.status} type="content" />
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex items-center gap-2 ml-4">
                              <button
                                onClick={(e) => e.stopPropagation()}
                                className="p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-dark-tertiary transition-colors"
                              >
                                <MoreVertical className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      )
                    })
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  // List view with table - now called "All" view
  const renderAllContentView = () => {
    // Pagination logic
    const totalItems = filteredContent.length
    const totalPages = Math.ceil(totalItems / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedContent = filteredContent.slice(startIndex, endIndex)

    return (
      <div className="space-y-4">
        <div className="card overflow-hidden p-2 md:p-3">
          {/* Professional Table Structure */}
          <table className="w-full table-fixed">
        <thead className="border-b border-dark-quaternary">
          <tr>
            <th className="px-6 py-4 text-left" style={{ width: '300px' }}>
              <button
                onClick={() => handleSort('title')}
                className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors"
              >
                Content
                {sortField === 'title' && (
                  sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                )}
              </button>
            </th>
            <th className="px-6 py-4 text-left" style={{ width: '120px' }}>
              <button
                onClick={() => handleSort('channel')}
                className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors"
              >
                Channel
                {sortField === 'channel' && (
                  sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                )}
              </button>
            </th>
            <th className="px-6 py-4 text-left" style={{ width: '130px' }}>
              <button
                onClick={() => handleSort('status')}
                className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors"
              >
                Status
                {sortField === 'status' && (
                  sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                )}
              </button>
            </th>
            <th className="px-6 py-4 text-right" style={{ width: '100px' }}>
              <button
                onClick={() => handleSort('date')}
                className="flex items-center gap-1 text-xs font-medium text-text-secondary uppercase tracking-wider hover:text-text-primary transition-colors ml-auto"
              >
                Date
                {sortField === 'date' && (
                  sortDirection === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                )}
              </button>
            </th>
            <th className="px-6 py-4 text-center" style={{ width: '120px' }}>
              <span className="text-xs font-medium text-text-secondary uppercase tracking-wider">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-dark-quaternary">
          {paginatedContent.map((item) => {
            const meta = getChannelMeta(item.channel)

            return (
              <tr key={item.id} onClick={() => handleContentClick(item.id)} className="hover:bg-dark-tertiary transition-colors cursor-pointer">
                <td className="px-6 py-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ background: meta.gradient }}>
                      <meta.Icon className="w-4 h-4 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="font-medium text-text-primary text-sm truncate">{item.title}</div>
                      <div className="text-xs font-normal text-text-tertiary mt-0.5 truncate">{item.type}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <span className="text-sm font-normal text-text-secondary">{item.channel}</span>
                </td>
                <td className="px-6 py-4">
                  <Status status={item.status} type="content" />
                </td>
                <td className="px-6 py-4 text-right">
                  <span className="text-xs font-normal text-text-tertiary">{formatDate(item.date)}</span>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center justify-center gap-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleContentClick(item.id);
                      }}
                      className="inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-text-tertiary border border-transparent hover:text-success-500 hover:border-success-500 hover:bg-success-500/10 rounded-md transition-colors"
                    >
                      <Eye className="w-3 h-3" />
                      View
                    </button>
                    <button
                      onClick={(e) => e.stopPropagation()}
                      className="p-1.5 rounded-md hover:bg-dark-quaternary transition-colors"
                    >
                      <MoreVertical className="w-4 h-4 text-text-tertiary" />
                    </button>
                  </div>
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>

    {/* Pagination - Always show for testing */}
    <div className="flex items-center justify-between px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg">
      <div className="flex items-center gap-2 text-sm text-text-tertiary">
        <span>Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} results</span>
        <span className="text-xs text-text-quaternary ml-2">
          (Page {currentPage} of {totalPages})
        </span>
      </div>
      <div className="flex items-center gap-2">
        <button
          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="px-3 py-1.5 text-sm font-medium text-text-secondary bg-dark-tertiary border border-dark-quaternary rounded-md hover:bg-dark-quaternary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Previous
        </button>
        <div className="flex items-center gap-1">
          {Array.from({ length: Math.max(1, totalPages) }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => setCurrentPage(page)}
              className={cn(
                "px-3 py-1.5 text-sm font-medium rounded-md transition-colors",
                currentPage === page
                  ? "bg-brand-500 text-white"
                  : "text-text-secondary bg-dark-tertiary border border-dark-quaternary hover:bg-dark-quaternary"
              )}
            >
              {page}
            </button>
          ))}
        </div>
        <button
          onClick={() => setCurrentPage(Math.min(Math.max(1, totalPages), currentPage + 1))}
          disabled={currentPage === totalPages || totalPages <= 1}
          className="px-3 py-1.5 text-sm font-medium text-text-secondary bg-dark-tertiary border border-dark-quaternary rounded-md hover:bg-dark-quaternary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Next
        </button>
      </div>
    </div>
  </div>
)
}

  const renderContent = () => {
    switch (viewMode) {
      case 'week':
        return renderWeeklyCalendarView()
      case 'month':
        return renderMonthlyCalendarView()
      case 'all':
        return renderAllContentView()
      default:
        return renderMonthlyCalendarView()
    }
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-xs text-text-tertiary">
        <button
          onClick={() => navigate('/campaigns')}
          className="hover:text-text-secondary transition-colors duration-200"
        >
          Campaigns
        </button>
        <ChevronRight className="w-3 h-3" />
        <span className="text-text-secondary font-medium">{campaign.name}</span>
      </div>

      {/* Page Header - Clean like draft screen */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/campaigns')}
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <div className="flex items-center gap-3 mb-1">
              <h1 className="text-xl font-bold text-text-primary">{campaign.name}</h1>
              <Status status={campaign.status} type="campaign" />
            </div>
            <div className="flex items-center gap-2 text-sm text-text-tertiary">
              <Calendar className="w-4 h-4" />
              <span>
                {formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}
              </span>
            </div>
          </div>
        </div>

        {/* Three-dot menu */}
        <div className="relative">
          <Button
            variant="ghost"
            size="sm"
            className="w-8 h-8 p-0"
          >
            <MoreVertical className="w-4 h-4" />
          </Button>
          {/* TODO: Add dropdown menu with Edit, Pause Campaign, etc. */}
        </div>
      </div>

      {/* Main Content */}
      <div className="card">
        {/* Tab Navigation */}
        <div className="px-6 py-4 border-b border-dark-quaternary">
          <div className="view-toggle">
            <button
              onClick={() => {
                setActiveTab('overview')
                setSearchParams({ tab: 'overview' })
              }}
              className={cn(
                'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                activeTab === 'overview'
                  ? 'view-toggle-btn-active'
                  : 'text-text-tertiary hover:text-text-primary'
              )}
            >
              Overview
            </button>
            <button
              onClick={() => {
                setActiveTab('content')
                setSearchParams({ tab: 'content' })
              }}
              className={cn(
                'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                activeTab === 'content'
                  ? 'view-toggle-btn-active'
                  : 'text-text-tertiary hover:text-text-primary'
              )}
            >
              Content Library
            </button>
            <button
              onClick={() => {
                setActiveTab('research')
                setSearchParams({ tab: 'research' })
              }}
              className={cn(
                'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                activeTab === 'research'
                  ? 'view-toggle-btn-active'
                  : 'text-text-tertiary hover:text-text-primary'
              )}
            >
              Research Insights
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Key Metrics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="metric-card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-text-tertiary uppercase tracking-wider">Total Posts</p>
                      <p className="text-3xl font-light text-text-primary mt-2 mb-1 leading-none">{campaign.stats.totalPosts}</p>
                    </div>
                    <BarChart3 className="w-6 h-6 text-info-500" />
                  </div>
                  <div className="mt-3 flex items-center gap-2">
                    <TrendingUp className="w-4 h-4 text-success-500" />
                    <span className="text-sm font-medium text-success-500">+12%</span>
                    <span className="text-xs text-text-tertiary">from last campaign</span>
                  </div>
                </div>

                <div className="metric-card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-text-tertiary uppercase tracking-wider">Campaign Duration</p>
                      <p className="text-3xl font-light text-text-primary mt-2 mb-1 leading-none">{campaign.totalWeeks} weeks</p>
                    </div>
                    <Clock className="w-6 h-6 text-secondary-500" />
                  </div>
                  <div className="mt-3">
                    <span className="text-xs text-text-tertiary">{formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}</span>
                  </div>
                </div>

                <div className="metric-card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-text-tertiary uppercase tracking-wider">Active Channels</p>
                      <p className="text-3xl font-light text-text-primary mt-2 mb-1 leading-none">{campaign.channels.length}</p>
                    </div>
                    <Users className="w-6 h-6 text-success-500" />
                  </div>
                  <div className="mt-3">
                    <span className="text-xs text-text-tertiary">{campaign.channels.join(', ')}</span>
                  </div>
                </div>

                <div className="metric-card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-text-tertiary uppercase tracking-wider">Published</p>
                      <p className="text-3xl font-light text-text-primary mt-2 mb-1 leading-none">{campaign.stats.published}</p>
                    </div>
                    <TrendingUp className="w-6 h-6 text-warning-500" />
                  </div>
                  <div className="mt-3">
                    <span className="text-xs text-text-tertiary">{Math.round((campaign.stats.published / campaign.stats.totalPosts) * 100)}% completion rate</span>
                  </div>
                </div>
              </div>

              {/* Charts Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Channel Distribution Chart */}
                <div className="card">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-text-primary">Channel Distribution</h3>
                    <button className="text-text-tertiary hover:text-text-secondary">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center pt-4 pb-6">
                    <CircularChannelChart contentData={contentData} />
                  </div>
                </div>

                {/* Post Volume Chart */}
                <PostVolumeChart contentData={contentData} campaign={campaign} />
              </div>

              {/* Status Breakdown */}
              <div className="card">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-text-primary">Content Status Breakdown</h3>
                  <button className="text-text-tertiary hover:text-text-secondary">
                    <MoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-success-500 mb-2">{campaign.stats.published}</div>
                    <div className="text-sm text-text-secondary">Published</div>
                    <div className="text-xs text-text-tertiary mt-1">+2 this week</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-info-500 mb-2">{campaign.stats.scheduled}</div>
                    <div className="text-sm text-text-secondary">Scheduled</div>
                    <div className="text-xs text-text-tertiary mt-1">+3 this week</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-warning-500 mb-2">{campaign.stats.pending_review}</div>
                    <div className="text-sm text-text-secondary">Pending Review</div>
                    <div className="text-xs text-text-tertiary mt-1">-1 this week</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-error-500 mb-2">{campaign.stats.changes_needed}</div>
                    <div className="text-sm text-text-secondary">Changes Needed</div>
                    <div className="text-xs text-text-tertiary mt-1">No change</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-brand-500 mb-2">{campaign.stats.totalPosts}</div>
                    <div className="text-sm text-text-secondary">Total Posts</div>
                    <div className="text-xs text-text-tertiary mt-1">+4 this week</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'content' && (
            <div className="space-y-6">
              {/* Content Library Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-quaternary w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search content..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="search-input pl-10 pr-4 py-2 w-64"
                    />
                  </div>

                  {/* Status Filter */}
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setStatusOpen(!statusOpen)}
                      className={cn(
                        'brand-selector-btn flex items-center justify-between px-3 py-2 text-sm min-w-[140px]',
                        (statusOpen || statusFilter !== 'all') && 'brand-selector-selected'
                      )}
                    >
                      <span className={cn(
                        statusFilter === 'all' && !statusOpen ? 'text-text-secondary' : 'text-white'
                      )}>
                        {statusOptions.find(opt => opt.value === statusFilter)?.label}
                      </span>
                      <ChevronDown className={cn(
                        'w-4 h-4 transition-transform ml-2',
                        statusOpen || statusFilter !== 'all' ? 'text-white' : 'text-text-tertiary',
                        statusOpen && 'rotate-180'
                      )} />
                    </button>
                    {statusOpen && (
                      <div className="dropdown-panel absolute top-full left-0 right-0 mt-1 z-20">
                        <div className="p-2">
                          {statusOptions.map(option => (
                            <button
                              key={option.value}
                              type="button"
                              onClick={() => {
                                setStatusFilter(option.value)
                                setStatusOpen(false)
                              }}
                              className={cn(
                                "dropdown-item",
                                statusFilter === option.value && "dropdown-item-active"
                              )}
                            >
                              {option.label}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Channel Filter */}
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setChannelOpen(!channelOpen)}
                      className={cn(
                        'brand-selector-btn flex items-center justify-between px-3 py-2 text-sm min-w-[140px]',
                        (channelOpen || channelFilter !== 'all') && 'brand-selector-selected'
                      )}
                    >
                      <span className={cn(
                        channelFilter === 'all' && !channelOpen ? 'text-text-secondary' : 'text-white'
                      )}>
                        {channelOptions.find(opt => opt.value === channelFilter)?.label}
                      </span>
                      <ChevronDown className={cn(
                        'w-4 h-4 transition-transform ml-2',
                        channelOpen || channelFilter !== 'all' ? 'text-white' : 'text-text-tertiary',
                        channelOpen && 'rotate-180'
                      )} />
                    </button>
                    {channelOpen && (
                      <div className="dropdown-panel absolute top-full left-0 right-0 mt-1 z-20">
                        <div className="p-2">
                          {channelOptions.map(option => (
                            <button
                              key={option.value}
                              type="button"
                              onClick={() => {
                                setChannelFilter(option.value)
                                setChannelOpen(false)
                              }}
                              className={cn(
                                "dropdown-item",
                                channelFilter === option.value && "dropdown-item-active"
                              )}
                            >
                              {option.label}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* View Toggle - Week/Month/All */}
                <div className="view-toggle">
                  <button
                    onClick={() => setViewMode('week')}
                    className={cn(
                      'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                      viewMode === 'week'
                        ? 'view-toggle-btn-active'
                        : 'text-text-tertiary hover:text-text-primary'
                    )}
                  >
                    Week
                  </button>
                  <button
                    onClick={() => setViewMode('month')}
                    className={cn(
                      'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                      viewMode === 'month'
                        ? 'view-toggle-btn-active'
                        : 'text-text-tertiary hover:text-text-primary'
                    )}
                  >
                    Month
                  </button>
                  <button
                    onClick={() => setViewMode('all')}
                    className={cn(
                      'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-150',
                      viewMode === 'all'
                        ? 'view-toggle-btn-active'
                        : 'text-text-tertiary hover:text-text-primary'
                    )}
                  >
                    All
                  </button>
                </div>
              </div>

              {/* Content Display */}
              {renderContent()}
            </div>
          )}

          {activeTab === 'research' && (
            <div className="space-y-6">
              {/* Coming Soon Banner */}
              <div className="bg-dark-secondary border border-dark-quaternary rounded-lg p-6 bg-gradient-to-r from-brand-500/20 to-secondary-500/20 text-center">
                <div className="w-16 h-16 bg-brand-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="w-8 h-8 text-brand-500" />
                </div>
                <h3 className="text-xl font-semibold text-text-primary mb-2">Research Insights Coming Soon</h3>
                <p className="text-text-secondary mb-4">
                  Get deep insights into your campaign performance, audience engagement, and market trends.
                </p>
                <div className="inline-flex items-center px-4 py-2 bg-brand-500/20 text-brand-400 rounded-full text-sm font-medium">
                  <Clock className="w-4 h-4 mr-2" />
                  Available after social media integrations
                </div>
              </div>

              {/* Preview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-dark-secondary border border-dark-quaternary rounded-lg p-6 opacity-60">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-text-primary">Audience Analytics</h4>
                    <Users className="w-5 h-5 text-text-quaternary" />
                  </div>
                  <p className="text-sm text-text-tertiary mb-4">
                    Demographic insights, engagement patterns, and audience growth metrics.
                  </p>
                  <div className="h-32 bg-dark-tertiary rounded-lg flex items-center justify-center">
                    <span className="text-text-quaternary text-sm">Chart Preview</span>
                  </div>
                </div>

                <div className="bg-dark-secondary border border-dark-quaternary rounded-lg p-6 opacity-60">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-text-primary">Performance Metrics</h4>
                    <TrendingUp className="w-5 h-5 text-text-quaternary" />
                  </div>
                  <p className="text-sm text-text-tertiary mb-4">
                    Reach, impressions, engagement rates, and conversion tracking.
                  </p>
                  <div className="h-32 bg-dark-tertiary rounded-lg flex items-center justify-center">
                    <span className="text-text-quaternary text-sm">Metrics Preview</span>
                  </div>
                </div>

                <div className="bg-dark-secondary border border-dark-quaternary rounded-lg p-6 opacity-60">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-text-primary">Content Analysis</h4>
                    <BarChart3 className="w-5 h-5 text-text-quaternary" />
                  </div>
                  <p className="text-sm text-text-tertiary mb-4">
                    Top performing content, optimal posting times, and content recommendations.
                  </p>
                  <div className="h-32 bg-dark-tertiary rounded-lg flex items-center justify-center">
                    <span className="text-text-quaternary text-sm">Analysis Preview</span>
                  </div>
                </div>
              </div>

              {/* Feature List */}
              <div className="bg-dark-secondary border border-dark-quaternary rounded-lg p-6">
                <h4 className="font-semibold text-text-primary mb-4">Upcoming Features</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-500 rounded-full mt-2"></div>
                    <div>
                      <h5 className="font-medium text-text-primary">Real-time Analytics</h5>
                      <p className="text-sm text-text-tertiary">Live performance tracking and alerts</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-500 rounded-full mt-2"></div>
                    <div>
                      <h5 className="font-medium text-text-primary">Competitor Analysis</h5>
                      <p className="text-sm text-text-tertiary">Benchmark against industry standards</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-500 rounded-full mt-2"></div>
                    <div>
                      <h5 className="font-medium text-text-primary">AI Recommendations</h5>
                      <p className="text-sm text-text-tertiary">Smart suggestions for content optimization</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-500 rounded-full mt-2"></div>
                    <div>
                      <h5 className="font-medium text-text-primary">Custom Reports</h5>
                      <p className="text-sm text-text-tertiary">Exportable insights and dashboards</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CampaignDetails 