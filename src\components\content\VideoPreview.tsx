import React from 'react'
import { Play, Clock, FileText } from 'lucide-react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/MarkdownRenderer'

interface VideoScriptSection {
  title: string
  startTime: string
  desc: string
}

interface VideoScript {
  intro: VideoScriptSection
  main: VideoScriptSection[]
  outro: VideoScriptSection
}

interface VideoContent {
  title: string
  desc: string
  videoScript?: VideoScript
  hashtags?: string[]
}

interface VideoPreviewProps {
  content: VideoContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const VideoPreview: React.FC<VideoPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="card">
      <div className="space-y-6">
        {/* Video Thumbnail Placeholder */}
        <div className="relative bg-black rounded-lg aspect-video flex items-center justify-center">
          <div className="text-center">
            <Play className="w-16 h-16 text-white mb-4 mx-auto opacity-80" />
            <p className="text-white text-sm opacity-60">Video Preview</p>
          </div>
          
          {/* Video Title Overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
            <h2 className="text-white font-semibold text-lg">{content.title}</h2>
          </div>
        </div>

        {/* Video Description */}
        <div>
          <h3 className="text-lg font-semibold text-text-primary mb-3">Description</h3>
          <div className="prose prose-invert max-w-none">
            <MarkdownRenderer content={content.desc} />
          </div>
        </div>

        {/* Video Script */}
        {content.videoScript && (
          <div>
            <div className="flex items-center gap-2 mb-4">
              <FileText className="w-5 h-5 text-brand-500" />
              <h3 className="text-lg font-semibold text-text-primary">Video Script</h3>
            </div>
            
            <div className="space-y-4">
              {/* Intro */}
              <div className="p-4 bg-dark-secondary rounded-lg border-l-4 border-green-500">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-green-500" />
                  <span className="text-sm font-medium text-green-400">
                    {content.videoScript.intro.startTime} - Intro
                  </span>
                </div>
                <h4 className="font-semibold text-text-primary mb-2">
                  {content.videoScript.intro.title}
                </h4>
                <p className="text-text-secondary text-sm">
                  {content.videoScript.intro.desc}
                </p>
              </div>

              {/* Main Sections */}
              {content.videoScript.main.map((section, index) => (
                <div key={index} className="p-4 bg-dark-secondary rounded-lg border-l-4 border-blue-500">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span className="text-sm font-medium text-blue-400">
                      {section.startTime} - Main Content {index + 1}
                    </span>
                  </div>
                  <h4 className="font-semibold text-text-primary mb-2">
                    {section.title}
                  </h4>
                  <p className="text-text-secondary text-sm">
                    {section.desc}
                  </p>
                </div>
              ))}

              {/* Outro */}
              <div className="p-4 bg-dark-secondary rounded-lg border-l-4 border-orange-500">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-orange-500" />
                  <span className="text-sm font-medium text-orange-400">
                    {content.videoScript.outro.startTime} - Outro
                  </span>
                </div>
                <h4 className="font-semibold text-text-primary mb-2">
                  {content.videoScript.outro.title}
                </h4>
                <p className="text-text-secondary text-sm">
                  {content.videoScript.outro.desc}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-text-secondary mb-3">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {content.hashtags.map((tag, index) => (
                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                  {tag.startsWith('#') ? tag : `#${tag}`}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default VideoPreview
