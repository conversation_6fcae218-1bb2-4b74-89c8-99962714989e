import React from 'react'
import { Play, Clock } from 'lucide-react'
import { cn } from '@/utils/cn'

interface VideoScriptSection {
  title: string
  startTime: string
  desc: string
}

interface VideoScript {
  intro: VideoScriptSection
  main: VideoScriptSection[]
  outro: VideoScriptSection
}

interface VideoContent {
  title: string
  desc: string
  videoScript?: VideoScript
  hashtags?: string[]
}

interface VideoPreviewProps {
  content: VideoContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
}

const VideoPreview: React.FC<VideoPreviewProps> = ({
  content,
  createdBy,
  scheduledDate
}) => {
  return (
    <div className="card">
      <div className="space-y-6">
        {/* Video Thumbnail */}
        <div className="relative">
          <div className="w-full aspect-video bg-black rounded-lg flex items-center justify-center">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center">
              <div className="w-0 h-0 border-l-[8px] border-l-white border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1"></div>
            </div>
          </div>
        </div>

        {/* Video Details */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-text-primary">{content.title}</h3>
          <p className="text-sm text-text-secondary leading-relaxed">
            {content.desc}
          </p>

          {/* Hashtags */}
          {content.hashtags && content.hashtags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {content.hashtags.map((tag, index) => (
                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Video Script Sections */}
        {content.videoScript && (
          <div className="space-y-4">
            <h4 className="text-md font-semibold text-text-primary">Video Script</h4>
            <div className="space-y-4">
              {/* Intro */}
              {content.videoScript.intro && (
                <div className="bg-dark-tertiary rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-2">
                    <Clock className="w-4 h-4 text-success-500" />
                    <span className="text-xs font-medium text-text-tertiary bg-dark-quaternary px-2 py-1 rounded">
                      {content.videoScript.intro.startTime}
                    </span>
                    <span className="text-xs font-medium px-2 py-1 rounded bg-success-500/10 text-success-500">
                      intro
                    </span>
                  </div>
                  <h5 className="font-medium text-text-primary mb-2">{content.videoScript.intro.title}</h5>
                  <p className="text-sm text-text-secondary">{content.videoScript.intro.desc}</p>
                </div>
              )}

              {/* Main Sections */}
              {content.videoScript.main && content.videoScript.main.map((section, index) => (
                <div key={index} className="bg-dark-tertiary rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-2">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span className="text-xs font-medium text-text-tertiary bg-dark-quaternary px-2 py-1 rounded">
                      {section.startTime}
                    </span>
                    <span className="text-xs font-medium px-2 py-1 rounded bg-blue-500/10 text-blue-500">
                      main {index + 1}
                    </span>
                  </div>
                  <h5 className="font-medium text-text-primary mb-2">{section.title}</h5>
                  <p className="text-sm text-text-secondary">{section.desc}</p>
                </div>
              ))}

              {/* Outro */}
              {content.videoScript.outro && (
                <div className="bg-dark-tertiary rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-2">
                    <Clock className="w-4 h-4 text-orange-500" />
                    <span className="text-xs font-medium text-text-tertiary bg-dark-quaternary px-2 py-1 rounded">
                      {content.videoScript.outro.startTime}
                    </span>
                    <span className="text-xs font-medium px-2 py-1 rounded bg-orange-500/10 text-orange-500">
                      outro
                    </span>
                  </div>
                  <h5 className="font-medium text-text-primary mb-2">{content.videoScript.outro.title}</h5>
                  <p className="text-sm text-text-secondary">{content.videoScript.outro.desc}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-text-secondary mb-3">Tags</h4>
            <div className="flex flex-wrap gap-2">
              {content.hashtags.map((tag, index) => (
                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                  {tag.startsWith('#') ? tag : `#${tag}`}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default VideoPreview
