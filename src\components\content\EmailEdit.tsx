import React from 'react'
import { X, Plus, Mail } from 'lucide-react'
import Button from '@/components/ui/Button'

interface EmailSection {
  type: string
  title: string
  content: string
  buttonText?: string
}

interface EmailContent {
  subject: string
  body: string
  preheader?: string
  sections?: EmailSection[]
  footer?: string
  hashtags?: string[]
}

interface EmailEditProps {
  content: EmailContent
  editContent: EmailContent
  setEditContent: (content: EmailContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const EmailEdit: React.FC<EmailEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof EmailContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateSection = (index: number, field: keyof EmailSection, value: string) => {
    const updatedSections = [...(editContent.sections || [])]
    updatedSections[index] = { ...updatedSections[index], [field]: value }
    updateField('sections', updatedSections)
  }

  const addSection = (type: string = 'content') => {
    const newSection: EmailSection = {
      type,
      title: '',
      content: '',
      ...(type === 'cta' && { buttonText: '' })
    }
    updateField('sections', [...(editContent.sections || []), newSection])
  }

  const removeSection = (index: number) => {
    const updatedSections = (editContent.sections || []).filter((_, i) => i !== index)
    updateField('sections', updatedSections)
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  return (
    <div className="space-y-6">
      {/* Subject */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">
          <Mail className="w-4 h-4 inline mr-2" />
          Email Subject
        </label>
        <input
          type="text"
          value={editContent.subject || ''}
          onChange={(e) => updateField('subject', e.target.value)}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
          placeholder="Enter email subject..."
        />
      </div>

      {/* Preheader */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Preheader (Optional)</label>
        <input
          type="text"
          value={editContent.preheader || ''}
          onChange={(e) => updateField('preheader', e.target.value)}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
          placeholder="Preview text that appears in inbox..."
        />
      </div>

      {/* Body */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Email Body</label>
        <textarea
          value={editContent.body || ''}
          onChange={(e) => updateField('body', e.target.value)}
          rows={6}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500 resize-none"
          placeholder="Enter main email content..."
        />
      </div>

      {/* Email Sections */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Email Sections</label>
          <div className="flex gap-2">
            <Button
              onClick={() => addSection('hero')}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              + Hero
            </Button>
            <Button
              onClick={() => addSection('content')}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              + Content
            </Button>
            <Button
              onClick={() => addSection('cta')}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              + CTA
            </Button>
          </div>
        </div>
        
        <div className="space-y-4">
          {(editContent.sections || []).map((section, index) => (
            <div key={index} className="p-4 bg-dark-secondary rounded-lg border border-dark-quaternary">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-text-secondary capitalize">
                    {section.type} Section
                  </span>
                  <select
                    value={section.type}
                    onChange={(e) => updateSection(index, 'type', e.target.value)}
                    className="px-2 py-1 bg-dark-primary border border-dark-quaternary rounded text-xs text-text-primary focus:outline-none focus:border-brand-500"
                  >
                    <option value="hero">Hero</option>
                    <option value="content">Content</option>
                    <option value="cta">Call to Action</option>
                  </select>
                </div>
                <button
                  onClick={() => removeSection(index)}
                  className="text-red-400 hover:text-red-300 p-1"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-3">
                <input
                  type="text"
                  value={section.title}
                  onChange={(e) => updateSection(index, 'title', e.target.value)}
                  className="w-full px-3 py-2 bg-dark-primary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                  placeholder="Section title..."
                />
                <textarea
                  value={section.content}
                  onChange={(e) => updateSection(index, 'content', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 bg-dark-primary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500 resize-none"
                  placeholder="Section content..."
                />
                {section.type === 'cta' && (
                  <input
                    type="text"
                    value={section.buttonText || ''}
                    onChange={(e) => updateSection(index, 'buttonText', e.target.value)}
                    className="w-full px-3 py-2 bg-dark-primary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                    placeholder="Button text..."
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Email Footer (Optional)</label>
        <textarea
          value={editContent.footer || ''}
          onChange={(e) => updateField('footer', e.target.value)}
          rows={3}
          className="w-full px-4 py-3 bg-dark-secondary border border-dark-quaternary rounded-lg text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500 resize-none"
          placeholder="Unsubscribe links, company info, etc..."
        />
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 px-3 py-2 bg-dark-secondary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default EmailEdit
