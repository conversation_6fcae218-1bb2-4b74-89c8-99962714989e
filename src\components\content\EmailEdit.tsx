import React from 'react'
import { X, Plus } from 'lucide-react'

interface EmailSection {
  type: string
  title: string
  content: string
  buttonText?: string
}

interface EmailContent {
  subject: string
  body: string
  preheader?: string
  sections?: EmailSection[]
  footer?: string
  hashtags?: string[]
}

interface EmailEditProps {
  content: EmailContent
  editContent: EmailContent
  setEditContent: (content: EmailContent) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: (hasChanges: boolean) => void
}

const EmailEdit: React.FC<EmailEditProps> = ({
  content,
  editContent,
  setEditContent,
  hasUnsavedChanges,
  setHasUnsavedChanges
}) => {
  const updateField = (field: keyof EmailContent, value: any) => {
    setEditContent({ ...editContent, [field]: value })
    setHasUnsavedChanges(true)
  }

  const updateSection = (index: number, field: keyof EmailSection, value: string) => {
    const updatedSections = [...(editContent.sections || [])]
    updatedSections[index] = { ...updatedSections[index], [field]: value }
    updateField('sections', updatedSections)
  }

  const addSection = (type: string = 'content') => {
    const newSection: EmailSection = {
      type,
      title: '',
      content: '',
      ...(type === 'cta' && { buttonText: '' })
    }
    updateField('sections', [...(editContent.sections || []), newSection])
  }

  const removeSection = (index: number) => {
    const updatedSections = (editContent.sections || []).filter((_, i) => i !== index)
    updateField('sections', updatedSections)
  }

  const updateHashtag = (index: number, value: string) => {
    const updatedHashtags = [...(editContent.hashtags || [])]
    updatedHashtags[index] = value
    updateField('hashtags', updatedHashtags)
  }

  const addHashtag = () => {
    updateField('hashtags', [...(editContent.hashtags || []), ''])
  }

  const removeHashtag = (index: number) => {
    const updatedHashtags = (editContent.hashtags || []).filter((_, i) => i !== index)
    updateField('hashtags', updatedHashtags)
  }

  return (
    <div className="space-y-6">
      {/* Subject */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Subject Line</label>
        <input
          type="text"
          value={editContent.subject || ''}
          onChange={(e) => updateField('subject', e.target.value)}
          className="w-full search-input"
        />
      </div>

      {/* Preheader */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Preheader</label>
        <input
          type="text"
          value={editContent.preheader || ''}
          onChange={(e) => updateField('preheader', e.target.value)}
          className="w-full search-input"
        />
      </div>

      {/* Body */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Email Body</label>
        <textarea
          value={editContent.body || ''}
          onChange={(e) => updateField('body', e.target.value)}
          rows={6}
          className="w-full search-input resize-none"
          placeholder="Enter main email content..."
        />
      </div>

      {/* Email Sections */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Email Sections</label>
        <div className="space-y-4">
          {(editContent.sections || []).map((section, index) => (
            <div key={index} className="border border-dark-quaternary rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <select
                  value={section.type}
                  onChange={(e) => updateSection(index, 'type', e.target.value)}
                  className="search-input"
                >
                  <option value="hero">Hero</option>
                  <option value="content">Content</option>
                  <option value="cta">Call to Action</option>
                </select>
                <button
                  onClick={() => removeSection(index)}
                  className="p-2 text-text-quaternary hover:text-error-500 hover:bg-error-500/10 rounded-lg transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              

              <input
                type="text"
                value={section.title || ''}
                onChange={(e) => updateSection(index, 'title', e.target.value)}
                className="w-full search-input"
                placeholder="Section title..."
              />

              <textarea
                value={section.content || ''}
                onChange={(e) => updateSection(index, 'content', e.target.value)}
                className="w-full search-input resize-none"
                rows={3}
                placeholder="Section content..."
              />

              {section.type === 'cta' && (
                <input
                  type="text"
                  value={section.buttonText || ''}
                  onChange={(e) => updateSection(index, 'buttonText', e.target.value)}
                  className="w-full search-input"
                  placeholder="Button text..."
                />
              )}
            </div>
          ))}
        </div>

        <button
          onClick={() => addSection('content')}
          className="w-full mt-4 p-3 border-2 border-dashed border-dark-quaternary rounded-lg text-text-tertiary hover:text-text-secondary hover:border-text-tertiary transition-colors"
        >
          <Plus className="w-4 h-4 inline mr-2" />
          Add Email Section
        </button>
      </div>

      {/* Footer */}
      <div>
        <label className="text-sm font-medium text-text-secondary block mb-3">Email Footer</label>
        <textarea
          value={editContent.footer || ''}
          onChange={(e) => updateField('footer', e.target.value)}
          rows={3}
          className="w-full search-input resize-none"
          placeholder="Unsubscribe links, company info, etc..."
        />
      </div>

      {/* Hashtags */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-text-secondary">Hashtags</label>
          <Button
            onClick={addHashtag}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Hashtag
          </Button>
        </div>
        
        <div className="space-y-2">
          {(editContent.hashtags || []).map((hashtag, index) => (
            <div key={index} className="flex items-center gap-2">
              <input
                type="text"
                value={hashtag}
                onChange={(e) => updateHashtag(index, e.target.value)}
                className="flex-1 px-3 py-2 bg-dark-secondary border border-dark-quaternary rounded text-text-primary placeholder-text-tertiary focus:outline-none focus:border-brand-500"
                placeholder="#hashtag"
              />
              <button
                onClick={() => removeHashtag(index)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default EmailEdit
