import React from 'react'
import { FileText, Phone, Mail, User } from 'lucide-react'
import { formatDate } from '@/utils/formatDate'
import MarkdownRenderer from '@/components/MarkdownRenderer'

interface BodySection {
  heading: string
  content: string
}

interface ContactInfo {
  name?: string
  email?: string
  phone?: string
}

interface PressReleaseContent {
  headline: string
  subheadline?: string
  dateline: string
  lead_paragraph: string
  body_sections: BodySection[]
  boilerplate: string
  contact_info: ContactInfo
  hashtags?: string[]
}

interface PressReleasePreviewProps {
  content: PressReleaseContent
  createdBy: {
    id: string
    name: string
    avatar: string
    role: string
  }
  scheduledDate?: string
  copyButton?: React.ReactNode
  copyButton?: React.ReactNode
}

const PressReleasePreview: React.FC<PressReleasePreviewProps> = ({
  content,
  createdBy,
  scheduledDate,
  copyButton
}) => {
  return (
    <div className="card">
      <div className="max-w-4xl mx-auto">
        {/* Press Release Header */}
        <div className="text-center mb-8 pb-6 border-b border-dark-quaternary">
          <div className="flex items-center justify-center gap-2 mb-4">
            <FileText className="w-6 h-6 text-brand-500" />
            <span className="text-xs text-text-tertiary uppercase tracking-wide">Press Release</span>
          </div>
          
          <h1 className="text-3xl font-bold text-text-primary mb-4">
            {content.headline}
          </h1>
          
          {content.subheadline && (
            <h2 className="text-xl text-text-secondary mb-4 italic">
              {content.subheadline}
            </h2>
          )}
          
          <div className="text-sm text-text-tertiary">
            {content.dateline}
          </div>
        </div>

        {/* Lead Paragraph */}
        <div className="mb-8">
          <div className="text-lg leading-relaxed text-text-primary font-medium">
            <MarkdownRenderer content={content.lead_paragraph} />
          </div>
        </div>

        {/* Body Sections */}
        {content.body_sections && content.body_sections.length > 0 && (
          <div className="space-y-6 mb-8">
            {content.body_sections.map((section, index) => (
              <div key={index}>
                <h3 className="text-xl font-semibold text-text-primary mb-4">
                  {section.heading}
                </h3>
                <div className="prose prose-invert max-w-none">
                  <MarkdownRenderer content={section.content} />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Boilerplate */}
        {content.boilerplate && (
          <div className="mb-8 p-4 bg-dark-secondary rounded-lg border-l-4 border-brand-500">
            <h4 className="text-sm font-semibold text-text-secondary mb-3 uppercase tracking-wide">
              About the Company
            </h4>
            <div className="text-sm text-text-tertiary">
              <MarkdownRenderer content={content.boilerplate} />
            </div>
          </div>
        )}

        {/* Contact Information */}
        {content.contact_info && (
          <div className="mb-8 p-4 bg-dark-secondary rounded-lg">
            <h4 className="text-sm font-semibold text-text-secondary mb-3 uppercase tracking-wide">
              Media Contact
            </h4>
            <div className="space-y-2">
              {content.contact_info.name && (
                <div className="flex items-center gap-2 text-sm text-text-primary">
                  <User className="w-4 h-4 text-text-tertiary" />
                  {content.contact_info.name}
                </div>
              )}
              {content.contact_info.email && (
                <div className="flex items-center gap-2 text-sm text-text-primary">
                  <Mail className="w-4 h-4 text-text-tertiary" />
                  <a href={`mailto:${content.contact_info.email}`} className="text-brand-500 hover:underline">
                    {content.contact_info.email}
                  </a>
                </div>
              )}
              {content.contact_info.phone && (
                <div className="flex items-center gap-2 text-sm text-text-primary">
                  <Phone className="w-4 h-4 text-text-tertiary" />
                  <a href={`tel:${content.contact_info.phone}`} className="text-brand-500 hover:underline">
                    {content.contact_info.phone}
                  </a>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Hashtags */}
        {content.hashtags && content.hashtags.length > 0 && (
          <div className="pt-6 border-t border-dark-quaternary">
            <div className="flex flex-wrap gap-2">
              {content.hashtags.map((tag, index) => (
                <span key={index} className="text-brand-500 hover:underline cursor-pointer text-sm">
                  {tag.startsWith('#') ? tag : `#${tag}`}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-8 pt-6 border-t border-dark-quaternary text-center">
          <div className="text-xs text-text-tertiary">
            ###
          </div>
        </div>
      </div>
    </div>
  )
}

export default PressReleasePreview
