import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface BackgroundProcess {
  id: string
  type: 'ai-regeneration' | 'content-generation' | 'other'
  status: 'pending' | 'in-progress' | 'completed' | 'failed'
  startTime: number
  endTime?: number
  data: {
    postId?: string
    campaignId?: string
    contentIndex?: number
    isQuickPost?: boolean
    quickPostId?: string
    instruction?: string
    brandData?: any
    originalContent?: any
  }
  result?: any
  error?: string
}

interface BackgroundProcessState {
  processes: BackgroundProcess[]
  
  // Actions
  startProcess: (process: Omit<BackgroundProcess, 'id' | 'startTime' | 'status'>) => string
  updateProcess: (id: string, updates: Partial<BackgroundProcess>) => void
  completeProcess: (id: string, result?: any) => void
  failProcess: (id: string, error: string) => void
  removeProcess: (id: string) => void
  getProcess: (id: string) => BackgroundProcess | undefined
  getProcessesByType: (type: BackgroundProcess['type']) => BackgroundProcess[]
  getActiveProcesses: () => BackgroundProcess[]
  clearCompletedProcesses: () => void
}

export const useBackgroundProcessStore = create<BackgroundProcessState>()(
  persist(
    (set, get) => ({
      processes: [],

      startProcess: (processData) => {
        const id = `${processData.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        const process: BackgroundProcess = {
          ...processData,
          id,
          status: 'pending',
          startTime: Date.now(),
        }

        set((state) => ({
          processes: [...state.processes, process]
        }))

        return id
      },

      updateProcess: (id, updates) => {
        set((state) => ({
          processes: state.processes.map((process) =>
            process.id === id ? { ...process, ...updates } : process
          )
        }))
      },

      completeProcess: (id, result) => {
        set((state) => ({
          processes: state.processes.map((process) =>
            process.id === id
              ? {
                  ...process,
                  status: 'completed' as const,
                  endTime: Date.now(),
                  result
                }
              : process
          )
        }))
      },

      failProcess: (id, error) => {
        set((state) => ({
          processes: state.processes.map((process) =>
            process.id === id
              ? {
                  ...process,
                  status: 'failed' as const,
                  endTime: Date.now(),
                  error
                }
              : process
          )
        }))
      },

      removeProcess: (id) => {
        set((state) => ({
          processes: state.processes.filter((process) => process.id !== id)
        }))
      },

      getProcess: (id) => {
        return get().processes.find((process) => process.id === id)
      },

      getProcessesByType: (type) => {
        return get().processes.filter((process) => process.type === type)
      },

      getActiveProcesses: () => {
        return get().processes.filter((process) => 
          process.status === 'pending' || process.status === 'in-progress'
        )
      },

      clearCompletedProcesses: () => {
        set((state) => ({
          processes: state.processes.filter((process) => 
            process.status !== 'completed' && process.status !== 'failed'
          )
        }))
      }
    }),
    {
      name: 'background-processes',
      // Only persist essential data, not the full state
      partialize: (state) => ({
        processes: state.processes.filter(p => 
          p.status === 'pending' || p.status === 'in-progress'
        )
      })
    }
  )
)
